# 🚀 VPS部署指南

## 📋 部署信息
- **VPS IP**: *************
- **服务端口**: 8080
- **管理后台**: http://*************:8080/admin
- **API地址**: http://*************:8080/api/v1

## 🔧 部署步骤

### 1. 连接到VPS服务器
```bash
ssh root@*************
```

### 2. 更新系统包
```bash
apt update && apt upgrade -y
```

### 3. 安装必要的软件
```bash
# 安装Python3和pip
apt install python3 python3-pip python3-venv -y

# 安装Git
apt install git -y

# 安装nginx（可选，用作反向代理）
apt install nginx -y

# 安装supervisor（可选，用于进程管理）
apt install supervisor -y
```

### 4. 创建应用目录
```bash
mkdir -p /opt/license-server
cd /opt/license-server
```

### 5. 上传服务器文件
将本地的 `license_server/` 目录下的所有文件上传到VPS的 `/opt/license-server/` 目录。

可以使用以下方法之一：

#### 方法1: 使用scp命令
```bash
# 在本地执行
scp -r license_server/* root@*************:/opt/license-server/
```

#### 方法2: 使用Git（推荐）
```bash
# 在VPS上执行
git clone <your-repository-url> .
# 或者如果已有仓库
git pull origin main
```

### 6. 设置环境配置
```bash
cd /opt/license-server

# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
nano .env
```

**重要配置项**：
```bash
FLASK_ENV=production
SECRET_KEY=your-very-secure-secret-key-here
JWT_SECRET_KEY=your-very-secure-jwt-key-here
HOST=0.0.0.0
PORT=8080
DEBUG=False
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your-secure-admin-password
```

### 7. 运行部署脚本
```bash
chmod +x deploy.sh
./deploy.sh
```

### 8. 验证部署
```bash
# 检查服务状态
systemctl status license-server

# 查看日志
journalctl -u license-server -f

# 测试API
curl http://localhost:8080/api/v1/ping
```

## 🔒 安全配置

### 1. 配置防火墙
```bash
# 安装ufw
apt install ufw -y

# 允许SSH
ufw allow ssh

# 允许HTTP和HTTPS
ufw allow 80
ufw allow 443

# 允许应用端口
ufw allow 8080

# 启用防火墙
ufw enable
```

### 2. 配置Nginx反向代理（推荐）
```bash
# 创建nginx配置
cat > /etc/nginx/sites-available/license-server << 'EOF'
server {
    listen 80;
    server_name *************;

    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
EOF

# 启用站点
ln -s /etc/nginx/sites-available/license-server /etc/nginx/sites-enabled/
rm /etc/nginx/sites-enabled/default

# 测试配置
nginx -t

# 重启nginx
systemctl restart nginx
systemctl enable nginx
```

### 3. SSL证书配置（可选）
```bash
# 安装certbot
apt install certbot python3-certbot-nginx -y

# 获取SSL证书（需要域名）
# certbot --nginx -d your-domain.com
```

## 📊 监控和维护

### 1. 查看服务状态
```bash
systemctl status license-server
```

### 2. 查看日志
```bash
# 实时日志
journalctl -u license-server -f

# 最近日志
journalctl -u license-server --since "1 hour ago"
```

### 3. 重启服务
```bash
systemctl restart license-server
```

### 4. 数据库备份
```bash
# 备份SQLite数据库
cp /opt/license-server/license_server_prod.db /opt/license-server/backup/license_server_$(date +%Y%m%d_%H%M%S).db
```

## 🧪 测试部署

### 1. API测试
```bash
# 测试ping
curl http://*************:8080/api/v1/ping

# 测试验证（会失败，因为许可证不存在）
curl -X POST http://*************:8080/api/v1/validate \
  -H "Content-Type: application/json" \
  -d '{"license_key": "TEST-1234-5678-ABCD"}'
```

### 2. 管理后台测试
访问: http://*************:8080/admin
- 用户名: admin
- 密码: 在.env文件中设置的密码

## 🔧 故障排除

### 1. 服务无法启动
```bash
# 查看详细错误
journalctl -u license-server -n 50

# 检查端口占用
netstat -tlnp | grep 8080

# 手动启动测试
cd /opt/license-server
source venv/bin/activate
python run.py
```

### 2. 数据库问题
```bash
# 重新初始化数据库
cd /opt/license-server
source venv/bin/activate
python init_db.py
```

### 3. 权限问题
```bash
# 修复文件权限
chown -R www-data:www-data /opt/license-server
chmod +x /opt/license-server/run.py
```

## 📝 部署清单

- [ ] VPS连接正常
- [ ] 系统包已更新
- [ ] Python3环境已安装
- [ ] 应用文件已上传
- [ ] 环境配置已设置
- [ ] 部署脚本已执行
- [ ] 服务状态正常
- [ ] API测试通过
- [ ] 管理后台可访问
- [ ] 防火墙已配置
- [ ] 监控已设置

## 🎯 下一步

1. **创建许可证**: 登录管理后台创建测试许可证
2. **客户端测试**: 使用客户端程序测试许可证验证
3. **性能优化**: 根据使用情况调整配置
4. **备份策略**: 设置定期数据库备份
5. **监控告警**: 配置服务监控和告警
