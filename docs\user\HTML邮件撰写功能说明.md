# HTML邮件撰写功能说明

## 🎉 功能概述

企业邮件管理系统现已支持完整的HTML邮件撰写功能，默认使用HTML格式，提供丰富的文本格式化选项。

## ✨ 主要特性

### 1. 默认HTML格式
- 邮件撰写对话框默认使用HTML格式
- 自动启用富文本编辑器
- 支持HTML和纯文本格式切换

### 2. 丰富的格式化工具
- **粗体** (Ctrl+B): 𝐁 按钮或快捷键
- **斜体** (Ctrl+I): 𝐼 按钮或快捷键  
- **下划线** (Ctrl+U): U̲ 按钮或快捷键
- **字体大小**: 8-32pt可选
- **字体颜色**: 🎨 颜色选择器

### 3. 邮件操作功能
- **撰写新邮件**: 工具栏"撰写"按钮
- **回复邮件**: 选中邮件后点击"回复"
- **转发邮件**: 选中邮件后点击"转发"
- **全部回复**: 右键菜单中的"全部回复"

### 4. 附件支持
- 📎 附件按钮添加文件
- 支持多个附件
- 显示文件大小信息
- 可预览和移除附件

## 🚀 使用方法

### 撰写新邮件
1. 点击工具栏的"撰写"按钮
2. 选择发件账户（如果有多个）
3. 填写收件人、主题等信息
4. 在内容区域输入邮件正文
5. 使用格式化工具美化文本
6. 添加附件（可选）
7. 点击"发送"按钮

### 格式化文本
1. 选中要格式化的文本
2. 点击相应的格式化按钮：
   - 𝐁: 粗体
   - 𝐼: 斜体
   - U̲: 下划线
   - 字体大小下拉框: 改变字体大小
   - 🎨: 改变字体颜色

### 回复邮件
1. 在邮件列表中选中要回复的邮件
2. 点击工具栏的"回复"按钮或右键选择"回复"
3. 系统自动填充：
   - 收件人为原发件人
   - 主题添加"Re:"前缀
   - 引用原邮件内容
4. 编辑回复内容并发送

### 转发邮件
1. 选中要转发的邮件
2. 点击"转发"按钮或右键选择"转发"
3. 系统自动填充：
   - 主题添加"Fwd:"前缀
   - 包含原邮件完整信息
4. 填写收件人并发送

## 🎨 HTML格式示例

### 基本格式化
```html
<p>这是<strong>粗体文本</strong>和<em>斜体文本</em>。</p>
<p><u>这是下划线文本</u>。</p>
```

### 颜色和字体
```html
<p style="color: red; font-size: 16px;">红色大字体文本</p>
<p style="color: blue;">蓝色文本</p>
```

### 列表
```html
<ul>
    <li>项目一</li>
    <li>项目二</li>
    <li>项目三</li>
</ul>
```

## 📋 快捷键

| 功能 | 快捷键 |
|------|--------|
| 发送邮件 | Ctrl+Enter |
| 粗体 | Ctrl+B |
| 斜体 | Ctrl+I |
| 下划线 | Ctrl+U |

## 🔧 技术特性

### 邮件格式支持
- **HTML格式**: 支持富文本、颜色、字体等
- **纯文本格式**: 简单文本，兼容性好
- **混合格式**: 同时包含HTML和纯文本版本

### SMTP发送
- 支持多种邮件服务商
- 自动检测SMTP配置
- 支持TLS/SSL加密
- OAuth2认证支持

### 附件处理
- 支持任意文件类型
- Base64编码传输
- 文件大小显示
- 批量附件管理

## 🛠️ 配置选项

### 邮件格式设置
- 默认格式: HTML
- 可切换为纯文本
- 保持用户选择

### 发送选项
- 优先级设置（高/普通/低）
- 阅读回执请求
- 保存到已发送文件夹

### 账户管理
- 多账户支持
- 账户切换
- 认证信息管理

## 📊 测试验证

所有功能已通过完整测试：

✅ **邮件撰写对话框测试**
- 默认HTML格式设置
- 富文本编辑器启用
- 界面组件完整性

✅ **HTML格式化功能测试**
- 粗体、斜体、下划线
- 字体大小和颜色
- HTML内容生成

✅ **邮件发送测试**
- SMTP配置自动检测
- 邮件数据验证
- 发送历史记录

✅ **附件处理测试**
- 文件添加和移除
- 大小计算
- 编码处理

✅ **回复转发测试**
- 内容格式化
- 主题处理
- 引用格式

## 🎯 使用建议

### 最佳实践
1. **使用HTML格式**创建美观的邮件
2. **适度使用格式化**，避免过度装饰
3. **测试发送**确保格式正确显示
4. **添加纯文本版本**提高兼容性

### 注意事项
1. HTML邮件可能被某些客户端过滤
2. 过多格式化可能影响加载速度
3. 附件大小建议控制在10MB以内
4. 重要邮件建议同时提供纯文本版本

## 🔮 未来扩展

### 计划功能
- 邮件模板系统
- 签名管理
- 定时发送
- 邮件追踪
- 更多格式化选项（表格、图片等）

### 技术改进
- 更好的HTML编辑器
- 实时协作编辑
- 邮件加密
- 高级搜索过滤

---

## 📞 技术支持

如有问题或建议，请联系开发团队。

**版本**: v2.0  
**更新日期**: 2025-08-03  
**兼容性**: Windows 10/11, Python 3.8+
