<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮件列表 - 邮件查看器</title>
    <link rel="stylesheet" href="outlook_styles.css">
    <style>
        .email-list-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
        }
        .list-header {
            background: #0078d4;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .list-stats {
            background: #f3f2f1;
            padding: 16px 20px;
            border-bottom: 1px solid #edebe9;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .email-item {
            border-bottom: 1px solid #edebe9;
            padding: 16px 20px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .email-item:hover {
            background: #f8f8f8;
        }
        .email-item.unread {
            border-left: 4px solid #0078d4;
            background: #f9f9f9;
        }
        .email-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        .sender-name {
            font-weight: 600;
            color: #323130;
        }
        .email-date {
            color: #605e5c;
            font-size: 13px;
        }
        .email-subject {
            font-weight: 500;
            color: #323130;
            margin-bottom: 6px;
            font-size: 15px;
        }
        .email-preview {
            color: #605e5c;
            font-size: 13px;
            line-height: 1.4;
            margin-bottom: 8px;
        }
        .email-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .email-size {
            color: #605e5c;
            font-size: 12px;
        }
        .status-indicators {
            display: flex;
            gap: 6px;
        }
        .status-badge {
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="email-list-container">
        <div class="list-header">
            <h1>📧 邮件列表</h1>
            <p>共 2 封邮件</p>
        </div>
        
        <div class="list-stats">
            <div class="stats-left">
                <strong>邮件总数:</strong> 2
            </div>
            <div class="stats-right">
                <span>生成时间: 2025-08-03 03:55:54</span>
            </div>
        </div>
        
        <div class="email-list">
            
                <div class="email-item " onclick="openEmail('email_001_20250803_035554_欢迎使用你的新 Outlook.com 帐户.html')">
                    <div class="email-item-header">
                        <div class="sender-name">Outlook 团队</div>
                        <div class="email-date">03/14</div>
                    </div>
                    <div class="email-subject">欢迎使用你的新 Outlook.com 帐户</div>
                    <div class="email-preview">@font-face { font-family: &quot;wf_segoe-ui_normal&quot;; src: local(&quot;Segoe UI&quot;), local(&quot;Segoe WP&quot;), url(&#x27;http...</div>
                    <div class="email-meta">
                        <span class="email-size">999.2 KB</span>
                        <div class="status-indicators"></div>
                    </div>
                </div>
                
                <div class="email-item " onclick="openEmail('email_002_20250803_035554_连接到 Microsoft 帐户的新应用.html')">
                    <div class="email-item-header">
                        <div class="sender-name">Microsoft 帐户团队</div>
                        <div class="email-date">03/14</div>
                    </div>
                    <div class="email-subject">连接到 Microsoft 帐户的新应用</div>
                    <div class="email-preview">Thunderbird 已连接到 Microsoft 帐户 cs**<EMAIL>。 如果未授予此访问权限，请从帐户中删除应用。 管理应用 https://account.live.com...</div>
                    <div class="email-meta">
                        <span class="email-size">62.7 KB</span>
                        <div class="status-indicators"></div>
                    </div>
                </div>
                
        </div>
    </div>
    
    <script>
        function openEmail(filename) {
            window.open(filename, '_blank');
        }
    </script>
</body>
</html>