#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生产环境监控和告警系统
实时监控多账户邮箱管理系统的性能和健康状态
"""

import time
import json
import logging
import threading
from typing import Dict, List, Any, Callable, Optional
from dataclasses import dataclass, asdict
from enum import Enum
from datetime import datetime, timedelta
import statistics

class AlertLevel(Enum):
    """告警级别"""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"

class MetricType(Enum):
    """指标类型"""
    PERFORMANCE = "performance"
    RELIABILITY = "reliability"
    RESOURCE = "resource"
    BUSINESS = "business"

@dataclass
class MetricThreshold:
    """指标阈值"""
    name: str
    warning_threshold: float
    critical_threshold: float
    comparison: str  # 'gt', 'lt', 'eq'
    metric_type: MetricType
    description: str

@dataclass
class Alert:
    """告警信息"""
    timestamp: datetime
    level: AlertLevel
    metric_name: str
    current_value: float
    threshold_value: float
    message: str
    resolved: bool = False
    resolution_time: Optional[datetime] = None

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, retention_hours: int = 24):
        self.retention_hours = retention_hours
        self.metrics_history: Dict[str, List[Tuple[datetime, float]]] = {}
        self.current_metrics: Dict[str, float] = {}
        self.alerts: List[Alert] = []
        self.alert_callbacks: List[Callable[[Alert], None]] = []
        
        # 线程安全
        self.lock = threading.RLock()
        
        # 配置日志
        self.logger = logging.getLogger(__name__)
        
        # 定义监控阈值
        self.thresholds = {
            'token_acquisition_time': MetricThreshold(
                name='token_acquisition_time',
                warning_threshold=5.0,
                critical_threshold=10.0,
                comparison='gt',
                metric_type=MetricType.PERFORMANCE,
                description='OAuth2令牌获取时间'
            ),
            'authentication_time': MetricThreshold(
                name='authentication_time',
                warning_threshold=2.0,
                critical_threshold=5.0,
                comparison='gt',
                metric_type=MetricType.PERFORMANCE,
                description='IMAP认证时间'
            ),
            'success_rate': MetricThreshold(
                name='success_rate',
                warning_threshold=95.0,
                critical_threshold=90.0,
                comparison='lt',
                metric_type=MetricType.RELIABILITY,
                description='认证成功率'
            ),
            'memory_usage_mb': MetricThreshold(
                name='memory_usage_mb',
                warning_threshold=500.0,
                critical_threshold=1000.0,
                comparison='gt',
                metric_type=MetricType.RESOURCE,
                description='内存使用量(MB)'
            ),
            'concurrent_connections': MetricThreshold(
                name='concurrent_connections',
                warning_threshold=100.0,
                critical_threshold=200.0,
                comparison='gt',
                metric_type=MetricType.RESOURCE,
                description='并发连接数'
            ),
            'error_rate': MetricThreshold(
                name='error_rate',
                warning_threshold=5.0,
                critical_threshold=10.0,
                comparison='gt',
                metric_type=MetricType.RELIABILITY,
                description='错误率(%)'
            )
        }
    
    def record_metric(self, name: str, value: float):
        """记录指标"""
        with self.lock:
            current_time = datetime.now()
            
            # 更新当前指标
            self.current_metrics[name] = value
            
            # 添加到历史记录
            if name not in self.metrics_history:
                self.metrics_history[name] = []
            
            self.metrics_history[name].append((current_time, value))
            
            # 清理过期数据
            self._cleanup_old_metrics(name, current_time)
            
            # 检查阈值
            self._check_thresholds(name, value, current_time)
    
    def _cleanup_old_metrics(self, metric_name: str, current_time: datetime):
        """清理过期指标数据"""
        cutoff_time = current_time - timedelta(hours=self.retention_hours)
        
        if metric_name in self.metrics_history:
            self.metrics_history[metric_name] = [
                (timestamp, value) for timestamp, value in self.metrics_history[metric_name]
                if timestamp > cutoff_time
            ]
    
    def _check_thresholds(self, metric_name: str, value: float, timestamp: datetime):
        """检查指标阈值"""
        if metric_name not in self.thresholds:
            return
        
        threshold = self.thresholds[metric_name]
        alert_level = None
        threshold_value = None
        
        # 检查阈值
        if threshold.comparison == 'gt':
            if value > threshold.critical_threshold:
                alert_level = AlertLevel.CRITICAL
                threshold_value = threshold.critical_threshold
            elif value > threshold.warning_threshold:
                alert_level = AlertLevel.WARNING
                threshold_value = threshold.warning_threshold
        elif threshold.comparison == 'lt':
            if value < threshold.critical_threshold:
                alert_level = AlertLevel.CRITICAL
                threshold_value = threshold.critical_threshold
            elif value < threshold.warning_threshold:
                alert_level = AlertLevel.WARNING
                threshold_value = threshold.warning_threshold
        
        # 生成告警
        if alert_level:
            alert = Alert(
                timestamp=timestamp,
                level=alert_level,
                metric_name=metric_name,
                current_value=value,
                threshold_value=threshold_value,
                message=f"{threshold.description}: {value} (阈值: {threshold_value})"
            )
            
            self._trigger_alert(alert)
    
    def _trigger_alert(self, alert: Alert):
        """触发告警"""
        with self.lock:
            self.alerts.append(alert)
            
            # 记录日志
            log_level = logging.CRITICAL if alert.level == AlertLevel.CRITICAL else logging.WARNING
            self.logger.log(log_level, f"告警: {alert.message}")
            
            # 调用告警回调
            for callback in self.alert_callbacks:
                try:
                    callback(alert)
                except Exception as e:
                    self.logger.error(f"告警回调执行失败: {e}")
    
    def add_alert_callback(self, callback: Callable[[Alert], None]):
        """添加告警回调"""
        self.alert_callbacks.append(callback)
    
    def get_metric_statistics(self, metric_name: str, hours: int = 1) -> Dict[str, float]:
        """获取指标统计信息"""
        with self.lock:
            if metric_name not in self.metrics_history:
                return {}
            
            cutoff_time = datetime.now() - timedelta(hours=hours)
            recent_values = [
                value for timestamp, value in self.metrics_history[metric_name]
                if timestamp > cutoff_time
            ]
            
            if not recent_values:
                return {}
            
            return {
                'count': len(recent_values),
                'min': min(recent_values),
                'max': max(recent_values),
                'avg': statistics.mean(recent_values),
                'median': statistics.median(recent_values),
                'std_dev': statistics.stdev(recent_values) if len(recent_values) > 1 else 0
            }
    
    def get_current_status(self) -> Dict[str, Any]:
        """获取当前状态"""
        with self.lock:
            # 统计告警
            active_alerts = [alert for alert in self.alerts if not alert.resolved]
            critical_alerts = [alert for alert in active_alerts if alert.level == AlertLevel.CRITICAL]
            warning_alerts = [alert for alert in active_alerts if alert.level == AlertLevel.WARNING]
            
            # 计算健康分数
            health_score = self._calculate_health_score()
            
            return {
                'timestamp': datetime.now().isoformat(),
                'health_score': health_score,
                'status': self._get_overall_status(health_score),
                'current_metrics': self.current_metrics.copy(),
                'alerts': {
                    'total_active': len(active_alerts),
                    'critical': len(critical_alerts),
                    'warning': len(warning_alerts)
                },
                'recent_alerts': [
                    {
                        'timestamp': alert.timestamp.isoformat(),
                        'level': alert.level.value,
                        'message': alert.message,
                        'resolved': alert.resolved
                    }
                    for alert in self.alerts[-10:]  # 最近10个告警
                ]
            }
    
    def _calculate_health_score(self) -> float:
        """计算健康分数 (0-100)"""
        if not self.current_metrics:
            return 100.0
        
        score = 100.0
        
        # 检查每个指标的健康状况
        for metric_name, value in self.current_metrics.items():
            if metric_name in self.thresholds:
                threshold = self.thresholds[metric_name]
                
                if threshold.comparison == 'gt':
                    if value > threshold.critical_threshold:
                        score -= 30
                    elif value > threshold.warning_threshold:
                        score -= 10
                elif threshold.comparison == 'lt':
                    if value < threshold.critical_threshold:
                        score -= 30
                    elif value < threshold.warning_threshold:
                        score -= 10
        
        return max(0.0, score)
    
    def _get_overall_status(self, health_score: float) -> str:
        """获取整体状态"""
        if health_score >= 90:
            return "healthy"
        elif health_score >= 70:
            return "warning"
        else:
            return "critical"
    
    def export_metrics(self, filename: str = None):
        """导出指标数据"""
        if filename is None:
            filename = f"metrics_export_{int(time.time())}.json"
        
        with self.lock:
            export_data = {
                'export_timestamp': datetime.now().isoformat(),
                'current_metrics': self.current_metrics,
                'metrics_history': {
                    name: [
                        {'timestamp': ts.isoformat(), 'value': value}
                        for ts, value in history
                    ]
                    for name, history in self.metrics_history.items()
                },
                'alerts': [
                    {
                        'timestamp': alert.timestamp.isoformat(),
                        'level': alert.level.value,
                        'metric_name': alert.metric_name,
                        'current_value': alert.current_value,
                        'threshold_value': alert.threshold_value,
                        'message': alert.message,
                        'resolved': alert.resolved,
                        'resolution_time': alert.resolution_time.isoformat() if alert.resolution_time else None
                    }
                    for alert in self.alerts
                ],
                'thresholds': {
                    name: asdict(threshold) for name, threshold in self.thresholds.items()
                }
            }
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"指标数据已导出到: {filename}")
            
        except Exception as e:
            self.logger.error(f"导出指标数据失败: {e}")

def email_alert_callback(alert: Alert):
    """邮件告警回调示例"""
    print(f"📧 邮件告警: [{alert.level.value.upper()}] {alert.message}")

def webhook_alert_callback(alert: Alert):
    """Webhook告警回调示例"""
    print(f"🔗 Webhook告警: [{alert.level.value.upper()}] {alert.message}")

def main():
    """主函数"""
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    print("📊 生产环境监控系统测试")
    print("=" * 50)
    
    # 创建监控器
    monitor = PerformanceMonitor()
    
    # 添加告警回调
    monitor.add_alert_callback(email_alert_callback)
    monitor.add_alert_callback(webhook_alert_callback)
    
    # 模拟指标数据
    print("\n📈 模拟性能指标...")
    
    # 正常指标
    monitor.record_metric('token_acquisition_time', 1.64)
    monitor.record_metric('authentication_time', 0.71)
    monitor.record_metric('success_rate', 100.0)
    monitor.record_metric('memory_usage_mb', 150.0)
    monitor.record_metric('concurrent_connections', 25.0)
    monitor.record_metric('error_rate', 0.0)
    
    # 模拟一些告警情况
    print("\n⚠️ 模拟告警情况...")
    monitor.record_metric('token_acquisition_time', 6.0)  # 触发警告
    monitor.record_metric('authentication_time', 12.0)    # 触发严重告警
    monitor.record_metric('success_rate', 85.0)           # 触发严重告警
    
    # 获取当前状态
    print("\n📊 当前系统状态:")
    status = monitor.get_current_status()
    
    print(f"   健康分数: {status['health_score']:.1f}")
    print(f"   整体状态: {status['status']}")
    print(f"   活跃告警: {status['alerts']['total_active']} (严重: {status['alerts']['critical']}, 警告: {status['alerts']['warning']})")
    
    print(f"\n📋 当前指标:")
    for metric, value in status['current_metrics'].items():
        print(f"   {metric}: {value}")
    
    print(f"\n🚨 最近告警:")
    for alert in status['recent_alerts'][-5:]:  # 显示最近5个告警
        print(f"   [{alert['level'].upper()}] {alert['message']}")
    
    # 获取统计信息
    print(f"\n📈 指标统计 (最近1小时):")
    for metric_name in ['token_acquisition_time', 'authentication_time', 'success_rate']:
        stats = monitor.get_metric_statistics(metric_name, hours=1)
        if stats:
            print(f"   {metric_name}: 平均 {stats['avg']:.2f}, 最大 {stats['max']:.2f}, 最小 {stats['min']:.2f}")
    
    # 导出指标
    monitor.export_metrics()

if __name__ == "__main__":
    main()
