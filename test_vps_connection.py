#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VPS服务器连接测试脚本
测试客户端与VPS许可证服务器的连接
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMessageBox
from auth.license_manager import LicenseManager
from auth.license_dialog import LicenseDialog


def test_server_connection():
    """测试服务器连接"""
    print("🔗 测试VPS服务器连接...")
    
    try:
        # 创建许可证管理器（使用VPS地址）
        manager = LicenseManager()
        
        print(f"📡 连接地址: {manager.validator.api_base_url}")
        
        # 测试连接
        success, message = manager.test_connection()
        
        if success:
            print(f"✅ 连接成功: {message}")
            return True
        else:
            print(f"❌ 连接失败: {message}")
            return False
            
    except Exception as e:
        print(f"❌ 连接测试出错: {e}")
        return False


def test_license_validation():
    """测试密钥验证（简化版）"""
    print("\n🔍 测试密钥验证...")
    
    try:
        manager = LicenseManager()
        
        # 测试无效密钥
        print("📝 测试无效密钥...")
        success, message, data = manager.validate_license_key("INVALID-TEST-KEY")
        print(f"   结果: {message}")

        # 测试格式正确但不存在的密钥
        print("📝 测试不存在的密钥...")
        success, message, data = manager.validate_license_key("TEST-1234-5678-ABCD")
        print(f"   结果: {message}")
        
        return True
        
    except Exception as e:
        print(f"❌ 密钥验证测试出错: {e}")
        return False


def test_license_dialog():
    """测试密钥验证对话框"""
    print("\n🖥️  测试密钥验证对话框...")
    
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建许可证管理器
        manager = LicenseManager()
        
        # 创建对话框
        dialog = LicenseDialog(None, manager)
        
        print("✅ 密钥验证对话框创建成功")
        print("📋 选择对话框类型:")
        print("   1. 经典版密钥验证对话框")
        print("   2. 🌟 现代化密钥验证对话框 (推荐)")

        choice = input("请选择对话框类型 (1/2，默认2): ").strip() or "2"

        if choice == "2":
            print("🚀 启动现代化许可证对话框...")
            print("   ✨ 特色功能:")
            print("   • 渐变背景和玻璃效果")
            print("   • 自动格式化许可证输入 (XXXX-XXXX-XXXX-XXXX)")
            print("   • 实时连接状态显示")
            print("   • 流畅的动画效果")
            print("   • 可拖动窗口")
            print("   • 测试许可证: GEAY-AMUY-8QBQ-D1Q5")

            from auth.modern_license_dialog import ModernLicenseDialog
            dialog = ModernLicenseDialog(None, manager)
        else:
            print("📋 启动经典版许可证对话框...")
            print("   测试说明:")
            print("   • 点击'测试连接'按钮 - 应该显示连接成功")
            print("   • 输入测试许可证密钥（如：GEAY-AMUY-8QBQ-D1Q5）")
            print("   • 点击'验证许可证'按钮")
            print("   • 查看'状态信息'标签页")

        # 显示对话框
        result = dialog.exec()
        
        if result == dialog.DialogCode.Accepted:
            print("✅ 用户完成了许可证验证")
            if manager.is_licensed():
                print("🎉 许可证验证成功！")
                license_info = manager.get_current_license_info()
                if license_info:
                    print(f"   密钥状态: {license_info['status']}")
            else:
                print("ℹ️  密钥验证未通过")
        else:
            print("ℹ️  用户取消了对话框")
        
        return True
        
    except Exception as e:
        print(f"❌ 对话框测试出错: {e}")
        return False


def test_main_program_integration():
    """测试主程序集成"""
    print("\n🚀 测试主程序集成...")
    
    try:
        # 模拟主程序启动时的许可证检查
        manager = LicenseManager()
        
        print("📋 模拟主程序启动流程:")
        print("   1. 检查是否已有有效许可证...")
        
        if manager.is_licensed():
            print("   ✅ 发现有效许可证")
            license_info = manager.get_current_license_info()
            if license_info:
                print(f"      状态: {license_info['status']}")
                print(f"      到期时间: {license_info.get('expiry_time', '未知')}")
        else:
            print("   ⚠️  未发现有效许可证，需要验证")
            print("   📝 在实际程序中，此时会显示密钥验证对话框")
        
        return True
        
    except Exception as e:
        print(f"❌ 主程序集成测试出错: {e}")
        return False


def main():
    """主函数"""
    print("🧪 VPS许可证服务器连接测试")
    print("=" * 50)
    print("🎯 测试目标: https://ka.915277.xyz/api/v1")
    print("")
    
    test_results = []
    
    # 运行测试
    test_results.append(("服务器连接", test_server_connection()))
    test_results.append(("密钥验证", test_license_validation()))
    test_results.append(("主程序集成", test_main_program_integration()))

    # 询问是否进行界面测试
    print("\n" + "=" * 50)
    choice = input("是否进行密钥验证对话框界面测试？(y/n): ").lower().strip()

    if choice in ['y', 'yes', '是']:
        test_results.append(("密钥验证对话框", test_license_dialog()))
    
    # 显示测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！客户端与VPS服务器连接正常。")
        print("\n📋 下一步建议:")
        print("   1. 登录VPS管理后台创建有效密钥")
        print("   2. 使用创建的密钥测试完整验证流程")
        print("   3. 测试主程序的完整启动流程")
    else:
        print("⚠️  部分测试失败，请检查:")
        print("   1. VPS服务器是否正常运行")
        print("   2. 防火墙是否允许8080端口")
        print("   3. 网络连接是否正常")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    print(f"\n🏁 测试完成: {'成功' if success else '失败'}")
    sys.exit(0 if success else 1)
