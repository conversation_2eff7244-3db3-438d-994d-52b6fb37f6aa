#!/bin/bash
# 快速部署脚本 - 将许可证服务器部署到VPS

set -e

# 配置变量
VPS_IP="*************"
VPS_USER="root"
DEPLOY_PATH="/opt/license-server"
LOCAL_SERVER_PATH="./license_server"

echo "🚀 开始部署许可证服务器到VPS: $VPS_IP"

# 检查本地文件
if [ ! -d "$LOCAL_SERVER_PATH" ]; then
    echo "❌ 错误: 找不到本地服务器文件目录 $LOCAL_SERVER_PATH"
    exit 1
fi

echo "📁 检查本地文件..."
echo "   服务器文件目录: $LOCAL_SERVER_PATH"
echo "   目标VPS: $VPS_IP"
echo "   部署路径: $DEPLOY_PATH"

# 确认部署
read -p "确认部署到生产服务器？(y/N): " confirm
if [[ $confirm != [yY] ]]; then
    echo "❌ 部署已取消"
    exit 0
fi

echo "🔗 连接到VPS服务器..."

# 创建部署目录
ssh $VPS_USER@$VPS_IP "mkdir -p $DEPLOY_PATH"

echo "📤 上传服务器文件..."
# 上传所有服务器文件
rsync -avz --progress $LOCAL_SERVER_PATH/ $VPS_USER@$VPS_IP:$DEPLOY_PATH/

echo "🔧 在VPS上执行部署..."
ssh $VPS_USER@$VPS_IP << 'ENDSSH'
set -e

cd /opt/license-server

echo "📦 更新系统包..."
apt update

echo "🐍 安装Python3和相关工具..."
apt install -y python3 python3-pip python3-venv git nginx supervisor

echo "🔧 设置环境配置..."
if [ ! -f ".env" ]; then
    cp .env.example .env
    echo "⚠️  请编辑 .env 文件设置正确的配置"
fi

echo "🏗️ 创建Python虚拟环境..."
python3 -m venv venv
source venv/bin/activate

echo "📚 安装Python依赖..."
pip install --upgrade pip
pip install -r requirements.txt

echo "🗄️ 初始化数据库..."
python init_db.py << 'ENDPYTHON'
1
n
4
ENDPYTHON

echo "🔒 设置文件权限..."
chown -R www-data:www-data .
chmod +x run.py
chmod +x deploy.sh

echo "🔧 创建systemd服务..."
cat > /etc/systemd/system/license-server.service << 'EOF'
[Unit]
Description=License Server
After=network.target

[Service]
Type=notify
User=www-data
Group=www-data
WorkingDirectory=/opt/license-server
Environment=PATH=/opt/license-server/venv/bin
ExecStart=/opt/license-server/venv/bin/gunicorn --config gunicorn.conf.py app:create_app()
ExecReload=/bin/kill -s HUP $MAINPID
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

echo "🔄 重新加载systemd..."
systemctl daemon-reload
systemctl enable license-server

echo "🚀 启动许可证服务器..."
systemctl start license-server

echo "📊 检查服务状态..."
systemctl status license-server --no-pager

echo "🔥 配置防火墙..."
if command -v ufw &> /dev/null; then
    ufw allow 8080/tcp
    echo "已允许8080端口通过防火墙"
fi

echo "🌐 配置Nginx反向代理..."
cat > /etc/nginx/sites-available/license-server << 'EOF'
server {
    listen 80;
    server_name *************;

    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
EOF

# 启用站点
ln -sf /etc/nginx/sites-available/license-server /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default

# 测试nginx配置
nginx -t

# 重启nginx
systemctl restart nginx
systemctl enable nginx

echo ""
echo "🎉 许可证服务器部署完成！"
echo ""
echo "📋 服务信息:"
echo "   HTTP地址: http://*************"
echo "   直接访问: http://*************:8080"
echo "   管理后台: http://*************/admin"
echo "   API地址: http://*************/api/v1"
echo ""
echo "🔧 管理命令:"
echo "   查看状态: systemctl status license-server"
echo "   查看日志: journalctl -u license-server -f"
echo "   重启服务: systemctl restart license-server"
echo ""
echo "⚠️  重要提醒:"
echo "   1. 请编辑 /opt/license-server/.env 文件设置安全的密码和密钥"
echo "   2. 默认管理员账户: admin / admin123 (请立即修改)"
echo "   3. 建议配置SSL证书以启用HTTPS"
echo ""

ENDSSH

echo ""
echo "🧪 测试部署结果..."

# 测试API连接
echo "📡 测试API连接..."
if curl -s http://$VPS_IP/api/v1/ping > /dev/null; then
    echo "✅ API连接测试成功"
else
    echo "❌ API连接测试失败"
fi

# 测试管理后台
echo "🖥️  测试管理后台..."
if curl -s http://$VPS_IP/admin > /dev/null; then
    echo "✅ 管理后台访问成功"
else
    echo "❌ 管理后台访问失败"
fi

echo ""
echo "🎯 部署完成！"
echo ""
echo "📋 下一步操作:"
echo "   1. 访问 http://$VPS_IP/admin 登录管理后台"
echo "   2. 使用默认账户 admin/admin123 登录（请立即修改密码）"
echo "   3. 创建测试许可证"
echo "   4. 使用客户端程序测试许可证验证"
echo ""
echo "🔧 如需修改配置:"
echo "   ssh $VPS_USER@$VPS_IP"
echo "   cd $DEPLOY_PATH"
echo "   nano .env"
echo "   systemctl restart license-server"
echo ""
