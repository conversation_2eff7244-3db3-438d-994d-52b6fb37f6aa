#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
账户管理对话框
用于添加和编辑邮件账户的专业对话框
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QGridLayout,
    QLabel, QLineEdit, QPushButton, QCheckBox, QSpinBox, QComboBox,
    QTextEdit, QGroupBox, QTabWidget, QWidget, QMessageBox
)
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.multi_account_manager import AccountConfig

class AccountDialog(QDialog):
    """账户配置对话框"""
    
    def __init__(self, parent=None, account_config=None):
        super().__init__(parent)
        
        self.account_config = account_config
        self.is_edit_mode = account_config is not None
        
        self.setWindowTitle("编辑账户" if self.is_edit_mode else "添加账户")
        self.setModal(True)
        self.resize(500, 400)
        
        self.setup_ui()
        
        if self.is_edit_mode:
            self.load_account_data()
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 创建选项卡
        tab_widget = QTabWidget()
        
        # 基本信息选项卡
        basic_tab = self.create_basic_tab()
        tab_widget.addTab(basic_tab, "基本信息")
        
        # 高级设置选项卡
        advanced_tab = self.create_advanced_tab()
        tab_widget.addTab(advanced_tab, "高级设置")
        
        layout.addWidget(tab_widget)
        
        # 按钮区域
        button_layout = self.create_button_layout()
        layout.addLayout(button_layout)
        
        # 设置样式
        self.setStyleSheet("""
            QDialog {
                background-color: #f0f0f0;
                font-family: "Microsoft YaHei", "Segoe UI", Arial, sans-serif;
                font-size: 9pt;
            }
            
            QGroupBox {
                font-weight: bold;
                border: 1px solid #c0c0c0;
                border-radius: 4px;
                margin-top: 8px;
                padding-top: 8px;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 4px 0 4px;
            }
            
            QLineEdit, QTextEdit, QComboBox, QSpinBox {
                background-color: #ffffff;
                border: 1px solid #c0c0c0;
                border-radius: 2px;
                padding: 4px;
                font-size: 9pt;
            }
            
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus, QSpinBox:focus {
                border-color: #0078d4;
            }
            
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f0f0f0);
                border: 1px solid #c0c0c0;
                border-radius: 3px;
                padding: 6px 12px;
                font-size: 9pt;
                min-width: 80px;
            }
            
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e8f4fd, stop:1 #d0e7f8);
                border-color: #0078d4;
            }
            
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #d0e7f8, stop:1 #b8daf2);
            }
        """)
    
    def create_basic_tab(self):
        """创建基本信息选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 账户信息组
        account_group = QGroupBox("账户信息")
        account_layout = QFormLayout(account_group)
        
        self.email_edit = QLineEdit()
        self.email_edit.setPlaceholderText("<EMAIL>")
        account_layout.addRow("邮箱地址*:", self.email_edit)
        
        self.account_id_edit = QLineEdit()
        self.account_id_edit.setPlaceholderText("自动生成或手动输入")
        account_layout.addRow("账户ID:", self.account_id_edit)
        
        layout.addWidget(account_group)
        
        # 认证信息组
        auth_group = QGroupBox("认证信息")
        auth_layout = QFormLayout(auth_group)
        
        self.client_id_edit = QLineEdit()
        self.client_id_edit.setText("9e5f94bc-e8a4-4e73-b8be-63364c29d753")
        auth_layout.addRow("Client ID*:", self.client_id_edit)
        
        self.refresh_token_edit = QTextEdit()
        self.refresh_token_edit.setMaximumHeight(80)
        self.refresh_token_edit.setPlaceholderText("输入Microsoft Graph API的Refresh Token")
        auth_layout.addRow("Refresh Token*:", self.refresh_token_edit)
        
        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setPlaceholderText("可选，用于某些认证场景")
        auth_layout.addRow("密码:", self.password_edit)
        
        layout.addWidget(auth_group)
        
        # 状态组
        status_group = QGroupBox("状态设置")
        status_layout = QFormLayout(status_group)
        
        self.enabled_checkbox = QCheckBox("启用此账户")
        self.enabled_checkbox.setChecked(True)
        status_layout.addRow("", self.enabled_checkbox)
        
        layout.addWidget(status_group)
        
        layout.addStretch()
        return widget
    
    def create_advanced_tab(self):
        """创建高级设置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 连接设置组
        connection_group = QGroupBox("连接设置")
        connection_layout = QFormLayout(connection_group)
        
        self.priority_spinbox = QSpinBox()
        self.priority_spinbox.setRange(1, 10)
        self.priority_spinbox.setValue(1)
        connection_layout.addRow("优先级:", self.priority_spinbox)
        
        self.max_retries_spinbox = QSpinBox()
        self.max_retries_spinbox.setRange(1, 10)
        self.max_retries_spinbox.setValue(3)
        connection_layout.addRow("最大重试次数:", self.max_retries_spinbox)
        
        layout.addWidget(connection_group)
        
        # 服务器设置组
        server_group = QGroupBox("服务器设置")
        server_layout = QFormLayout(server_group)
        
        self.imap_server_edit = QLineEdit()
        self.imap_server_edit.setText("outlook.office365.com")
        self.imap_server_edit.setReadOnly(True)
        server_layout.addRow("IMAP服务器:", self.imap_server_edit)
        
        self.imap_port_spinbox = QSpinBox()
        self.imap_port_spinbox.setRange(1, 65535)
        self.imap_port_spinbox.setValue(993)
        self.imap_port_spinbox.setReadOnly(True)
        server_layout.addRow("IMAP端口:", self.imap_port_spinbox)
        
        layout.addWidget(server_group)
        
        # 备注组
        notes_group = QGroupBox("备注")
        notes_layout = QVBoxLayout(notes_group)
        
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        self.notes_edit.setPlaceholderText("可选的账户备注信息...")
        notes_layout.addWidget(self.notes_edit)
        
        layout.addWidget(notes_group)
        
        layout.addStretch()
        return widget
    
    def create_button_layout(self):
        """创建按钮布局"""
        layout = QHBoxLayout()
        
        # 测试连接按钮
        test_btn = QPushButton("测试连接")
        test_btn.clicked.connect(self.test_connection)
        layout.addWidget(test_btn)
        
        layout.addStretch()
        
        # 确定和取消按钮
        ok_btn = QPushButton("确定")
        ok_btn.clicked.connect(self.accept_dialog)
        layout.addWidget(ok_btn)
        
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        layout.addWidget(cancel_btn)
        
        return layout
    
    def load_account_data(self):
        """加载账户数据"""
        if not self.account_config:
            return
        
        self.email_edit.setText(self.account_config.email)
        self.account_id_edit.setText(self.account_config.account_id)
        self.client_id_edit.setText(self.account_config.client_id)
        self.refresh_token_edit.setPlainText(self.account_config.refresh_token)
        self.password_edit.setText(self.account_config.password or "")
        self.enabled_checkbox.setChecked(self.account_config.enabled)
        self.priority_spinbox.setValue(self.account_config.priority)
        self.max_retries_spinbox.setValue(self.account_config.max_retries)
    
    def validate_input(self):
        """验证输入"""
        if not self.email_edit.text().strip():
            QMessageBox.warning(self, "验证失败", "请输入邮箱地址")
            self.email_edit.setFocus()
            return False
        
        if "@" not in self.email_edit.text():
            QMessageBox.warning(self, "验证失败", "邮箱地址格式不正确")
            self.email_edit.setFocus()
            return False
        
        if not self.client_id_edit.text().strip():
            QMessageBox.warning(self, "验证失败", "请输入Client ID")
            self.client_id_edit.setFocus()
            return False
        
        if not self.refresh_token_edit.toPlainText().strip():
            QMessageBox.warning(self, "验证失败", "请输入Refresh Token")
            self.refresh_token_edit.setFocus()
            return False
        
        return True
    
    def test_connection(self):
        """测试连接"""
        if not self.validate_input():
            return
        
        try:
            # 这里可以添加实际的连接测试逻辑
            QMessageBox.information(self, "测试连接", "连接测试功能开发中...")
            
        except Exception as e:
            QMessageBox.warning(self, "连接测试失败", f"连接测试失败: {e}")
    
    def accept_dialog(self):
        """接受对话框"""
        if not self.validate_input():
            return
        
        self.accept()
    
    def get_account_config(self):
        """获取账户配置"""
        account_id = self.account_id_edit.text().strip()
        if not account_id:
            # 如果没有输入账户ID，自动生成
            email = self.email_edit.text().strip()
            account_id = f"account_{email.split('@')[0]}"
        
        return AccountConfig(
            account_id=account_id,
            email=self.email_edit.text().strip(),
            client_id=self.client_id_edit.text().strip(),
            refresh_token=self.refresh_token_edit.toPlainText().strip(),
            password=self.password_edit.text().strip() or None,
            enabled=self.enabled_checkbox.isChecked(),
            priority=self.priority_spinbox.value(),
            max_retries=self.max_retries_spinbox.value()
        )


def main():
    """测试函数"""
    import sys
    from PySide6.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    
    dialog = AccountDialog()
    if dialog.exec() == QDialog.Accepted:
        config = dialog.get_account_config()
        print(f"账户配置: {config}")
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
