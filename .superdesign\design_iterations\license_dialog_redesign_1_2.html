<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>授权成功 - 科幻增强版</title>
    <script src="lucide.min.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Geist+Mono:wght@400;700&display=swap');
        @import url('theme_scifi_glitch_1.css');

        :root {
            --color-normal: var(--primary);
            --color-warning: oklch(0.9 0.25 90); /* Bright Yellow */
            --color-urgent: oklch(0.8 0.25 45); /* Bright Orange */
            --color-expired: var(--destructive); /* Bright Red */
        }

        body {
            background-color: var(--background);
            color: var(--foreground);
            font-family: var(--font-mono);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            margin: 0;
            padding: 0 20px 40px; /* MODIFICATION: Removed top padding */
            text-shadow: 0 0 5px oklch(from var(--primary) l a c / 0.5), 0 0 10px oklch(from var(--primary) l a c / 0.3);
            position: relative;
            overflow: hidden;
            perspective: 1500px; /* For 3D effect */
        }
        
        /* --- NEW: Animated Grid Background --- */
        .grid-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                linear-gradient(oklch(from var(--border) l a c / 0.15) 1px, transparent 1px),
                linear-gradient(90deg, oklch(from var(--border) l a c / 0.15) 1px, transparent 1px);
            background-size: 50px 50px;
            animation: moveGrid 20s linear infinite;
            z-index: -1;
        }

        @keyframes moveGrid {
            from { background-position: 0 0; }
            to { background-position: 50px 50px; }
        }

        /* --- NEW: Ticker --- */
        .ticker-wrap {
            width: 100%;
            max-width: 520px;
            overflow: hidden;
            border: 1px solid var(--border);
            background: oklch(from var(--background) l+0.03 h c);
            padding: 5px 0;
            margin-bottom: 10px;
        }
        .ticker {
            display: inline-block;
            white-space: nowrap;
            animation: scrollTicker 20s linear infinite;
        }
        .ticker span {
            padding: 0 2rem;
            color: var(--muted-foreground);
            font-size: 12px;
        }
        @keyframes scrollTicker {
            0% { transform: translateX(0%); }
            100% { transform: translateX(-50%); }
        }

        .dialog-container {
            width: 100%;
            max-width: 520px;
            background: oklch(from var(--card) l a c / 0.85);
            backdrop-filter: blur(12px);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            box-shadow: var(--shadow-xl);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            /* MODIFICATION: Replaced animation with transition */
            opacity: 0;
            transform: translateY(40px) rotateX(0) rotateY(0);
            transition: opacity 0.6s ease, transform 0.6s cubic-bezier(0.1, 1, 0.2, 1);
            position: relative;
            z-index: 1;
            /* --- NEW: Breathing Glow --- */
            animation: breathing-glow 8s infinite ease-in-out;
        }
        
        /* NEW: Visibility class to be added by JS */
        .dialog-container.visible {
            opacity: 1;
            transform: translateY(0);
        }
        
        /* --- NEW: Top/Bottom Bars & Enhanced Corners --- */
        .dialog-top-bar, .dialog-bottom-bar {
            display: flex;
            justify-content: space-between;
            padding: 8px 12px;
            font-size: 12px;
            color: var(--muted-foreground);
            border-bottom: 1px solid var(--border);
        }
        .dialog-bottom-bar {
            border-top: 1px solid var(--border);
            border-bottom: none;
        }
        .dialog-container::before, .dialog-container::after,
        .corner-tl, .corner-br {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            border-color: var(--primary);
            border-style: solid;
            opacity: 0.7;
            transition: border-color 0.3s ease;
        }
        .corner-tl { top: -1px; left: -1px; border-width: 2px 0 0 2px; }
        .corner-br { bottom: -1px; right: -1px; border-width: 0 2px 2px 0; }


        /* Header */
        .dialog-header {
            padding: 24px 32px;
            text-align: center;
        }

        .header-icon {
            width: 48px;
            height: 48px;
            stroke-width: 1.5;
            margin: 0 auto 16px;
            filter: drop-shadow(0 0 10px oklch(from var(--primary) l a c / 0.8));
        }

        /* --- NEW: Glitch Effect for Title --- */
        .header-title {
            font-size: 24px;
            font-weight: 700;
            color: var(--foreground);
            margin: 0 0 4px 0;
            letter-spacing: var(--tracking-normal);
            text-transform: uppercase;
            position: relative;
        }
        .glitch {
            animation: glitch-shake 2.5s infinite;
        }
        .glitch::before, .glitch::after {
            content: attr(data-text);
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: inherit;
        }
        .glitch::before {
            left: 2px;
            text-shadow: -1px 0 var(--color-expired);
            clip-path: polygon(0 0, 100% 0, 100% 35%, 0 35%);
            animation: glitch-anim-1 2.5s infinite;
        }
        .glitch::after {
            left: -2px;
            text-shadow: -1px 0 var(--color-normal), 1px 1px var(--color-warning);
            clip-path: polygon(0 65%, 100% 65%, 100% 100%, 0 100%);
            animation: glitch-anim-2 2.5s infinite;
        }
        @keyframes glitch-anim-1 { 0%, 12%, 18%, 20%, 100% { clip-path: polygon(0 0, 100% 0, 100% 35%, 0 35%); } 14% { clip-path: polygon(0 40%, 100% 40%, 100% 55%, 0 55%); } 16% { clip-path: polygon(0 20%, 100% 20%, 100% 30%, 0 30%); } }
        @keyframes glitch-anim-2 { 0%, 12%, 18%, 20%, 100% { clip-path: polygon(0 65%, 100% 65%, 100% 100%, 0 100%); } 15% { clip-path: polygon(0 50%, 100% 50%, 100% 60%, 0 60%); } 17% { clip-path: polygon(0 80%, 100% 80%, 100% 90%, 0 90%); } }
        @keyframes glitch-shake { 0%, 12%, 18%, 20%, 100% { transform: none; } 13%, 16%, 19% { transform: skewX(var(--skew, 5deg)); } }

        /* --- NEW: Rainbow Text Effect --- */
        @keyframes rainbow-text-flow {
            0% { background-position: 0% 50%; }
            100% { background-position: 100% 50%; }
        }
        .rainbow-text {
            background-image: linear-gradient(
                to right,
                oklch(0.7 0.25 25),   /* Red */
                oklch(0.8 0.25 80),  /* Orange/Yellow */
                oklch(0.8 0.2 150),  /* Green */
                oklch(0.8 0.2 180),  /* Cyan */
                oklch(0.7 0.25 260), /* Blue */
                oklch(0.8 0.2 300),  /* Purple/Magenta */
                oklch(0.7 0.25 25)   /* Red again for smooth loop */
            );
            background-size: 200% auto;
            color: transparent;
            background-clip: text;
            -webkit-background-clip: text;
            animation: rainbow-text-flow 12s linear infinite;
        }

        .header-subtitle { font-size: 14px; color: var(--muted-foreground); margin: 0; }
        .header-subtitle .user-name { font-weight: 700; color: var(--foreground); }

        /* Countdown & Progress Bar */
        .dialog-countdown { padding: 32px; text-align: center; }
        .countdown-title { font-size: 14px; color: var(--muted-foreground); margin: 0 0 8px 0; text-transform: uppercase; }
        .countdown-expiry-date { font-size: 32px; font-weight: 700; color: var(--foreground); margin: 0 0 24px 0; display: flex; align-items: center; justify-content: center; gap: 12px; }
        .countdown-expiry-date .icon { width: 28px; height: 28px; stroke-width: 1.5; }
        .countdown-remaining { font-size: 18px; font-weight: 700; margin: 0 0 16px 0; display: flex; align-items: center; justify-content: center; gap: 8px; }
        .countdown-remaining .icon { width: 18px; height: 18px; }
        .progress-bar-container { width: 100%; height: 6px; background-color: oklch(from var(--border) l-0.05 h c / 0.5); border-radius: 0; overflow: hidden; margin-top: 8px; border: 1px solid var(--border); padding: 2px; }
        .progress-bar { height: 100%; border-radius: 0; transition: width 0.8s cubic-bezier(0.25, 1, 0.5, 1), background-color 0.4s ease-in-out; box-shadow: 0 0 10px 0px oklch(from var(--primary) l a c / 0.8); }

        /* --- NEW: Data Stream Visualization --- */
        .data-stream-container {
            padding: 20px 32px 10px;
            text-align: center;
            border-top: 1px solid var(--border);
            background: oklch(from var(--background) l+0.02 h c);
        }

        .stream-label {
            font-size: 12px;
            color: var(--muted-foreground);
            text-transform: uppercase;
            margin-bottom: 15px;
            animation: pulse-text 2s infinite ease-in-out;
        }

        .data-stream-svg path {
            stroke-dasharray: 600;
            stroke-dashoffset: 600;
            animation: draw-stream 4s infinite linear;
            filter: drop-shadow(0 0 5px var(--primary));
        }

        @keyframes draw-stream {
            to {
                stroke-dashoffset: 0;
            }
        }

        @keyframes pulse-text {
            0% { opacity: 0.7; }
            50% { opacity: 1; }
            100% { opacity: 0.7; }
        }

        /* Details */
        .dialog-details { padding: 20px 32px; }
        .detail-item { display: flex; align-items: center; justify-content: space-between; font-size: 14px; padding: 10px 0; text-transform: uppercase; }
        .detail-item:not(:last-child) { border-bottom: 1px solid var(--border); }
        .detail-label { color: var(--muted-foreground); display: flex; align-items: center; gap: 8px; }
        .detail-label .icon { width: 14px; height: 14px; stroke-width: 1.5; }
        .detail-value { font-weight: 700; color: var(--foreground); }

        /* Actions */
        .dialog-actions { padding: 24px 32px; display: flex; gap: 12px; }
        .button { flex-grow: 1; padding: 12px 20px; border-radius: var(--radius); border: 1px solid var(--border); font-size: 14px; font-weight: 700; font-family: var(--font-mono); cursor: pointer; transition: transform 0.2s ease, background-color 0.2s ease, box-shadow 0.2s ease, color 0.2s ease; display: flex; align-items: center; justify-content: center; gap: 8px; text-transform: uppercase; position: relative; overflow: hidden; }
        .button .icon { width: 16px; height: 16px; }
        .button:hover { background-color: oklch(from var(--primary) l a c / 0.1); border-color: oklch(from var(--primary) l a c / 0.5); color: var(--primary); }
        .button:active { transform: translateY(1px); }

        /* --- NEW: Button Scan Effect --- */
        .button-primary::after {
            content: '';
            position: absolute;
            top: 0;
            left: -150%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, oklch(from var(--primary) l a c / 0.3), transparent);
            transition: left 0.6s ease;
        }
        .button-primary:hover::after {
            left: 150%;
        }

        /* --- NEW: Breathing Glow Keyframes --- */
        @keyframes breathing-glow {
            0% { box-shadow: 0 0 25px 0px oklch(from var(--primary) l a c / 0.25); }
            50% { box-shadow: 0 0 40px 5px oklch(from var(--primary) l a c / 0.4); }
            100% { box-shadow: 0 0 25px 0px oklch(from var(--primary) l a c / 0.25); }
        }

        .button-primary { background-color: var(--primary); color: var(--primary-foreground); border-color: var(--primary); box-shadow: 0 0 15px 0px oklch(from var(--primary) l a c / 0.5); }
        .button-primary:hover { background-color: oklch(from var(--primary) l+0.1 h c); color: var(--primary-foreground); border-color: oklch(from var(--primary) l+0.1 h c); }
        .button-secondary { background-color: var(--secondary); color: var(--secondary-foreground); }

        /* Status Styles */
        .status-normal .header-icon, .status-normal .countdown-remaining { color: var(--color-normal); }
        .status-normal .progress-bar { background-color: var(--color-normal); box-shadow: 0 0 10px 0px var(--color-normal); }
        .status-warning .header-icon, .status-warning .countdown-remaining { color: var(--color-warning); }
        .status-warning .progress-bar { background-color: var(--color-warning); box-shadow: 0 0 10px 0px var(--color-warning); }
        .status-warning .corner-tl, .status-warning .corner-br { border-color: var(--color-warning); }
        .status-warning .button-primary { background-color: var(--color-warning); color: var(--background); border-color: var(--color-warning); box-shadow: 0 0 15px 0px var(--color-warning); }
        .status-expired .header-icon, .status-expired .countdown-remaining, .status-expired .countdown-expiry-date { color: var(--color-expired); }
        .status-expired .progress-bar { background-color: var(--color-expired); box-shadow: 0 0 10px 0px var(--color-expired); }
        .status-expired .corner-tl, .status-expired .corner-br { border-color: var(--color-expired); }
        .status-expired .button-primary { background-color: var(--color-expired); color: var(--background); border-color: var(--color-expired); box-shadow: 0 0 15px 0px var(--color-expired); }
        .status-permanent .header-icon { color: oklch(0.8 0.2 280); }
        .status-permanent .countdown-expiry-date { color: oklch(0.8 0.2 280); }
        .status-permanent .corner-tl, .status-permanent .corner-br { border-color: oklch(0.8 0.2 280); }
    </style>
</head>
<body>
    <div class="grid-background"></div>

    <div class="ticker-wrap">
        <div class="ticker" style="animation-duration: 25s;">
            <span>// STATUS: SYS_CHECK OK</span>
            <span>// NODE_SYNC: COMPLETE</span>
            <span>// AUTH_KEY: VALIDATED</span>
            <span>// SECURE_LAYER: ACTIVE</span>
            <span>// USER: 正常用户</span>
            <span>// PERMISSION: PREMIUM</span>
            <!-- Repeat for seamless loop -->
            <span>// STATUS: SYS_CHECK OK</span>
            <span>// NODE_SYNC: COMPLETE</span>
            <span>// AUTH_KEY: VALIDATED</span>
            <span>// SECURE_LAYER: ACTIVE</span>
            <span>// USER: 正常用户</span>
            <span>// PERMISSION: PREMIUM</span>
        </div>
    </div>

    <div class="dialog-container status-normal">
        <div class="corner-tl"></div><div class="corner-br"></div>
        <div class="dialog-top-bar">
            <span>[ // O.S.A.S // ]</span>
            <span>[ v3.0.1 ]</span>
        </div>
        <div class="dialog-header">
            <i data-lucide="shield-check" class="header-icon"></i>
            <h1 class="header-title glitch rainbow-text" data-text="微软邮件批量收件">微软邮件批量收件</h1>
            <p class="header-subtitle">用户: <span class="user-name">正常用户</span> [高级权限]</p>
        </div>
        <div class="dialog-countdown">
            <p class="countdown-title">许可证有效期至</p>
            <h2 class="countdown-expiry-date">
                <i data-lucide="calendar-clock" class="icon"></i>
                <span>2025-08-05</span>
            </h2>
            <p class="countdown-remaining">
                <i data-lucide="hourglass" class="icon"></i>
                <span>剩余 365 天</span>
            </p>
            <div class="progress-bar-container">
                <div class="progress-bar" style="width: 80%;"></div>
            </div>
        </div>
        
        <!-- NEW: Data Stream Visualization -->
        <div class="data-stream-container">
            <div class="stream-label">// KEY_VALIDATION_STREAM //</div>
            <svg class="data-stream-svg" width="100%" height="60" viewBox="0 0 450 60" preserveAspectRatio="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M0 30 L50 30 L60 10 L80 50 L100 20 L120 40 L140 15 L160 30 L200 30 L210 5 L230 55 L250 25 L270 45 L290 10 L310 30 L350 30 L360 50 L380 20 L400 40 L420 15 L450 30" 
                      fill="none" stroke="var(--primary)" stroke-width="1.5"/>
            </svg>
        </div>

        <div class="dialog-details">
            <div class="detail-item">
                <span class="detail-label"><i data-lucide="key-round" class="icon"></i>许可证ID</span>
                <span class="detail-value" id="license-key">NORM-****-****-7890</span>
            </div>
            <div class="detail-item">
                <span class="detail-label"><i data-lucide="cpu" class="icon"></i>激活模块</span>
                <span class="detail-value" id="features">邮件管理, 批量导入</span>
            </div>
        </div>
        <div class="dialog-actions">
            <button class="button button-secondary" id="btn-close">
                <i data-lucide="x" class="icon"></i>
                <span>断开连接</span>
            </button>
            <button class="button button-primary" id="btn-start">
                <i data-lucide="rocket" class="icon"></i>
                <span>启动程序</span>
            </button>
        </div>
        <div class="dialog-bottom-bar">
            <span>[SESSION: 8A-F4-C1-D0]</span>
            <span id="timestamp">[TIMESTAMP: --:--:--]</span>
        </div>
    </div>

    <script src="qrc:///qtwebchannel/qwebchannel.js"></script>
    <script>
        let timestampInitialized = false; // Flag to ensure timestamp is set only once

        // --- Main Entry Point ---
        document.addEventListener("DOMContentLoaded", () => {
            lucide.createIcons();

            new QWebChannel(qt.webChannelTransport, (channel) => {
                window.py_bridge = channel.objects.py_bridge;
                console.log("Python bridge connected!");

                const startBtn = document.getElementById('btn-start');
                const closeBtn = document.getElementById('btn-close');
                if(startBtn) startBtn.addEventListener('click', () => window.py_bridge.start());
                if(closeBtn) closeBtn.addEventListener('click', () => window.py_bridge.close());
            });

            // --- NEW: 3D Parallax Effect ---
            const container = document.querySelector('.dialog-container');
            document.body.addEventListener('mousemove', (e) => {
                if (!container.classList.contains('visible')) return;

                const rect = container.getBoundingClientRect();
                const x = e.clientX - rect.left - rect.width / 2;
                const y = e.clientY - rect.top - rect.height / 2;

                const rotateX = -y / 40; // Adjust divisor for sensitivity
                const rotateY = x / 30;  // Adjust divisor for sensitivity

                container.style.transform = `translateY(0) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;
            });

            document.body.addEventListener('mouseleave', () => {
                 container.style.transform = 'translateY(0) rotateX(0) rotateY(0)';
            });
        });

        // --- Function called by Python ---
        function updateUI(licenseData) {
            console.log("Received data from Python:", licenseData);

            // --- FINAL FIX: Initialize timestamp here, as it's the most reliable moment ---
            if (!timestampInitialized) {
                const timestampEl = document.getElementById('timestamp');
                if (timestampEl) {
                    setInterval(() => {
                        const now = new Date();
                        const timeString = now.toTimeString().split(' ')[0];
                        timestampEl.textContent = `[TIMESTAMP: ${timeString}]`;
                    }, 1000);
                    timestampInitialized = true;
                }
            }

            const container = document.querySelector('.dialog-container');
            if (container) container.classList.add('visible');

            const subtitle = document.querySelector('.header-subtitle');
            if(subtitle) subtitle.innerHTML = `用户: <span class="user-name">${licenseData.user_name || '未知用户'}</span> [${licenseData.license_type || '标准'}权限]`;

            const licenseKeyEl = document.getElementById('license-key');
            if(licenseKeyEl) {
                let maskedKey = licenseData.license_key || 'N/A';
                if (maskedKey.length > 8) maskedKey = maskedKey.substring(0, 4) + '-****-****-' + maskedKey.substring(maskedKey.length - 4);
                licenseKeyEl.textContent = maskedKey;
            }

            const featuresEl = document.getElementById('features');
            if(featuresEl) featuresEl.textContent = (licenseData.features || ['N/A']).join(', ');

            const ticker = document.querySelector('.ticker');
            if(ticker) {
                // 使用 textContent 而不是 innerHTML 来避免重新解析HTML
                const tickerContent = [
                    '// STATUS: SYS_CHECK OK',
                    '// NODE_SYNC: COMPLETE',
                    '// AUTH_KEY: VALIDATED',
                    '// SECURE_LAYER: ACTIVE',
                    `// USER: ${licenseData.user_name || 'N/A'}`,
                    `// PERMISSION: ${licenseData.license_type || 'N/A'}`,
                    // 重复内容以实现无缝循环
                    '// STATUS: SYS_CHECK OK',
                    '// NODE_SYNC: COMPLETE',
                    '// AUTH_KEY: VALIDATED',
                    '// SECURE_LAYER: ACTIVE',
                    `// USER: ${licenseData.user_name || 'N/A'}`,
                    `// PERMISSION: ${licenseData.license_type || 'N/A'}`
                ];

                // 清空现有内容并重新创建span元素
                ticker.innerHTML = '';
                tickerContent.forEach(text => {
                    const span = document.createElement('span');
                    span.textContent = text;
                    ticker.appendChild(span);
                });
            }
        }
    </script>
</body>
</html>
