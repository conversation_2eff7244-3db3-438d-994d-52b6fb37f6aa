# 真实邮件账户导入使用说明

## 🎯 概述

企业邮件管理系统现已支持真实邮件账户的导入和管理。您可以使用真实的Outlook、Gmail等邮箱账户来收发邮件。

## 📋 支持的账户格式

### 标准格式
```
邮箱地址----密码----客户端ID----刷新令牌
```

### 示例
```
<EMAIL>----813z9I3V----9e5f94bc-e8a4-4e73-b8be-63364c29d753----M.C520_SN1.0.U.-CnfiQPBh0REEzFhAazPlkmrD8IRFztNOm!hHLBPXDh9UFaa*qrpaUtfKCSCi7U8s!IzyVDmSzIpuOsFOtmezyYO9kJzqBonb2fHNXIx*j4msJnR6WPerq!oO!jVDNV50NdEfp!NG65ByF7jv0TOy!9FzYJqpnN*WVuhkTPSQCBRfMoMftcuoBjzqz9RPKTMucZF6ncat8gWqusGRobCCrGfUTth!AHUqOxl79s6uoAuhp7IiYYCrR6ODbEgQQq598jvELH6qqmmUVaXfg1B2yKkl39sDIGTOB3goceiQ0hNv8UawXO70mUMSOUq*F!gsPyEj2dSA2IrL0cL!oK8CNwqGGiJcPaYb4NxSjAckJPc!*qXXuLHeSVg68USC9cEiyvThHo69fSR4r8v7aH57x1RvudNZ0pyizOELi82e8MDA
```

## 🚀 导入步骤

### 方法一：批量导入（推荐）

1. **启动应用程序**
   ```bash
   python enterprise_email_manager.py
   ```

2. **点击批量导入按钮**
   - 在工具栏中找到 `📥 批量导入` 按钮
   - 点击打开批量导入对话框

3. **粘贴账户信息**
   - 在"文本导入"标签页中
   - 将您的账户信息粘贴到文本框中
   - 每行一个账户，使用您提供的格式

4. **解析账户**
   - 点击 `🔍 解析账户信息` 按钮
   - 系统会自动解析每行账户信息
   - 查看解析结果和统计信息

5. **预览和导入**
   - 切换到"预览导入"标签页
   - 检查解析的账户信息
   - 点击 `📥 导入账户` 完成导入

### 方法二：单个账户配置

1. **点击真实账户按钮**
   - 在工具栏中找到 `🔐 真实账户` 按钮
   - 点击打开账户配置对话框

2. **填写账户信息**
   - **邮箱地址**: `<EMAIL>`
   - **客户端ID**: `9e5f94bc-e8a4-4e73-b8be-63364c29d753`
   - **刷新令牌**: 完整的刷新令牌字符串

3. **配置服务器设置**
   - IMAP服务器: `outlook.office365.com`
   - IMAP端口: `993`
   - SMTP服务器: `smtp-mail.outlook.com`
   - SMTP端口: `587`
   - 启用SSL/TLS: ✅

4. **测试和保存**
   - 点击 `🧪 测试账户连接` 验证配置
   - 点击 `✅ 确定` 保存账户

## 📁 文件导入

### 支持的文件格式

1. **TXT文件**
   ```
   <EMAIL>----pass1----client1----token1
   <EMAIL>----pass2----client2----token2
   ```

2. **CSV文件**
   ```csv
   email,password,client_id,refresh_token
   <EMAIL>,pass1,client1,token1
   <EMAIL>,pass2,client2,token2
   ```

3. **JSON文件**
   ```json
   [
     {
       "email": "<EMAIL>",
       "password": "pass1",
       "client_id": "client1",
       "refresh_token": "token1"
     }
   ]
   ```

## 🔧 功能特性

### 自动配置
- 系统会根据邮箱域名自动配置IMAP/SMTP服务器
- Outlook邮箱自动使用Microsoft服务器设置
- Gmail邮箱自动使用Google服务器设置

### 账户管理
- 启用/禁用账户
- 设置同步间隔（默认5分钟）
- 配置最大邮件数量（默认1000封）
- 自动重试机制

### 邮件同步
- 自动同步收件箱邮件
- 支持HTML和纯文本格式
- 保存邮件到本地数据库
- 实时同步状态显示

### 邮件发送
- 支持HTML格式邮件发送
- 附件支持
- 多账户发送选择
- 发送状态跟踪

## 🎨 界面说明

### 账户树结构
```
🔐 真实账户
  🟢 <EMAIL>
    📥 收件箱
    📤 已发送
    📝 草稿箱
    🗑️ 已删除
    ⚠️ 垃圾邮件

🧪 模拟账户
  📧 <EMAIL>
    📥 收件箱
    📤 已发送
    ...
```

### 状态指示器
- 🟢 绿色圆点：账户已启用且正常工作
- 🔴 红色圆点：账户已禁用或连接失败
- 📧 邮件图标：模拟账户
- 🔐 锁图标：真实账户

## ⚙️ 高级设置

### 连接设置
- **最大重试次数**: 3次（默认）
- **连接超时**: 30秒（默认）
- **保持连接活跃**: 启用（推荐）

### 同步设置
- **同步间隔**: 5分钟（可调整1-60分钟）
- **最大邮件数**: 1000封（可调整10-10000封）
- **自动下载附件**: 禁用（可启用）

### 安全设置
- **OAuth 2.0认证**: 自动使用
- **SSL/TLS加密**: 强制启用
- **令牌自动刷新**: 启用

## 🔍 故障排除

### 常见问题

1. **导入失败**
   - 检查账户格式是否正确
   - 确保使用四个连字符（----）分隔
   - 验证刷新令牌是否有效

2. **连接失败**
   - 检查网络连接
   - 验证服务器设置
   - 确认OAuth令牌未过期

3. **同步问题**
   - 检查账户是否启用
   - 验证IMAP权限
   - 查看错误日志

### 错误代码

- **401**: 认证失败，检查令牌
- **403**: 权限不足，检查账户权限
- **500**: 服务器错误，稍后重试
- **timeout**: 连接超时，检查网络

## 📊 性能优化

### 建议设置
- **同步间隔**: 5-10分钟（平衡性能和实时性）
- **最大邮件数**: 500-1000封（避免过载）
- **并发账户**: 不超过10个（避免API限制）

### 监控指标
- 同步成功率
- 平均响应时间
- 错误频率
- 内存使用量

## 🛡️ 安全注意事项

1. **令牌安全**
   - 刷新令牌具有敏感性，请妥善保管
   - 定期检查令牌有效性
   - 避免在不安全环境中使用

2. **网络安全**
   - 使用安全的网络连接
   - 启用防火墙保护
   - 定期更新系统

3. **数据保护**
   - 邮件数据本地加密存储
   - 定期备份重要邮件
   - 遵守数据保护法规

## 📞 技术支持

如遇到问题，请提供以下信息：
- 错误消息截图
- 账户配置信息（隐藏敏感信息）
- 操作步骤描述
- 系统环境信息

---

**版本**: v2.0  
**更新日期**: 2025-08-03  
**兼容性**: Windows 10/11, Python 3.8+
