#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实邮件账户配置对话框
用于配置和管理真实的邮件账户
"""

import sys
import json
import logging
from pathlib import Path
from typing import Dict, Optional, Any, Tuple
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QGroupBox,
    QLineEdit, QComboBox, QPushButton, QLabel, QCheckBox, QSpinBox,
    QTextEdit, QTabWidget, QWidget, QMessageBox, QProgressBar,
    QApplication, QFrame, QScrollArea
)
from PySide6.QtCore import Qt, QThread, Signal, QTimer
from PySide6.QtGui import QFont, QIcon, QPixmap
from .loading_widget import LoadingManager, ProgressStepManager

class AccountTestThread(QThread):
    """账户测试线程"""
    
    progress_updated = Signal(str)  # 进度更新信号
    test_completed = Signal(bool, str)  # 测试完成信号
    
    def __init__(self, account_config):
        super().__init__()
        self.account_config = account_config
        self.logger = logging.getLogger(__name__)
    
    def run(self):
        """运行账户测试"""
        try:
            self.progress_updated.emit("🔍 正在验证邮箱地址格式...")
            
            # 验证邮箱格式
            email = self.account_config.get('email', '')
            if '@' not in email or '.' not in email.split('@')[1]:
                self.test_completed.emit(False, "邮箱地址格式无效")
                return
            
            self.progress_updated.emit("🔐 正在测试IMAP连接...")
            
            # 测试IMAP连接
            from core.production_optimized_v2 import ProductionOptimizedClientV2, AuthMethod
            
            client = ProductionOptimizedClientV2(
                client_id=self.account_config.get('client_id', ''),
                email=self.account_config['email']
            )
            
            self.progress_updated.emit("🔑 正在获取访问令牌...")
            
            # 尝试获取令牌
            if self.account_config.get('refresh_token'):
                # 设置账户配置
                client.set_account_config(
                    self.account_config['email'],
                    self.account_config['client_id'],
                    self.account_config['refresh_token']
                )
                success = client.get_access_token_optimized(force_refresh=True)
                if not success:
                    self.test_completed.emit(False, "刷新令牌无效或已过期")
                    return
            else:
                self.test_completed.emit(False, "缺少刷新令牌")
                return
            
            self.progress_updated.emit("📧 正在连接IMAP服务器...")

            # 测试IMAP连接
            success = client.connect_and_authenticate_fast()
            if not success:
                self.test_completed.emit(False, "IMAP连接失败")
                return
            
            self.progress_updated.emit("📁 正在获取文件夹列表...")

            # 获取文件夹列表
            folders = client.list_folders_optimized()
            if not folders:
                self.test_completed.emit(False, "无法获取文件夹列表")
                return
            
            self.progress_updated.emit("✅ 账户验证成功！")
            self.test_completed.emit(True, f"成功连接，发现 {len(folders)} 个文件夹")
            
        except Exception as e:
            self.logger.error(f"账户测试失败: {e}")
            self.test_completed.emit(False, f"测试失败: {str(e)}")

class RealAccountConfigDialog(QDialog):
    """真实邮件账户配置对话框"""
    
    def __init__(self, parent=None, account_config=None):
        super().__init__(parent)
        self.account_config = account_config or {}
        self.logger = logging.getLogger(__name__)

        self.setWindowTitle("邮件账户配置")
        self.setFixedSize(600, 700)
        self.setModal(True)

        # 初始化加载管理器
        self.loading_manager = None
        self.progress_manager = None

        self.setup_ui()
        self.load_account_config()

        # 在UI设置完成后初始化加载管理器
        self.loading_manager = LoadingManager(self)
        self.progress_manager = ProgressStepManager(self.loading_manager)
        
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        
        # 基本配置标签页
        self.setup_basic_tab()
        
        # 高级配置标签页
        self.setup_advanced_tab()
        
        # OAuth配置标签页
        self.setup_oauth_tab()
        
        layout.addWidget(self.tab_widget)
        
        # 测试和按钮区域
        self.setup_test_area(layout)
        self.setup_buttons(layout)
    
    def setup_basic_tab(self):
        """设置基本配置标签页"""
        basic_tab = QWidget()
        layout = QVBoxLayout(basic_tab)
        
        # 账户信息组
        account_group = QGroupBox("账户信息")
        account_layout = QFormLayout(account_group)
        
        self.email_edit = QLineEdit()
        self.email_edit.setPlaceholderText("<EMAIL>")
        account_layout.addRow("邮箱地址*:", self.email_edit)

        self.password_edit = QLineEdit()
        self.password_edit.setPlaceholderText("邮箱密码（可选）")
        self.password_edit.setEchoMode(QLineEdit.EchoMode.Password)
        account_layout.addRow("密码:", self.password_edit)

        self.display_name_edit = QLineEdit()
        self.display_name_edit.setPlaceholderText("显示名称")
        account_layout.addRow("显示名称:", self.display_name_edit)

        self.account_name_edit = QLineEdit()
        self.account_name_edit.setPlaceholderText("账户名称（可选）")
        account_layout.addRow("账户名称:", self.account_name_edit)
        
        layout.addWidget(account_group)
        
        # 服务器配置组
        server_group = QGroupBox("服务器配置")
        server_layout = QFormLayout(server_group)
        
        self.provider_combo = QComboBox()
        self.provider_combo.addItems([
            "Outlook/Hotmail (outlook.live.com)",
            "Gmail (gmail.com)",
            "QQ邮箱 (qq.com)",
            "163邮箱 (163.com)",
            "126邮箱 (126.com)",
            "自定义配置"
        ])
        self.provider_combo.currentTextChanged.connect(self.on_provider_changed)
        server_layout.addRow("邮件服务商*:", self.provider_combo)
        
        self.imap_server_edit = QLineEdit()
        self.imap_server_edit.setText("outlook.office365.com")
        server_layout.addRow("IMAP服务器*:", self.imap_server_edit)
        
        self.imap_port_spin = QSpinBox()
        self.imap_port_spin.setRange(1, 65535)
        self.imap_port_spin.setValue(993)
        server_layout.addRow("IMAP端口*:", self.imap_port_spin)
        
        self.smtp_server_edit = QLineEdit()
        self.smtp_server_edit.setText("smtp-mail.outlook.com")
        server_layout.addRow("SMTP服务器*:", self.smtp_server_edit)
        
        self.smtp_port_spin = QSpinBox()
        self.smtp_port_spin.setRange(1, 65535)
        self.smtp_port_spin.setValue(587)
        server_layout.addRow("SMTP端口*:", self.smtp_port_spin)
        
        self.use_ssl_check = QCheckBox("使用SSL/TLS加密")
        self.use_ssl_check.setChecked(True)
        server_layout.addRow("", self.use_ssl_check)
        
        layout.addWidget(server_group)
        
        # 状态组
        status_group = QGroupBox("账户状态")
        status_layout = QFormLayout(status_group)
        
        self.enabled_check = QCheckBox("启用此账户")
        self.enabled_check.setChecked(True)
        status_layout.addRow("", self.enabled_check)
        
        self.priority_spin = QSpinBox()
        self.priority_spin.setRange(1, 10)
        self.priority_spin.setValue(1)
        status_layout.addRow("优先级:", self.priority_spin)
        
        layout.addWidget(status_group)
        
        self.tab_widget.addTab(basic_tab, "基本配置")
    
    def setup_advanced_tab(self):
        """设置高级配置标签页"""
        advanced_tab = QWidget()
        layout = QVBoxLayout(advanced_tab)
        
        # 连接设置组
        connection_group = QGroupBox("连接设置")
        connection_layout = QFormLayout(connection_group)
        
        self.max_retries_spin = QSpinBox()
        self.max_retries_spin.setRange(1, 10)
        self.max_retries_spin.setValue(3)
        connection_layout.addRow("最大重试次数:", self.max_retries_spin)
        
        self.timeout_spin = QSpinBox()
        self.timeout_spin.setRange(5, 300)
        self.timeout_spin.setValue(30)
        self.timeout_spin.setSuffix(" 秒")
        connection_layout.addRow("连接超时:", self.timeout_spin)
        
        self.keep_alive_check = QCheckBox("保持连接活跃")
        self.keep_alive_check.setChecked(True)
        connection_layout.addRow("", self.keep_alive_check)
        
        layout.addWidget(connection_group)
        
        # 同步设置组
        sync_group = QGroupBox("同步设置")
        sync_layout = QFormLayout(sync_group)
        
        self.sync_interval_spin = QSpinBox()
        self.sync_interval_spin.setRange(1, 60)
        self.sync_interval_spin.setValue(5)
        self.sync_interval_spin.setSuffix(" 分钟")
        sync_layout.addRow("同步间隔:", self.sync_interval_spin)
        
        self.max_emails_spin = QSpinBox()
        self.max_emails_spin.setRange(10, 10000)
        self.max_emails_spin.setValue(1000)
        sync_layout.addRow("最大邮件数:", self.max_emails_spin)
        
        self.download_attachments_check = QCheckBox("自动下载附件")
        self.download_attachments_check.setChecked(False)
        sync_layout.addRow("", self.download_attachments_check)
        
        layout.addWidget(sync_group)
        
        self.tab_widget.addTab(advanced_tab, "高级设置")
    
    def setup_oauth_tab(self):
        """设置OAuth配置标签页"""
        oauth_tab = QWidget()
        layout = QVBoxLayout(oauth_tab)
        
        # OAuth信息组
        oauth_group = QGroupBox("OAuth 2.0 配置")
        oauth_layout = QFormLayout(oauth_group)
        
        self.client_id_edit = QLineEdit()
        self.client_id_edit.setPlaceholderText("应用程序客户端ID")
        oauth_layout.addRow("客户端ID*:", self.client_id_edit)
        
        self.refresh_token_edit = QTextEdit()
        self.refresh_token_edit.setMaximumHeight(100)
        self.refresh_token_edit.setPlaceholderText("刷新令牌（从OAuth授权获取）")
        oauth_layout.addRow("刷新令牌*:", self.refresh_token_edit)
        
        # OAuth帮助信息
        help_label = QLabel("""
<b>OAuth 2.0 配置说明：</b><br>
1. 客户端ID：在邮件服务商的开发者控制台中创建应用程序获取<br>
2. 刷新令牌：通过OAuth授权流程获取，用于自动刷新访问令牌<br>
3. 对于Outlook/Hotmail，需要在Azure AD中注册应用程序<br>
4. 对于Gmail，需要在Google Cloud Console中创建项目
        """)
        help_label.setWordWrap(True)
        help_label.setStyleSheet("QLabel { color: #666; font-size: 12px; }")
        oauth_layout.addRow("", help_label)
        
        layout.addWidget(oauth_group)
        
        # OAuth工具组
        tools_group = QGroupBox("OAuth 工具")
        tools_layout = QVBoxLayout(tools_group)
        
        self.oauth_help_btn = QPushButton("📖 OAuth配置帮助")
        self.oauth_help_btn.clicked.connect(self.show_oauth_help)
        tools_layout.addWidget(self.oauth_help_btn)
        
        self.get_token_btn = QPushButton("🔑 获取刷新令牌")
        self.get_token_btn.clicked.connect(self.get_refresh_token)
        tools_layout.addWidget(self.get_token_btn)
        
        layout.addWidget(tools_group)
        
        self.tab_widget.addTab(oauth_tab, "OAuth配置")
    
    def setup_test_area(self, layout):
        """设置测试区域"""
        test_group = QGroupBox("账户测试")
        test_layout = QVBoxLayout(test_group)
        
        # 测试按钮
        test_btn_layout = QHBoxLayout()
        self.test_btn = QPushButton("🧪 测试账户连接")
        self.test_btn.clicked.connect(self.test_account)
        test_btn_layout.addWidget(self.test_btn)
        test_btn_layout.addStretch()
        test_layout.addLayout(test_btn_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        test_layout.addWidget(self.progress_bar)
        
        # 测试结果
        self.test_result_label = QLabel("")
        self.test_result_label.setWordWrap(True)
        test_layout.addWidget(self.test_result_label)
        
        layout.addWidget(test_group)
    
    def setup_buttons(self, layout):
        """设置按钮"""
        button_layout = QHBoxLayout()
        
        self.save_btn = QPushButton("💾 保存配置")
        self.save_btn.clicked.connect(self.save_config)
        button_layout.addWidget(self.save_btn)
        
        self.cancel_btn = QPushButton("❌ 取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        button_layout.addStretch()
        
        self.ok_btn = QPushButton("✅ 确定")
        self.ok_btn.clicked.connect(self.accept)
        button_layout.addWidget(self.ok_btn)
        
        layout.addLayout(button_layout)
    
    def on_provider_changed(self, provider_text):
        """邮件服务商改变事件"""
        if "Outlook" in provider_text:
            self.imap_server_edit.setText("outlook.office365.com")
            self.imap_port_spin.setValue(993)
            self.smtp_server_edit.setText("smtp-mail.outlook.com")
            self.smtp_port_spin.setValue(587)
        elif "Gmail" in provider_text:
            self.imap_server_edit.setText("imap.gmail.com")
            self.imap_port_spin.setValue(993)
            self.smtp_server_edit.setText("smtp.gmail.com")
            self.smtp_port_spin.setValue(587)
        elif "QQ" in provider_text:
            self.imap_server_edit.setText("imap.qq.com")
            self.imap_port_spin.setValue(993)
            self.smtp_server_edit.setText("smtp.qq.com")
            self.smtp_port_spin.setValue(587)
        elif "163" in provider_text:
            self.imap_server_edit.setText("imap.163.com")
            self.imap_port_spin.setValue(993)
            self.smtp_server_edit.setText("smtp.163.com")
            self.smtp_port_spin.setValue(25)
        elif "126" in provider_text:
            self.imap_server_edit.setText("imap.126.com")
            self.imap_port_spin.setValue(993)
            self.smtp_server_edit.setText("smtp.126.com")
            self.smtp_port_spin.setValue(25)
    
    def load_account_config(self):
        """加载账户配置"""
        if not self.account_config:
            return

        # 检查account_config的类型
        if hasattr(self.account_config, 'email'):
            # 如果是RealAccountConfig对象，直接访问属性
            self.email_edit.setText(getattr(self.account_config, 'email', ''))
            self.password_edit.setText(getattr(self.account_config, 'password', ''))
            self.display_name_edit.setText(getattr(self.account_config, 'display_name', ''))
            self.account_name_edit.setText(getattr(self.account_config, 'display_name', ''))  # 使用display_name作为account_name

            # 服务器配置
            self.imap_server_edit.setText(getattr(self.account_config, 'imap_server', ''))
            self.imap_port_spin.setValue(getattr(self.account_config, 'imap_port', 993))
            self.smtp_server_edit.setText(getattr(self.account_config, 'smtp_server', ''))
            self.smtp_port_spin.setValue(getattr(self.account_config, 'smtp_port', 587))
            self.use_ssl_check.setChecked(getattr(self.account_config, 'use_ssl', True))

            # OAuth配置
            self.client_id_edit.setText(getattr(self.account_config, 'client_id', ''))
            self.refresh_token_edit.setPlainText(getattr(self.account_config, 'refresh_token', ''))

            # 高级设置
            self.enabled_check.setChecked(getattr(self.account_config, 'enabled', True))
            self.priority_spin.setValue(getattr(self.account_config, 'priority', 1))
            self.max_retries_spin.setValue(getattr(self.account_config, 'max_retries', 3))
            self.timeout_spin.setValue(getattr(self.account_config, 'timeout', 30))
            self.keep_alive_check.setChecked(getattr(self.account_config, 'keep_alive', True))
            self.sync_interval_spin.setValue(getattr(self.account_config, 'sync_interval', 5))
            self.max_emails_spin.setValue(getattr(self.account_config, 'max_emails', 1000))
            self.download_attachments_check.setChecked(getattr(self.account_config, 'download_attachments', False))
        else:
            # 如果是字典，使用get方法
            self.email_edit.setText(self.account_config.get('email', ''))
            self.password_edit.setText(self.account_config.get('password', ''))
            self.display_name_edit.setText(self.account_config.get('display_name', ''))
            self.account_name_edit.setText(self.account_config.get('account_name', ''))

            # 服务器配置
            self.imap_server_edit.setText(self.account_config.get('imap_server', ''))
            self.imap_port_spin.setValue(self.account_config.get('imap_port', 993))
            self.smtp_server_edit.setText(self.account_config.get('smtp_server', ''))
            self.smtp_port_spin.setValue(self.account_config.get('smtp_port', 587))
            self.use_ssl_check.setChecked(self.account_config.get('use_ssl', True))

            # OAuth配置
            self.client_id_edit.setText(self.account_config.get('client_id', ''))
            self.refresh_token_edit.setPlainText(self.account_config.get('refresh_token', ''))

            # 高级设置
            self.enabled_check.setChecked(self.account_config.get('enabled', True))
            self.priority_spin.setValue(self.account_config.get('priority', 1))
            self.max_retries_spin.setValue(self.account_config.get('max_retries', 3))
            self.timeout_spin.setValue(self.account_config.get('timeout', 30))
            self.keep_alive_check.setChecked(self.account_config.get('keep_alive', True))
            self.sync_interval_spin.setValue(self.account_config.get('sync_interval', 5))
            self.max_emails_spin.setValue(self.account_config.get('max_emails', 1000))
            self.download_attachments_check.setChecked(self.account_config.get('download_attachments', False))
    
    def get_account_config(self) -> Dict[str, Any]:
        """获取账户配置"""
        return {
            'email': self.email_edit.text().strip(),
            'password': self.password_edit.text().strip(),
            'display_name': self.display_name_edit.text().strip(),
            'account_name': self.account_name_edit.text().strip(),
            'imap_server': self.imap_server_edit.text().strip(),
            'imap_port': self.imap_port_spin.value(),
            'smtp_server': self.smtp_server_edit.text().strip(),
            'smtp_port': self.smtp_port_spin.value(),
            'use_ssl': self.use_ssl_check.isChecked(),
            'client_id': self.client_id_edit.text().strip(),
            'refresh_token': self.refresh_token_edit.toPlainText().strip(),
            'enabled': self.enabled_check.isChecked(),
            'priority': self.priority_spin.value(),
            'max_retries': self.max_retries_spin.value(),
            'timeout': self.timeout_spin.value(),
            'keep_alive': self.keep_alive_check.isChecked(),
            'sync_interval': self.sync_interval_spin.value(),
            'max_emails': self.max_emails_spin.value(),
            'download_attachments': self.download_attachments_check.isChecked()
        }
    
    def validate_config(self) -> Tuple[bool, str]:
        """验证配置"""
        config = self.get_account_config()
        
        # 必填字段验证
        if not config['email']:
            return False, "邮箱地址不能为空"
        
        if '@' not in config['email']:
            return False, "邮箱地址格式无效"
        
        if not config['imap_server']:
            return False, "IMAP服务器不能为空"
        
        if not config['smtp_server']:
            return False, "SMTP服务器不能为空"
        
        if not config['client_id']:
            return False, "客户端ID不能为空"
        
        if not config['refresh_token']:
            return False, "刷新令牌不能为空"
        
        return True, "配置验证通过"
    
    def test_account(self):
        """测试账户连接"""
        # 验证配置
        valid, message = self.validate_config()
        if not valid:
            QMessageBox.warning(self, "配置错误", message)
            return
        
        # 开始测试
        self.test_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 无限进度条
        self.test_result_label.setText("正在测试账户连接...")
        
        # 创建测试线程
        self.test_thread = AccountTestThread(self.get_account_config())
        self.test_thread.progress_updated.connect(self.on_test_progress)
        self.test_thread.test_completed.connect(self.on_test_completed)
        self.test_thread.start()
    
    def on_test_progress(self, message):
        """测试进度更新"""
        self.test_result_label.setText(message)
    
    def on_test_completed(self, success, message):
        """测试完成"""
        self.test_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        
        if success:
            self.test_result_label.setText(f"✅ {message}")
            self.test_result_label.setStyleSheet("QLabel { color: green; }")
        else:
            self.test_result_label.setText(f"❌ {message}")
            self.test_result_label.setStyleSheet("QLabel { color: red; }")
    
    def show_oauth_help(self):
        """显示OAuth帮助"""
        help_text = """
<h3>OAuth 2.0 配置帮助</h3>

<h4>Outlook/Hotmail 配置：</h4>
<ol>
<li>访问 <a href="https://portal.azure.com">Azure Portal</a></li>
<li>注册新的应用程序</li>
<li>配置重定向URI和权限</li>
<li>获取客户端ID和刷新令牌</li>
</ol>

<h4>Gmail 配置：</h4>
<ol>
<li>访问 <a href="https://console.cloud.google.com">Google Cloud Console</a></li>
<li>创建新项目或选择现有项目</li>
<li>启用Gmail API</li>
<li>创建OAuth 2.0凭据</li>
<li>获取客户端ID和刷新令牌</li>
</ol>

<h4>注意事项：</h4>
<ul>
<li>确保应用程序有适当的权限</li>
<li>刷新令牌需要通过OAuth授权流程获取</li>
<li>某些邮件服务商可能需要启用"不太安全的应用访问"</li>
</ul>
        """
        
        msg = QMessageBox(self)
        msg.setWindowTitle("OAuth配置帮助")
        msg.setText(help_text)
        msg.setTextFormat(Qt.RichText)
        msg.exec()
    
    def get_refresh_token(self):
        """获取刷新令牌"""
        QMessageBox.information(
            self,
            "获取刷新令牌",
            "此功能需要实现OAuth授权流程。\n"
            "请参考OAuth配置帮助，手动获取刷新令牌。"
        )
    
    def save_config(self):
        """保存配置"""
        valid, message = self.validate_config()
        if not valid:
            QMessageBox.warning(self, "配置错误", message)
            return

        # 设置保存步骤
        steps = [
            "🔍 验证配置信息...",
            "📁 创建配置目录...",
            "💾 保存配置文件...",
            "✅ 保存完成"
        ]

        # 开始进度显示
        self.progress_manager.set_steps(steps)
        self.progress_manager.start_progress([self.save_btn, self.ok_btn])

        try:
            # 步骤1: 验证配置
            QTimer.singleShot(300, self._save_step_1)

        except Exception as e:
            self.progress_manager.error_progress("保存配置失败")
            QTimer.singleShot(2500, lambda: QMessageBox.critical(
                self, "保存失败", f"保存配置时出错：\n{str(e)}"
            ))

    def _save_step_1(self):
        """保存步骤1: 获取配置"""
        try:
            self.config = self.get_account_config()
            self.progress_manager.next_step()
            QTimer.singleShot(200, self._save_step_2)
        except Exception as e:
            self.progress_manager.cancel_progress()
            QMessageBox.critical(self, "保存失败", f"获取配置失败：\n{str(e)}")

    def _save_step_2(self):
        """保存步骤2: 创建目录"""
        try:
            config_dir = Path("config")
            config_dir.mkdir(exist_ok=True)
            self.config_file = config_dir / f"account_{self.config['email'].replace('@', '_').replace('.', '_')}.json"
            self.progress_manager.next_step()
            QTimer.singleShot(200, self._save_step_3)
        except Exception as e:
            self.progress_manager.cancel_progress()
            QMessageBox.critical(self, "保存失败", f"创建配置目录失败：\n{str(e)}")

    def _save_step_3(self):
        """保存步骤3: 保存文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            self.progress_manager.next_step()
            QTimer.singleShot(500, self._save_complete)
        except Exception as e:
            self.progress_manager.cancel_progress()
            QMessageBox.critical(self, "保存失败", f"保存配置文件失败：\n{str(e)}")

    def _save_complete(self):
        """保存完成"""
        self.progress_manager.complete_progress("✅ 配置保存成功！")
        QTimer.singleShot(1000, lambda: QMessageBox.information(
            self, "保存成功", f"账户配置已保存到：\n{self.config_file}"
        ))
    
    def accept(self):
        """确定按钮 - 添加账户"""
        valid, message = self.validate_config()
        if not valid:
            QMessageBox.warning(self, "配置错误", message)
            return

        # 设置添加账户步骤
        steps = [
            "🔍 验证账户信息...",
            "🔑 测试账户连接...",
            "💾 保存账户配置...",
            "🚀 启动账户同步...",
            "✅ 账户添加完成"
        ]

        # 开始进度显示
        self.progress_manager.set_steps(steps)
        self.progress_manager.start_progress([self.ok_btn, self.save_btn, self.cancel_btn])

        try:
            # 步骤1: 验证配置
            QTimer.singleShot(300, self._accept_step_1)

        except Exception as e:
            self.progress_manager.error_progress("添加账户失败")
            QTimer.singleShot(2500, lambda: QMessageBox.critical(
                self, "添加失败", f"添加账户时出错：\n{str(e)}"
            ))

    def _accept_step_1(self):
        """添加步骤1: 验证配置"""
        try:
            self.config = self.get_account_config()

            # 检查是否有现有的邮件数据
            account_id = self.config['email'].replace('@', '_').replace('.', '_')
            try:
                from core.email_database import EmailDatabase
                db = EmailDatabase()
                existing_emails = db.count_emails_by_account(account_id)

                if existing_emails > 0:
                    # 显示邮件数据恢复提示
                    reply = QMessageBox.question(
                        self, "发现现有邮件数据",
                        f"🎉 好消息！发现账户 '{self.config['email']}' 的现有邮件数据：\n\n"
                        f"📧 邮件数量: {existing_emails} 封\n"
                        f"💾 数据状态: 完整保存\n\n"
                        f"添加账户后，这些邮件将立即可用，无需重新下载！\n\n"
                        f"是否继续添加账户？",
                        QMessageBox.Yes | QMessageBox.No,
                        QMessageBox.Yes
                    )

                    if reply != QMessageBox.Yes:
                        self.progress_manager.cancel_progress()
                        return

                    self.existing_emails_count = existing_emails
                else:
                    self.existing_emails_count = 0

            except Exception as check_e:
                self.logger.warning(f"检查现有邮件数据失败: {check_e}")
                self.existing_emails_count = 0

            self.progress_manager.next_step()
            QTimer.singleShot(500, self._accept_step_2)
        except Exception as e:
            self.progress_manager.cancel_progress()
            QMessageBox.critical(self, "添加失败", f"验证配置失败：\n{str(e)}")

    def _accept_step_2(self):
        """添加步骤2: 测试连接"""
        try:
            # 这里可以添加实际的连接测试逻辑
            # 模拟测试过程
            self.progress_manager.next_step()
            QTimer.singleShot(1000, self._accept_step_3)
        except Exception as e:
            self.progress_manager.cancel_progress()
            QMessageBox.critical(self, "添加失败", f"测试连接失败：\n{str(e)}")

    def _accept_step_3(self):
        """添加步骤3: 保存配置"""
        try:
            # 保存配置逻辑
            config_dir = Path("config")
            config_dir.mkdir(exist_ok=True)
            config_file = config_dir / f"account_{self.config['email'].replace('@', '_').replace('.', '_')}.json"

            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)

            self.progress_manager.next_step()
            QTimer.singleShot(500, self._accept_step_4)
        except Exception as e:
            self.progress_manager.cancel_progress()
            QMessageBox.critical(self, "添加失败", f"保存配置失败：\n{str(e)}")

    def _accept_step_4(self):
        """添加步骤4: 启动同步"""
        try:
            # 这里可以添加启动同步的逻辑
            self.progress_manager.next_step()
            QTimer.singleShot(500, self._accept_complete)
        except Exception as e:
            self.progress_manager.cancel_progress()
            QMessageBox.critical(self, "添加失败", f"启动同步失败：\n{str(e)}")

    def _accept_complete(self):
        """添加完成"""
        # 根据是否有现有邮件数据显示不同的完成消息
        if hasattr(self, 'existing_emails_count') and self.existing_emails_count > 0:
            success_message = f"🎉 账户添加成功！已恢复 {self.existing_emails_count} 封邮件"
        else:
            success_message = "🎉 账户添加成功！"

        self.progress_manager.complete_progress(success_message)
        QTimer.singleShot(1500, lambda: super(RealAccountConfigDialog, self).accept())

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    dialog = RealAccountConfigDialog()
    if dialog.exec() == QDialog.Accepted:
        config = dialog.get_account_config()
        print("账户配置：", config)
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
