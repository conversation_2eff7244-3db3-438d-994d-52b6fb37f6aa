<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>授权成功 - 科幻风格</title>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.min.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Geist+Mono:wght@400;700&display=swap');
        @import url('theme_scifi_glitch_1.css');

        :root {
            --color-normal: var(--primary);
            --color-warning: oklch(0.9 0.25 90); /* Bright Yellow */
            --color-urgent: oklch(0.8 0.25 45); /* Bright Orange */
            --color-expired: var(--destructive); /* Bright Red */
        }

        body {
            background-color: var(--background);
            color: var(--foreground);
            font-family: var(--font-mono);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: flex-start;
            min-height: 100vh;
            margin: 0;
            padding: 40px 20px;
            gap: 40px;
            text-shadow: 0 0 5px oklch(from var(--primary) l a c / 0.5), 0 0 10px oklch(from var(--primary) l a c / 0.3);
            position: relative;
            overflow: hidden;
        }
        
        /* Scanline effect */
        body::after {
            content: ' ';
            display: block;
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: linear-gradient(
                rgba(18, 16, 16, 0) 50%,
                rgba(0, 0, 0, 0.25) 50%
            ), linear-gradient(
                90deg, rgba(255, 0, 0, 0.06),
                rgba(0, 255, 0, 0.02),
                rgba(0, 0, 255, 0.06)
            );
            background-size: 100% 4px, 100% 100%;
            z-index: 2;
            pointer-events: none;
            animation: flicker 0.15s infinite;
        }

        @keyframes flicker {
            0% { opacity: 0.8; }
            5% { opacity: 0.7; }
            10% { opacity: 0.9; }
            15% { opacity: 0.6; }
            20% { opacity: 1; }
            100% { opacity: 1; }
        }

        .preview-title {
            font-size: 20px;
            font-weight: 700;
            color: var(--primary);
            border: 1px solid var(--border);
            border-bottom: 1px solid var(--primary);
            padding: 10px 20px;
            background: oklch(from var(--background) l+0.03 h c);
            letter-spacing: var(--tracking-normal);
            text-transform: uppercase;
        }

        .dialog-container {
            width: 100%;
            max-width: 520px;
            background: oklch(from var(--card) l a c / 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            box-shadow: var(--shadow-xl);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            animation: dialogLoad 0.8s 0.2s cubic-bezier(0.1, 1, 0.2, 1) forwards;
            opacity: 0;
            transform: translateY(40px);
            position: relative;
            z-index: 1;
        }
        
        /* Corner brackets */
        .dialog-container::before, .dialog-container::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            border-color: var(--primary);
            border-style: solid;
            opacity: 0.7;
        }
        .dialog-container::before {
            top: 10px;
            left: 10px;
            border-width: 2px 0 0 2px;
        }
        .dialog-container::after {
            bottom: 10px;
            right: 10px;
            border-width: 0 2px 2px 0;
        }


        @keyframes dialogLoad {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Header */
        .dialog-header {
            padding: 24px 32px;
            text-align: center;
            border-bottom: 1px solid var(--border);
            background: linear-gradient(to bottom, oklch(from var(--card) l a c / 0.5), transparent);
        }

        .header-icon {
            width: 48px;
            height: 48px;
            stroke-width: 1.5;
            margin: 0 auto 16px;
            filter: drop-shadow(0 0 10px oklch(from var(--primary) l a c / 0.8));
        }

        .header-title {
            font-size: 24px;
            font-weight: 700;
            color: var(--foreground);
            margin: 0 0 4px 0;
            letter-spacing: var(--tracking-normal);
            text-transform: uppercase;
        }

        .header-subtitle {
            font-size: 14px;
            color: var(--muted-foreground);
            margin: 0;
        }
        .header-subtitle .user-name {
            font-weight: 700;
            color: var(--foreground);
        }

        /* Countdown */
        .dialog-countdown {
            padding: 32px;
            text-align: center;
        }
        
        .countdown-title {
            font-size: 14px;
            color: var(--muted-foreground);
            margin: 0 0 8px 0;
            text-transform: uppercase;
        }

        .countdown-expiry-date {
            font-size: 32px;
            font-weight: 700;
            color: var(--foreground);
            margin: 0 0 24px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
        }
        .countdown-expiry-date .icon {
            width: 28px;
            height: 28px;
            stroke-width: 1.5;
        }

        .countdown-remaining {
            font-size: 18px;
            font-weight: 700;
            margin: 0 0 16px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        .countdown-remaining .icon {
            width: 18px;
            height: 18px;
        }

        .progress-bar-container {
            width: 100%;
            height: 6px;
            background-color: oklch(from var(--border) l-0.05 h c / 0.5);
            border-radius: 0;
            overflow: hidden;
            margin-top: 8px;
            border: 1px solid var(--border);
            padding: 2px;
        }

        .progress-bar {
            height: 100%;
            border-radius: 0;
            transition: width 0.8s cubic-bezier(0.25, 1, 0.5, 1), background-color 0.4s ease-in-out;
            box-shadow: 0 0 10px 0px oklch(from var(--primary) l a c / 0.8);
        }

        /* Details */
        .dialog-details {
            padding: 20px 32px;
            border-top: 1px solid var(--border);
        }
        .detail-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 14px;
            padding: 10px 0;
            text-transform: uppercase;
        }
        .detail-item:not(:last-child) {
            border-bottom: 1px solid var(--border);
        }
        .detail-label {
            color: var(--muted-foreground);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .detail-label .icon {
            width: 14px;
            height: 14px;
            stroke-width: 1.5;
        }
        .detail-value {
            font-weight: 700;
            color: var(--foreground);
            background-color: transparent;
            padding: 0;
            border-radius: 0;
        }

        /* Actions */
        .dialog-actions {
            padding: 24px 32px;
            display: flex;
            gap: 12px;
            border-top: 1px solid var(--border);
        }

        .button {
            flex-grow: 1;
            padding: 12px 20px;
            border-radius: var(--radius);
            border: 1px solid var(--border);
            font-size: 14px;
            font-weight: 700;
            font-family: var(--font-mono);
            cursor: pointer;
            transition: transform 0.2s ease, background-color 0.2s ease, box-shadow 0.2s ease, color 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            text-transform: uppercase;
            position: relative;
            overflow: hidden;
        }
        .button .icon {
            width: 16px;
            height: 16px;
        }

        .button:hover {
            background-color: oklch(from var(--primary) l a c / 0.1);
            border-color: oklch(from var(--primary) l a c / 0.5);
            color: var(--primary);
        }
        .button:active {
            transform: translateY(1px);
        }

        .button-primary {
            background-color: var(--primary);
            color: var(--primary-foreground);
            border-color: var(--primary);
            box-shadow: 0 0 15px 0px oklch(from var(--primary) l a c / 0.5);
        }
        .button-primary:hover {
             background-color: oklch(from var(--primary) l+0.1 h c);
             color: var(--primary-foreground);
             border-color: oklch(from var(--primary) l+0.1 h c);
        }

        .button-secondary {
            background-color: var(--secondary);
            color: var(--secondary-foreground);
        }

        /* Status Styles */
        .status-normal .header-icon, .status-normal .countdown-remaining { color: var(--color-normal); }
        .status-normal .progress-bar { background-color: var(--color-normal); box-shadow: 0 0 10px 0px var(--color-normal); }

        .status-warning .header-icon, .status-warning .countdown-remaining { color: var(--color-warning); }
        .status-warning .progress-bar { background-color: var(--color-warning); box-shadow: 0 0 10px 0px var(--color-warning); }
        .status-warning .dialog-container::before, .status-warning .dialog-container::after { border-color: var(--color-warning); }
        .status-warning .button-primary { background-color: var(--color-warning); color: var(--background); border-color: var(--color-warning); box-shadow: 0 0 15px 0px var(--color-warning); }

        .status-expired .header-icon, .status-expired .countdown-remaining, .status-expired .countdown-expiry-date { color: var(--color-expired); }
        .status-expired .progress-bar { background-color: var(--color-expired); box-shadow: 0 0 10px 0px var(--color-expired); }
        .status-expired .dialog-container::before, .status-expired .dialog-container::after { border-color: var(--color-expired); }
        .status-expired .button-primary { background-color: var(--color-expired); color: var(--background); border-color: var(--color-expired); box-shadow: 0 0 15px 0px var(--color-expired); }

        .status-permanent .header-icon { color: oklch(0.8 0.2 280); } /* Purple for permanent */
        .status-permanent .countdown-expiry-date { color: oklch(0.8 0.2 280); }
        .status-permanent .dialog-container::before, .status-permanent .dialog-container::after { border-color: oklch(0.8 0.2 280); }

    </style>
</head>
<body>

    <!-- Normal License -->
    <div>
        <h2 class="preview-title">// STATUS: NORMAL</h2>
        <div class="dialog-container status-normal">
            <div class="dialog-header">
                <i data-lucide="shield-check" class="header-icon"></i>
                <h1 class="header-title">授权协议已同步</h1>
                <p class="header-subtitle">用户: <span class="user-name">正常用户</span> [高级权限]</p>
            </div>
            <div class="dialog-countdown">
                <p class="countdown-title">许可证有效期至</p>
                <h2 class="countdown-expiry-date">
                    <i data-lucide="calendar-clock" class="icon"></i>
                    <span>2025-08-05</span>
                </h2>
                <p class="countdown-remaining">
                    <i data-lucide="hourglass" class="icon"></i>
                    <span>剩余 365 天</span>
                </p>
                <div class="progress-bar-container">
                    <div class="progress-bar" style="width: 80%;"></div>
                </div>
            </div>
            <div class="dialog-details">
                <div class="detail-item">
                    <span class="detail-label"><i data-lucide="key-round" class="icon"></i>许可证ID</span>
                    <span class="detail-value">NORM-****-****-7890</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label"><i data-lucide="cpu" class="icon"></i>激活模块</span>
                    <span class="detail-value">邮件管理, 批量导入</span>
                </div>
            </div>
            <div class="dialog-actions">
                <button class="button button-secondary">
                    <i data-lucide="x" class="icon"></i>
                    <span>断开连接</span>
                </button>
                <button class="button button-primary">
                    <i data-lucide="rocket" class="icon"></i>
                    <span>启动程序</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Warning License (30 days) -->
    <div>
        <h2 class="preview-title" style="color: var(--color-warning); border-bottom-color: var(--color-warning);">// STATUS: WARNING</h2>
        <div class="dialog-container status-warning">
            <div class="dialog-header">
                <i data-lucide="shield-alert" class="header-icon"></i>
                <h1 class="header-title">授权协议即将失效</h1>
                <p class="header-subtitle">用户: <span class="user-name">警告用户</span> [标准权限]</p>
            </div>
            <div class="dialog-countdown">
                <p class="countdown-title">许可证有效期至</p>
                <h2 class="countdown-expiry-date">
                    <i data-lucide="calendar-clock" class="icon"></i>
                    <span>2024-09-04</span>
                </h2>
                <p class="countdown-remaining">
                    <i data-lucide="hourglass" class="icon"></i>
                    <span>剩余 30 天</span>
                </p>
                <div class="progress-bar-container">
                    <div class="progress-bar" style="width: 8%;"></div>
                </div>
            </div>
            <div class="dialog-details">
                 <div class="detail-item">
                    <span class="detail-label"><i data-lucide="key-round" class="icon"></i>许可证ID</span>
                    <span class="detail-value">WARN-****-****-6789</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label"><i data-lucide="cpu" class="icon"></i>激活模块</span>
                    <span class="detail-value">邮件管理</span>
                </div>
            </div>
            <div class="dialog-actions">
                <button class="button button-secondary">
                    <i data-lucide="x" class="icon"></i>
                    <span>稍后</span>
                </button>
                <button class="button button-primary">
                    <i data-lucide="refresh-cw" class="icon"></i>
                    <span>同步续期</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Expired License -->
    <div>
        <h2 class="preview-title" style="color: var(--color-expired); border-bottom-color: var(--color-expired);">// STATUS: EXPIRED</h2>
        <div class="dialog-container status-expired">
            <div class="dialog-header">
                <i data-lucide="shield-off" class="header-icon"></i>
                <h1 class="header-title">授权协议已过期</h1>
                <p class="header-subtitle">连接已于 10 天前中断</p>
            </div>
            <div class="dialog-countdown">
                <p class="countdown-title">协议过期时间</p>
                <h2 class="countdown-expiry-date">
                    <i data-lucide="calendar-x" class="icon"></i>
                    <span>2024-07-26</span>
                </h2>
                <p class="countdown-remaining">
                    <i data-lucide="alert-triangle" class="icon"></i>
                    <span>必须续期以重新建立连接</span>
                </p>
                <div class="progress-bar-container">
                    <div class="progress-bar" style="width: 100%;"></div>
                </div>
            </div>
            <div class="dialog-details">
                 <div class="detail-item">
                    <span class="detail-label"><i data-lucide="key-round" class="icon"></i>许可证ID</span>
                    <span class="detail-value">EXPI-****-****-6789</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label"><i data-lucide="cpu" class="icon"></i>受限模块</span>
                    <span class="detail-value">邮件管理</span>
                </div>
            </div>
            <div class="dialog-actions">
                <button class="button button-secondary">
                    <i data-lucide="log-out" class="icon"></i>
                    <span>终止</span>
                </button>
                <button class="button button-primary">
                    <i data-lucide="shopping-cart" class="icon"></i>
                    <span>接入续期端口</span>
                </button>
            </div>
        </div>
    </div>
    
    <script>
        lucide.createIcons();
    </script>
</body>
</html>
