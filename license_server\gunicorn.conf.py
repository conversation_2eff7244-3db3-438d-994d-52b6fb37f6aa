#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gunicorn配置文件
用于生产环境部署
"""

import os
import multiprocessing

# 服务器配置
bind = f"0.0.0.0:{os.environ.get('PORT', 8080)}"
workers = multiprocessing.cpu_count() * 2 + 1
worker_class = "sync"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 30
keepalive = 2

# 日志配置
accesslog = "access.log"
errorlog = "error.log"
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# 进程配置
preload_app = True
daemon = False
pidfile = "gunicorn.pid"
user = None
group = None
tmp_upload_dir = None

# 安全配置
limit_request_line = 4094
limit_request_fields = 100
limit_request_field_size = 8190

# 性能配置
worker_tmp_dir = "/dev/shm"  # 使用内存文件系统提高性能（Linux）
