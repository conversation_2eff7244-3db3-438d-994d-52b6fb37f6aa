# 📁 企业邮件管理系统 - 项目结构

## 🎯 项目概述

企业级邮件管理系统，基于PySide6实现，支持真实邮件账户管理、OAuth 2.0认证、IMAP/SMTP协议，具备现代化的用户界面和完整的邮件管理功能。

## 📂 目录结构

```
outlook - 副本/
├── 📁 core/                          # 核心业务逻辑模块
│   ├── __init__.py                   # 包初始化文件
│   ├── production_optimized_v2.py    # 生产级邮件客户端（OAuth 2.0 + IMAP）
│   ├── email_database.py            # 邮件数据库管理
│   ├── multi_account_manager.py     # 多账户管理器
│   └── real_account_manager.py      # 真实账户管理器
│
├── 📁 ui/                            # 用户界面模块
│   ├── __init__.py                   # 包初始化文件
│   ├── account_dialog.py            # 账户配置对话框
│   ├── advanced_search_dialog.py    # 高级搜索对话框
│   ├── attachment_manager_dialog.py # 附件管理对话框
│   ├── email_compose_dialog.py      # 邮件撰写对话框
│   ├── email_list_view.py           # 邮件列表视图
│   ├── outlook_style_viewer.py      # Outlook风格邮件查看器
│   └── real_account_config_dialog.py # 真实账户配置对话框
│
├── 📁 utils/                         # 工具和辅助模块
│   ├── __init__.py                   # 包初始化文件
│   ├── batch_account_importer.py    # 批量账户导入器（模拟账户）
│   ├── batch_real_account_importer.py # 批量真实账户导入器
│   ├── email_html_converter.py      # HTML邮件转换器
│   ├── email_sender.py              # 邮件发送器
│   ├── folder_manager.py            # 文件夹管理器
│   ├── html_email_processor.py      # HTML邮件处理器
│   ├── imap_optimizer.py            # IMAP连接优化器
│   └── production_monitoring.py     # 生产环境监控
│
├── 📁 static/                        # 静态资源文件
│   ├── outlook_styles.css           # Outlook风格样式表
│   └── outlook_viewer.js            # 邮件查看器JavaScript
│
├── 📁 docs/                          # 项目文档
│   ├── ADVANCED_SEARCH_GUIDE.md     # 高级搜索使用指南
│   ├── ATTACHMENT_MANAGER_GUIDE.md  # 附件管理指南
│   ├── COMPLETION_REPORT.md         # 项目完成报告
│   ├── EMAIL_OPERATIONS_GUIDE.md    # 邮件操作指南
│   ├── EMAIL_VIEWING_GUIDE.md       # 邮件查看指南
│   ├── HTML_VIEWER_GUIDE.md         # HTML查看器指南
│   ├── HTML邮件撰写功能说明.md       # HTML邮件撰写说明
│   ├── README_NEW_UI.md             # 新界面说明
│   ├── 真实账户使用指南.md           # 真实账户使用指南
│   └── 真实账户导入使用说明.md       # 真实账户导入说明
│
├── 📁 config/                        # 配置文件目录
│   └── account_*.json               # 账户配置文件
│
├── 📁 logs/                          # 日志文件目录
│   └── startup.log                  # 启动日志
│
├── 📁 email_html_output/             # HTML邮件输出目录
│   ├── *.html                       # 生成的HTML邮件文件
│   ├── outlook_styles.css           # 样式文件副本
│   └── outlook_viewer.js            # 脚本文件副本
│
├── 📁 backup_ui_20250803-054202/     # UI备份目录
│   └── *.py                         # 备份的UI文件
│
├── 📁 __pycache__/                   # Python缓存目录
│   └── *.pyc                        # 编译的Python文件
│
├── 📄 enterprise_email_manager.py   # 🚀 主应用程序入口
├── 📄 start_enterprise_email_manager.py # 启动脚本
├── 📄 install_dependencies.py       # 依赖安装脚本
├── 📄 requirements.txt              # Python依赖列表
├── 📄 multi_account_config.json     # 多账户配置文件
├── 📄 email_storage.db              # SQLite邮件数据库
└── 📄 PROJECT_STRUCTURE.md          # 📋 本文档
```

## 🎯 核心功能模块

### 🔐 Core 模块
- **production_optimized_v2.py**: 生产级邮件客户端，支持OAuth 2.0认证和IMAP协议
- **email_database.py**: SQLite数据库管理，存储邮件、账户和文件夹信息
- **multi_account_manager.py**: 多账户管理，支持模拟和真实账户
- **real_account_manager.py**: 真实账户管理，处理OAuth认证和邮件同步

### 🖥️ UI 模块
- **enterprise_email_manager.py**: 主界面，Outlook风格的邮件客户端
- **real_account_config_dialog.py**: 真实账户配置界面
- **email_compose_dialog.py**: 邮件撰写界面，支持HTML编辑
- **advanced_search_dialog.py**: 高级搜索功能
- **attachment_manager_dialog.py**: 附件管理界面

### 🛠️ Utils 模块
- **batch_real_account_importer.py**: 批量导入真实账户
- **email_html_converter.py**: HTML邮件格式转换
- **email_sender.py**: 邮件发送功能
- **imap_optimizer.py**: IMAP连接优化

## 🚀 启动方式

### 方法1：直接启动
```bash
python enterprise_email_manager.py
```

### 方法2：使用启动脚本
```bash
python start_enterprise_email_manager.py
```

### 方法3：安装依赖后启动
```bash
python install_dependencies.py
python enterprise_email_manager.py
```

## 📋 主要功能

### ✅ 已实现功能
- 🔐 **真实邮件账户支持** - OAuth 2.0认证，支持Outlook/Hotmail
- 📧 **邮件管理** - 收发邮件、HTML显示、附件管理
- 🔍 **高级搜索** - 多条件搜索、日期范围、发件人过滤
- 📥 **批量导入** - 支持特定格式的账户批量导入
- 💾 **数据持久化** - SQLite数据库存储
- 🎨 **现代化界面** - Outlook风格的用户界面
- 🔄 **自动同步** - 定期同步新邮件
- 📎 **附件支持** - 附件查看和管理

### 🎯 技术特性
- **框架**: PySide6 (Qt6)
- **数据库**: SQLite
- **认证**: OAuth 2.0
- **协议**: IMAP/SMTP
- **架构**: 模块化设计
- **界面**: 响应式布局

## 🔧 配置说明

### 账户配置
- **模拟账户**: 存储在 `multi_account_config.json`
- **真实账户**: 存储在 `config/account_*.json`

### 数据库
- **文件**: `email_storage.db`
- **表结构**: accounts, emails, folders
- **索引**: 优化查询性能

### 日志
- **位置**: `logs/` 目录
- **级别**: INFO, WARNING, ERROR
- **轮转**: 按日期自动轮转

## 📊 项目统计

- **总文件数**: ~50个文件
- **代码行数**: ~15,000行
- **模块数**: 3个主要模块（core, ui, utils）
- **功能数**: 20+个主要功能
- **支持格式**: HTML, 纯文本邮件
- **数据库表**: 3个主要表

## 🎉 版本信息

- **版本**: v2.0.0
- **状态**: ✅ 生产就绪
- **最后更新**: 2025-08-03
- **维护状态**: 🟢 活跃维护

---

**注意**: 此项目结构经过优化整理，移除了临时文件和测试脚本，保持了清晰的模块化架构。
