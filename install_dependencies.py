#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
依赖安装脚本
自动安装企业邮件管理系统所需的所有依赖包
"""

import sys
import subprocess
import os
from pathlib import Path

def run_command(command, description):
    """运行命令并显示结果"""
    print(f"\n🔧 {description}...")
    print(f"执行命令: {command}")
    print("-" * 50)
    
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            capture_output=True, 
            text=True, 
            encoding='utf-8'
        )
        
        if result.stdout:
            print(result.stdout)
        
        if result.stderr:
            print("错误信息:")
            print(result.stderr)
        
        if result.returncode == 0:
            print(f"✅ {description}成功")
            return True
        else:
            print(f"❌ {description}失败 (返回码: {result.returncode})")
            return False
            
    except Exception as e:
        print(f"❌ 执行命令时出错: {e}")
        return False

def check_pip():
    """检查pip是否可用"""
    print("🔍 检查pip...")
    
    try:
        result = subprocess.run([sys.executable, "-m", "pip", "--version"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ pip版本: {result.stdout.strip()}")
            return True
        else:
            print("❌ pip不可用")
            return False
    except Exception as e:
        print(f"❌ 检查pip时出错: {e}")
        return False

def upgrade_pip():
    """升级pip到最新版本"""
    return run_command(
        f"{sys.executable} -m pip install --upgrade pip",
        "升级pip"
    )

def install_from_requirements():
    """从requirements.txt安装依赖"""
    requirements_file = Path("requirements.txt")
    
    if not requirements_file.exists():
        print("❌ requirements.txt文件不存在")
        return False
    
    return run_command(
        f"{sys.executable} -m pip install -r requirements.txt",
        "从requirements.txt安装依赖"
    )

def install_individual_packages():
    """逐个安装依赖包"""
    packages = [
        "requests>=2.25.0",
        "PySide6>=6.6.0", 
        "Pillow>=10.0.0",
        "python-dateutil>=2.8.0",
        "psutil>=5.8.0"
    ]
    
    success_count = 0
    
    for package in packages:
        print(f"\n📦 安装 {package}...")
        if run_command(
            f"{sys.executable} -m pip install {package}",
            f"安装 {package}"
        ):
            success_count += 1
        else:
            print(f"⚠️ {package} 安装失败，继续安装其他包...")
    
    print(f"\n📊 安装结果: {success_count}/{len(packages)} 个包安装成功")
    return success_count == len(packages)

def verify_installation():
    """验证安装结果"""
    print("\n🔍 验证安装结果...")

    modules_to_check = {
        'requests': 'requests',
        'PySide6.QtWidgets': 'PySide6',  # 检查具体的子模块
        'PIL': 'Pillow',  # Pillow包在Python中用PIL导入
        'dateutil': 'python-dateutil',
        'psutil': 'psutil'
    }

    success_count = 0

    for module, package_name in modules_to_check.items():
        try:
            __import__(module)
            print(f"✅ {package_name} - 安装成功")
            success_count += 1
        except ImportError:
            print(f"❌ {package_name} - 安装失败或不可用")

    print(f"\n📊 验证结果: {success_count}/{len(modules_to_check)} 个模块可用")
    return success_count == len(modules_to_check)

def show_manual_instructions():
    """显示手动安装说明"""
    print("\n" + "="*60)
    print("📋 手动安装说明")
    print("="*60)
    print("\n如果自动安装失败，请尝试以下手动安装方法：")
    print("\n方法1 - 使用requirements.txt:")
    print("pip install -r requirements.txt")
    print("\n方法2 - 逐个安装:")
    print("pip install requests>=2.25.0")
    print("pip install PySide6>=6.6.0")
    print("pip install Pillow>=10.0.0") 
    print("pip install python-dateutil>=2.8.0")
    print("pip install psutil>=5.8.0")
    print("\n方法3 - 一次性安装:")
    print("pip install requests>=2.25.0 PySide6>=6.6.0 Pillow>=10.0.0 python-dateutil>=2.8.0 psutil>=5.8.0")
    print("\n如果遇到权限问题，请尝试:")
    print("pip install --user [包名]")
    print("\n如果遇到网络问题，请尝试使用国内镜像:")
    print("pip install -i https://pypi.tuna.tsinghua.edu.cn/simple [包名]")

def main():
    """主函数"""
    print("="*60)
    print("🏢 企业邮件管理系统 - 依赖安装脚本")
    print("="*60)
    
    print(f"\nPython版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    
    # 检查pip
    if not check_pip():
        print("\n❌ pip不可用，无法继续安装")
        show_manual_instructions()
        return 1
    
    # 升级pip
    print("\n" + "="*40)
    print("第1步: 升级pip")
    print("="*40)
    upgrade_pip()  # 即使失败也继续
    
    # 安装依赖
    print("\n" + "="*40)
    print("第2步: 安装依赖包")
    print("="*40)
    
    # 首先尝试从requirements.txt安装
    if install_from_requirements():
        print("✅ 从requirements.txt安装成功")
    else:
        print("⚠️ 从requirements.txt安装失败，尝试逐个安装...")
        install_individual_packages()
    
    # 验证安装
    print("\n" + "="*40)
    print("第3步: 验证安装结果")
    print("="*40)
    
    if verify_installation():
        print("\n🎉 所有依赖安装成功！")
        print("\n现在可以运行以下命令启动应用:")
        print("python start_enterprise_email_manager.py")
        return 0
    else:
        print("\n⚠️ 部分依赖安装失败")
        show_manual_instructions()
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        input("\n按Enter键退出...")
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n用户取消安装")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 安装过程中出现错误: {e}")
        show_manual_instructions()
        input("\n按Enter键退出...")
        sys.exit(1)
