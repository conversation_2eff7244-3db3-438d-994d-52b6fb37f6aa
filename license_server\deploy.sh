#!/bin/bash
# 许可证服务器部署脚本

set -e  # 遇到错误立即退出

echo "🚀 开始部署许可证服务器..."

# 检查Python版本
python3 --version
if [ $? -ne 0 ]; then
    echo "❌ Python3 未安装，请先安装Python3"
    exit 1
fi

# 检查pip
pip3 --version
if [ $? -ne 0 ]; then
    echo "❌ pip3 未安装，请先安装pip3"
    exit 1
fi

# 创建虚拟环境
echo "📦 创建Python虚拟环境..."
python3 -m venv venv
source venv/bin/activate

# 升级pip
echo "⬆️  升级pip..."
pip install --upgrade pip

# 安装依赖
echo "📚 安装Python依赖..."
pip install -r requirements.txt

# 检查环境配置文件
if [ ! -f ".env" ]; then
    echo "⚠️  未找到.env文件，复制示例配置..."
    cp .env.example .env
    echo "📝 请编辑.env文件设置正确的配置值"
    echo "   特别是SECRET_KEY、JWT_SECRET_KEY和ADMIN_PASSWORD"
fi

# 初始化数据库
echo "🗄️  初始化数据库..."
python3 simple_init.py

# 创建systemd服务文件
echo "🔧 创建systemd服务文件..."
sudo tee /etc/systemd/system/license-server.service > /dev/null <<EOF
[Unit]
Description=License Server
After=network.target

[Service]
Type=notify
User=www-data
Group=www-data
WorkingDirectory=$(pwd)
Environment=PATH=$(pwd)/venv/bin
ExecStart=$(pwd)/venv/bin/gunicorn --config gunicorn.conf.py app:create_app()
ExecReload=/bin/kill -s HUP \$MAINPID
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 设置文件权限
echo "🔒 设置文件权限..."
sudo chown -R www-data:www-data .
sudo chmod +x run.py

# 重新加载systemd
echo "🔄 重新加载systemd..."
sudo systemctl daemon-reload

# 启用服务
echo "✅ 启用许可证服务器服务..."
sudo systemctl enable license-server

# 启动服务
echo "🚀 启动许可证服务器..."
sudo systemctl start license-server

# 检查服务状态
echo "📊 检查服务状态..."
sudo systemctl status license-server --no-pager

# 配置防火墙（如果需要）
echo "🔥 配置防火墙..."
if command -v ufw &> /dev/null; then
    # 重要：先允许SSH端口，避免锁定
    sudo ufw allow ssh
    sudo ufw allow 22/tcp
    sudo ufw allow 80/tcp
    sudo ufw allow 443/tcp
    sudo ufw allow 8080/tcp
    echo "已配置防火墙规则（SSH、HTTP、HTTPS、8080端口）"
    echo "⚠️  防火墙规则已配置但未启用，请手动执行 'sudo ufw enable'"
fi

# 显示部署信息
echo ""
echo "🎉 许可证服务器部署完成！"
echo ""
echo "📋 服务信息:"
echo "   服务地址: http://$(hostname -I | awk '{print $1}'):8080"
echo "   管理后台: http://$(hostname -I | awk '{print $1}'):8080/admin"
echo "   API文档: http://$(hostname -I | awk '{print $1}'):8080/api/v1/ping"
echo ""
echo "🔧 管理命令:"
echo "   查看状态: sudo systemctl status license-server"
echo "   启动服务: sudo systemctl start license-server"
echo "   停止服务: sudo systemctl stop license-server"
echo "   重启服务: sudo systemctl restart license-server"
echo "   查看日志: sudo journalctl -u license-server -f"
echo ""
echo "⚠️  重要提醒:"
echo "   1. 请修改.env文件中的默认密码和密钥"
echo "   2. 建议使用nginx作为反向代理"
echo "   3. 在生产环境中使用PostgreSQL或MySQL数据库"
echo "   4. 定期备份数据库文件"
echo ""
