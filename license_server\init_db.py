#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库初始化脚本
"""

import os
import json
from datetime import datetime, timedelta
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

from app import create_app
from models import db, License, Admin


def init_database():
    """初始化数据库"""
    print("🗄️  初始化数据库...")
    
    app = create_app()
    
    with app.app_context():
        # 创建所有表
        db.create_all()
        print("✅ 数据库表创建完成")
        
        # 创建默认管理员
        admin_username = app.config['ADMIN_USERNAME']
        admin_password = app.config['ADMIN_PASSWORD']
        
        existing_admin = Admin.query.filter_by(username=admin_username).first()
        if not existing_admin:
            admin = Admin(
                username=admin_username,
                email='<EMAIL>'
            )
            admin.set_password(admin_password)
            db.session.add(admin)
            db.session.commit()
            print(f"✅ 创建默认管理员: {admin_username}")
        else:
            print(f"ℹ️  管理员已存在: {admin_username}")
        
        # 创建示例许可证（可选）
        create_sample = input("是否创建示例许可证？(y/n): ").lower().strip()
        if create_sample in ['y', 'yes', '是']:
            create_sample_licenses()
        
        print("🎉 数据库初始化完成！")


def create_sample_licenses():
    """创建示例许可证"""
    print("📝 创建示例许可证...")
    
    # 示例许可证数据
    sample_licenses = [
        {
            'license_type': 'standard',
            'max_activations': 1,
            'expires_days': 365,
            'user_name': '测试用户',
            'user_email': '<EMAIL>',
            'company_name': '测试公司',
            'features': ['email_management'],
            'notes': '这是一个测试许可证'
        },
        {
            'license_type': 'premium',
            'max_activations': 3,
            'expires_days': 730,
            'user_name': '高级用户',
            'user_email': '<EMAIL>',
            'company_name': '高级公司',
            'features': ['email_management', 'batch_import', 'advanced_search'],
            'notes': '高级版许可证示例'
        },
        {
            'license_type': 'enterprise',
            'max_activations': 10,
            'expires_days': None,  # 永久
            'user_name': '企业用户',
            'user_email': '<EMAIL>',
            'company_name': '企业公司',
            'features': ['email_management', 'batch_import', 'advanced_search', 'auto_sync', 'multi_account'],
            'notes': '企业版许可证示例（永久有效）'
        }
    ]
    
    created_count = 0
    for license_data in sample_licenses:
        # 创建许可证
        license_obj = License(
            license_type=license_data['license_type'],
            max_activations=license_data['max_activations'],
            user_name=license_data['user_name'],
            user_email=license_data['user_email'],
            company_name=license_data['company_name'],
            features=json.dumps(license_data['features']),
            notes=license_data['notes']
        )
        
        # 设置过期时间
        if license_data['expires_days']:
            license_obj.expires_at = datetime.utcnow() + timedelta(days=license_data['expires_days'])
        
        db.session.add(license_obj)
        created_count += 1
    
    db.session.commit()
    print(f"✅ 创建了 {created_count} 个示例许可证")
    
    # 显示创建的许可证
    print("\n📋 创建的示例许可证:")
    licenses = License.query.all()
    for license_obj in licenses:
        print(f"   {license_obj.license_key} - {license_obj.license_type} - {license_obj.user_name}")


def reset_database():
    """重置数据库"""
    confirm = input("⚠️  确定要重置数据库吗？这将删除所有数据！(yes/no): ").lower().strip()
    if confirm != 'yes':
        print("❌ 操作已取消")
        return
    
    print("🗑️  重置数据库...")
    
    app = create_app()
    
    with app.app_context():
        # 删除所有表
        db.drop_all()
        print("✅ 删除所有表完成")
        
        # 重新创建表
        db.create_all()
        print("✅ 重新创建表完成")
        
        # 创建默认管理员
        admin = Admin(
            username=app.config['ADMIN_USERNAME'],
            email='<EMAIL>'
        )
        admin.set_password(app.config['ADMIN_PASSWORD'])
        db.session.add(admin)
        db.session.commit()
        print(f"✅ 创建默认管理员: {admin.username}")
        
        print("🎉 数据库重置完成！")


def show_stats():
    """显示数据库统计信息"""
    print("📊 数据库统计信息:")
    
    app = create_app()
    
    with app.app_context():
        # 许可证统计
        total_licenses = License.query.count()
        active_licenses = License.query.filter_by(status='active').count()
        expired_licenses = License.query.filter(
            License.expires_at < datetime.utcnow()
        ).count()
        
        print(f"   总许可证数: {total_licenses}")
        print(f"   活跃许可证: {active_licenses}")
        print(f"   过期许可证: {expired_licenses}")
        
        # 管理员统计
        total_admins = Admin.query.count()
        active_admins = Admin.query.filter_by(is_active=True).count()
        
        print(f"   总管理员数: {total_admins}")
        print(f"   活跃管理员: {active_admins}")


def main():
    """主函数"""
    print("🔧 许可证服务器数据库管理工具")
    print("=" * 40)
    
    while True:
        print("\n请选择操作:")
        print("1. 初始化数据库")
        print("2. 重置数据库")
        print("3. 显示统计信息")
        print("4. 退出")
        
        choice = input("\n请输入选项 (1-4): ").strip()
        
        if choice == '1':
            init_database()
        elif choice == '2':
            reset_database()
        elif choice == '3':
            show_stats()
        elif choice == '4':
            print("👋 再见！")
            break
        else:
            print("❌ 无效选项，请重新选择")


if __name__ == '__main__':
    main()
