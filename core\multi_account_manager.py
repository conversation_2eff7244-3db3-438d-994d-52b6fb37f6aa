#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多账户邮箱管理系统
基于v2.0性能优化的批量邮箱管理解决方案
"""

import asyncio
import logging
import time
import json
import threading
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from concurrent.futures import ThreadPoolExecutor, as_completed
from enum import Enum
import queue

# 导入优化的客户端
from .production_optimized_v2 import ProductionOptimizedClientV2, AuthMethod

class AccountStatus(Enum):
    """账户状态枚举"""
    PENDING = "pending"
    CONNECTING = "connecting"
    AUTHENTICATED = "authenticated"
    FAILED = "failed"
    DISABLED = "disabled"

@dataclass
class AccountConfig:
    """账户配置"""
    account_id: str
    email: str
    client_id: str
    refresh_token: str
    password: str = ""
    enabled: bool = True
    priority: int = 1
    max_retries: int = 3

@dataclass
class AccountResult:
    """账户处理结果"""
    account_id: str
    email: str
    status: AccountStatus
    folders: List[str] = None
    folder_count: int = 0
    auth_method: str = ""
    auth_time: float = 0.0
    token_time: float = 0.0
    error_message: str = ""
    timestamp: float = 0.0

class MultiAccountManager:
    """多账户管理器"""
    
    def __init__(self, max_concurrent: int = 10, enable_fast_mode: bool = True):
        self.max_concurrent = max_concurrent
        self.enable_fast_mode = enable_fast_mode
        
        # 账户管理
        self.accounts: Dict[str, AccountConfig] = {}
        self.results: Dict[str, AccountResult] = {}
        
        # 性能统计
        self.performance_stats = {
            'total_accounts': 0,
            'successful_accounts': 0,
            'failed_accounts': 0,
            'total_processing_time': 0.0,
            'avg_processing_time': 0.0,
            'method_usage': {method.value: 0 for method in AuthMethod}
        }
        
        # 线程安全
        self.lock = threading.RLock()
        self.result_queue = queue.Queue()
        
        # 配置日志
        self.logger = logging.getLogger(__name__)
        
    def add_account(self, account_config: AccountConfig):
        """添加账户"""
        with self.lock:
            self.accounts[account_config.account_id] = account_config
            self.logger.info(f"添加账户: {account_config.email}")
            # 自动保存配置
            self.save_accounts_to_config("multi_account_config.json")
    
    def load_accounts_from_config(self, config_file: str):
        """从配置文件加载账户"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            for account_data in config_data.get('accounts', []):
                account = AccountConfig(**account_data)
                self.add_account(account)
            
            self.logger.info(f"从配置文件加载了 {len(self.accounts)} 个账户")
            
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")

    def save_accounts_to_config(self, config_file: str):
        """保存账户到配置文件"""
        try:
            config_data = {
                'accounts': []
            }

            for account_config in self.accounts.values():
                account_dict = {
                    'account_id': account_config.account_id,
                    'email': account_config.email,
                    'client_id': account_config.client_id,
                    'refresh_token': account_config.refresh_token,
                    'password': account_config.password,
                    'enabled': account_config.enabled,
                    'priority': account_config.priority,
                    'max_retries': account_config.max_retries
                }
                config_data['accounts'].append(account_dict)

            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)

            self.logger.info(f"保存了 {len(self.accounts)} 个账户到配置文件")

        except Exception as e:
            self.logger.error(f"保存配置文件失败: {e}")

    def process_single_account(self, account_config: AccountConfig) -> AccountResult:
        """处理单个账户"""
        start_time = time.time()
        
        result = AccountResult(
            account_id=account_config.account_id,
            email=account_config.email,
            status=AccountStatus.CONNECTING,
            timestamp=start_time
        )
        
        client = None
        
        try:
            self.logger.info(f"开始处理账户: {account_config.email}")
            
            # 创建客户端
            client = ProductionOptimizedClientV2(enable_fast_mode=self.enable_fast_mode)
            
            # 设置账户信息
            client.email = account_config.email
            client.client_id = account_config.client_id
            client.refresh_token = account_config.refresh_token
            
            # 连接和认证
            if client.connect_and_authenticate_fast():
                result.status = AccountStatus.AUTHENTICATED
                result.auth_method = client.current_method.value
                
                # 获取性能信息
                if client.token_cache:
                    result.token_time = client.token_cache.last_refresh_time
                
                if client.performance_stats['auth_times']:
                    result.auth_time = list(client.performance_stats['auth_times'].values())[0]
                
                # 获取文件夹列表
                folders = client.list_folders_optimized()
                result.folders = folders
                result.folder_count = len(folders)
                
                self.logger.info(f"账户处理成功: {account_config.email} ({len(folders)} 个文件夹)")
                
            else:
                result.status = AccountStatus.FAILED
                result.error_message = "认证失败"
                self.logger.error(f"账户认证失败: {account_config.email}")
                
        except Exception as e:
            result.status = AccountStatus.FAILED
            result.error_message = str(e)
            self.logger.error(f"账户处理异常: {account_config.email}, 错误: {e}")
            
        finally:
            # 清理连接
            if client:
                client.close()
            
            # 更新处理时间
            result.timestamp = time.time() - start_time
            
        return result
    
    def process_accounts_batch(self, account_ids: List[str] = None) -> Dict[str, AccountResult]:
        """批量处理账户"""
        if account_ids is None:
            account_ids = list(self.accounts.keys())
        
        # 过滤启用的账户
        enabled_accounts = [
            self.accounts[aid] for aid in account_ids 
            if aid in self.accounts and self.accounts[aid].enabled
        ]
        
        if not enabled_accounts:
            self.logger.warning("没有启用的账户需要处理")
            return {}
        
        self.logger.info(f"开始批量处理 {len(enabled_accounts)} 个账户")
        start_time = time.time()
        
        results = {}
        
        # 使用线程池并发处理
        with ThreadPoolExecutor(max_workers=self.max_concurrent) as executor:
            # 提交任务
            future_to_account = {
                executor.submit(self.process_single_account, account): account
                for account in enabled_accounts
            }
            
            # 收集结果
            for future in as_completed(future_to_account):
                account = future_to_account[future]
                try:
                    result = future.result()
                    results[result.account_id] = result
                    
                    # 更新统计
                    with self.lock:
                        self.results[result.account_id] = result
                        self._update_performance_stats(result)
                    
                except Exception as e:
                    self.logger.error(f"处理账户 {account.email} 时出现异常: {e}")
                    
                    # 创建失败结果
                    error_result = AccountResult(
                        account_id=account.account_id,
                        email=account.email,
                        status=AccountStatus.FAILED,
                        error_message=str(e),
                        timestamp=time.time() - start_time
                    )
                    results[account.account_id] = error_result
        
        total_time = time.time() - start_time
        
        # 更新总体统计
        with self.lock:
            self.performance_stats['total_processing_time'] = total_time
            if self.performance_stats['total_accounts'] > 0:
                self.performance_stats['avg_processing_time'] = (
                    self.performance_stats['total_processing_time'] / 
                    self.performance_stats['total_accounts']
                )
        
        self.logger.info(f"批量处理完成: {len(results)} 个账户, 耗时 {total_time:.2f}s")
        
        return results
    
    def _update_performance_stats(self, result: AccountResult):
        """更新性能统计"""
        self.performance_stats['total_accounts'] += 1
        
        if result.status == AccountStatus.AUTHENTICATED:
            self.performance_stats['successful_accounts'] += 1
            
            # 更新方法使用统计
            if result.auth_method:
                self.performance_stats['method_usage'][result.auth_method] += 1
        else:
            self.performance_stats['failed_accounts'] += 1
    
    def get_summary_report(self) -> Dict[str, Any]:
        """获取汇总报告"""
        with self.lock:
            successful = self.performance_stats['successful_accounts']
            total = self.performance_stats['total_accounts']
            
            report = {
                'summary': {
                    'total_accounts': total,
                    'successful_accounts': successful,
                    'failed_accounts': self.performance_stats['failed_accounts'],
                    'success_rate': f"{(successful/total*100):.1f}%" if total > 0 else "0%",
                    'total_processing_time': f"{self.performance_stats['total_processing_time']:.2f}s",
                    'avg_processing_time': f"{self.performance_stats['avg_processing_time']:.2f}s"
                },
                'method_usage': self.performance_stats['method_usage'],
                'account_details': {}
            }
            
            # 添加账户详情
            for account_id, result in self.results.items():
                report['account_details'][account_id] = {
                    'email': result.email,
                    'status': result.status.value,
                    'folder_count': result.folder_count,
                    'auth_method': result.auth_method,
                    'auth_time': f"{result.auth_time:.2f}s",
                    'token_time': f"{result.token_time:.2f}s",
                    'processing_time': f"{result.timestamp:.2f}s",
                    'error': result.error_message if result.error_message else None
                }
            
            return report
    
    def export_results(self, filename: str = None):
        """导出结果"""
        if filename is None:
            filename = f"multi_account_results_{int(time.time())}.json"
        
        report = self.get_summary_report()
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"结果已导出到: {filename}")
            
        except Exception as e:
            self.logger.error(f"导出结果失败: {e}")

def create_sample_multi_account_config():
    """创建示例多账户配置"""
    config = {
        'accounts': [
            {
                'account_id': 'account_1',
                'email': '<EMAIL>',
                'client_id': '9e5f94bc-e8a4-4e73-b8be-63364c29d753',
                'refresh_token': 'M.C553_BL2.0.U.-Cu95AlCo5F2ecNfP6eXPgKg2vMKcS*SulS6ospsomaeXcL8hnU1KodCx7YO833tuKNxW1v2HuliUqfU!*HE3A6LESe2MLb5nNeKZNyN73uC9e0dZH6Z1UhBuf0lQLd1!38GP2XfvohTnYDTe56OlYf*Oizlw58XX*LlTZ*QFUGVET7oDq9JBGR8ajeIQDHbLIVsv2ow7SXaWTEtYG0k2Q*t0rn7cDUN8jtW4eHpnWJd*0EkyF1ms7kZHFKGBIIDA!3Fq6X3WNOnfa7b22J6H03bkynsOowREmiChJAWIzSKu3qNrBPuHwY885OF8IAIKx4tTCUaANbdkyBIQ1zHt*VgHQ*Bg*1ZXH28sW6u!KFRGN!h1LUuiHywFfsGO!5mcARYOfF43ITUinVsCvP6NxjNy!UzPYlDNGMfkZQvAl7yC',
                'password': 'QLqI229U7u',
                'enabled': True,
                'priority': 1,
                'max_retries': 3
            }
            # 可以添加更多账户
        ],
        'settings': {
            'max_concurrent': 10,
            'enable_fast_mode': True,
            'timeout_settings': {
                'connection_timeout': 6,
                'token_timeout': 8
            }
        }
    }
    
    with open('multi_account_config.json', 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print("✅ 创建了示例多账户配置文件: multi_account_config.json")

def main():
    """主函数"""
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    print("🚀 多账户邮箱管理系统测试")
    print("=" * 50)
    
    # 创建示例配置
    create_sample_multi_account_config()
    
    # 创建管理器
    manager = MultiAccountManager(max_concurrent=5, enable_fast_mode=True)
    
    # 加载配置
    manager.load_accounts_from_config('multi_account_config.json')
    
    # 批量处理账户
    print(f"\n📊 开始批量处理账户...")
    results = manager.process_accounts_batch()
    
    # 显示结果
    print(f"\n📋 处理结果:")
    report = manager.get_summary_report()
    
    print(f"   总账户数: {report['summary']['total_accounts']}")
    print(f"   成功账户: {report['summary']['successful_accounts']}")
    print(f"   失败账户: {report['summary']['failed_accounts']}")
    print(f"   成功率: {report['summary']['success_rate']}")
    print(f"   总处理时间: {report['summary']['total_processing_time']}")
    
    print(f"\n📈 认证方法使用统计:")
    for method, count in report['method_usage'].items():
        if count > 0:
            print(f"   {method}: {count} 次")
    
    print(f"\n📄 账户详情:")
    for account_id, details in report['account_details'].items():
        status_icon = "✅" if details['status'] == 'authenticated' else "❌"
        print(f"   {status_icon} {details['email']}: {details['status']} ({details['folder_count']} 文件夹)")
        if details['error']:
            print(f"      错误: {details['error']}")
    
    # 导出结果
    manager.export_results()

if __name__ == "__main__":
    main()
