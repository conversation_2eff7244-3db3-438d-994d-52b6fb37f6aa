#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级搜索对话框
提供多条件邮件搜索功能
"""

import sys
from datetime import datetime, timedelta
from typing import Dict, Any, List

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QGridLayout,
    QLabel, QLineEdit, QPushButton, QComboBox, QDateEdit, QSpinBox,
    QCheckBox, QTextEdit, QTabWidget, QWidget, QGroupBox,
    QListWidget, QListWidgetItem, QSplitter, QMessageBox
)
from PySide6.QtCore import Qt, QDate, Signal
from PySide6.QtGui import QFont

class AdvancedSearchDialog(QDialog):
    """高级搜索对话框"""
    
    # 搜索信号
    search_requested = Signal(dict)  # 发送搜索条件字典
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("高级邮件搜索")
        self.setModal(True)
        self.resize(800, 600)
        
        # 搜索历史
        self.search_history = []
        
        self.setup_ui()
        self.setup_connections()
        self.load_search_history()
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        
        # 基本搜索标签页
        self.basic_tab = self.create_basic_search_tab()
        self.tab_widget.addTab(self.basic_tab, "基本搜索")
        
        # 高级搜索标签页
        self.advanced_tab = self.create_advanced_search_tab()
        self.tab_widget.addTab(self.advanced_tab, "高级搜索")
        
        # 搜索历史标签页
        self.history_tab = self.create_history_tab()
        self.tab_widget.addTab(self.history_tab, "搜索历史")
        
        layout.addWidget(self.tab_widget)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.search_btn = QPushButton("搜索")
        self.search_btn.setDefault(True)
        button_layout.addWidget(self.search_btn)
        
        self.reset_btn = QPushButton("重置")
        button_layout.addWidget(self.reset_btn)
        
        button_layout.addStretch()
        
        self.save_btn = QPushButton("保存搜索")
        button_layout.addWidget(self.save_btn)
        
        self.cancel_btn = QPushButton("取消")
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
    
    def create_basic_search_tab(self):
        """创建基本搜索标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 快速搜索区域
        quick_group = QGroupBox("快速搜索")
        quick_layout = QFormLayout(quick_group)
        
        self.quick_search_edit = QLineEdit()
        self.quick_search_edit.setPlaceholderText("输入关键词搜索主题、发件人或内容...")
        quick_layout.addRow("关键词:", self.quick_search_edit)
        
        self.search_scope_combo = QComboBox()
        self.search_scope_combo.addItems([
            "全部内容", "仅主题", "仅发件人", "仅内容", "主题和发件人"
        ])
        quick_layout.addRow("搜索范围:", self.search_scope_combo)
        
        layout.addWidget(quick_group)
        
        # 时间范围区域
        time_group = QGroupBox("时间范围")
        time_layout = QFormLayout(time_group)
        
        self.time_range_combo = QComboBox()
        self.time_range_combo.addItems([
            "不限制", "今天", "昨天", "本周", "上周", "本月", "上月", "自定义"
        ])
        time_layout.addRow("时间范围:", self.time_range_combo)
        
        # 自定义时间范围
        date_layout = QHBoxLayout()
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addDays(-30))
        self.start_date.setEnabled(False)
        date_layout.addWidget(QLabel("从:"))
        date_layout.addWidget(self.start_date)
        
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setEnabled(False)
        date_layout.addWidget(QLabel("到:"))
        date_layout.addWidget(self.end_date)
        
        time_layout.addRow("自定义日期:", date_layout)
        
        layout.addWidget(time_group)
        
        # 其他选项
        options_group = QGroupBox("其他选项")
        options_layout = QVBoxLayout(options_group)
        
        self.case_sensitive_cb = QCheckBox("区分大小写")
        options_layout.addWidget(self.case_sensitive_cb)
        
        self.whole_word_cb = QCheckBox("全词匹配")
        options_layout.addWidget(self.whole_word_cb)
        
        self.include_attachments_cb = QCheckBox("包含有附件的邮件")
        options_layout.addWidget(self.include_attachments_cb)
        
        layout.addWidget(options_group)
        
        layout.addStretch()
        return widget
    
    def create_advanced_search_tab(self):
        """创建高级搜索标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 详细条件区域
        conditions_group = QGroupBox("搜索条件")
        conditions_layout = QFormLayout(conditions_group)
        
        self.subject_edit = QLineEdit()
        self.subject_edit.setPlaceholderText("邮件主题包含...")
        conditions_layout.addRow("主题:", self.subject_edit)
        
        self.sender_edit = QLineEdit()
        self.sender_edit.setPlaceholderText("发件人邮箱或姓名...")
        conditions_layout.addRow("发件人:", self.sender_edit)
        
        self.recipient_edit = QLineEdit()
        self.recipient_edit.setPlaceholderText("收件人邮箱或姓名...")
        conditions_layout.addRow("收件人:", self.recipient_edit)
        
        self.content_edit = QTextEdit()
        self.content_edit.setPlaceholderText("邮件内容包含...")
        self.content_edit.setMaximumHeight(80)
        conditions_layout.addRow("内容:", self.content_edit)
        
        layout.addWidget(conditions_group)
        
        # 邮件属性区域
        properties_group = QGroupBox("邮件属性")
        properties_layout = QFormLayout(properties_group)
        
        self.status_combo = QComboBox()
        self.status_combo.addItems(["不限制", "仅未读", "仅已读"])
        properties_layout.addRow("阅读状态:", self.status_combo)
        
        self.folder_combo = QComboBox()
        self.folder_combo.addItems(["当前文件夹", "所有文件夹", "收件箱", "发件箱", "草稿箱"])
        properties_layout.addRow("搜索文件夹:", self.folder_combo)
        
        # 邮件大小
        size_layout = QHBoxLayout()
        self.size_min_spin = QSpinBox()
        self.size_min_spin.setRange(0, 999999)
        self.size_min_spin.setSuffix(" KB")
        size_layout.addWidget(self.size_min_spin)
        
        size_layout.addWidget(QLabel("到"))
        
        self.size_max_spin = QSpinBox()
        self.size_max_spin.setRange(0, 999999)
        self.size_max_spin.setValue(999999)
        self.size_max_spin.setSuffix(" KB")
        size_layout.addWidget(self.size_max_spin)
        
        properties_layout.addRow("邮件大小:", size_layout)
        
        layout.addWidget(properties_group)
        
        layout.addStretch()
        return widget
    
    def create_history_tab(self):
        """创建搜索历史标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 历史列表
        history_group = QGroupBox("搜索历史")
        history_layout = QVBoxLayout(history_group)
        
        self.history_list = QListWidget()
        history_layout.addWidget(self.history_list)
        
        # 历史操作按钮
        history_btn_layout = QHBoxLayout()
        
        self.load_history_btn = QPushButton("加载选中")
        history_btn_layout.addWidget(self.load_history_btn)
        
        self.delete_history_btn = QPushButton("删除选中")
        history_btn_layout.addWidget(self.delete_history_btn)
        
        self.clear_history_btn = QPushButton("清空历史")
        history_btn_layout.addWidget(self.clear_history_btn)
        
        history_btn_layout.addStretch()
        
        history_layout.addLayout(history_btn_layout)
        
        layout.addWidget(history_group)
        
        # 预设搜索
        presets_group = QGroupBox("预设搜索")
        presets_layout = QVBoxLayout(presets_group)
        
        preset_buttons = [
            ("今天的未读邮件", self.search_today_unread),
            ("本周的重要邮件", self.search_week_important),
            ("有附件的邮件", self.search_with_attachments),
            ("大邮件 (>1MB)", self.search_large_emails)
        ]
        
        for text, callback in preset_buttons:
            btn = QPushButton(text)
            btn.clicked.connect(callback)
            presets_layout.addWidget(btn)
        
        layout.addWidget(presets_group)
        
        return widget
    
    def setup_connections(self):
        """设置信号连接"""
        # 按钮连接
        self.search_btn.clicked.connect(self.perform_search)
        self.reset_btn.clicked.connect(self.reset_form)
        self.save_btn.clicked.connect(self.save_search)
        self.cancel_btn.clicked.connect(self.reject)
        
        # 时间范围变化
        self.time_range_combo.currentTextChanged.connect(self.on_time_range_changed)
        
        # 历史操作
        self.load_history_btn.clicked.connect(self.load_selected_history)
        self.delete_history_btn.clicked.connect(self.delete_selected_history)
        self.clear_history_btn.clicked.connect(self.clear_search_history)
        
        # 双击历史项目
        self.history_list.itemDoubleClicked.connect(self.load_selected_history)
    
    def on_time_range_changed(self, range_text):
        """时间范围变化处理"""
        is_custom = range_text == "自定义"
        self.start_date.setEnabled(is_custom)
        self.end_date.setEnabled(is_custom)
        
        if not is_custom:
            # 设置预定义时间范围
            today = QDate.currentDate()
            if range_text == "今天":
                self.start_date.setDate(today)
                self.end_date.setDate(today)
            elif range_text == "昨天":
                yesterday = today.addDays(-1)
                self.start_date.setDate(yesterday)
                self.end_date.setDate(yesterday)
            elif range_text == "本周":
                week_start = today.addDays(-today.dayOfWeek() + 1)
                self.start_date.setDate(week_start)
                self.end_date.setDate(today)
            elif range_text == "本月":
                month_start = QDate(today.year(), today.month(), 1)
                self.start_date.setDate(month_start)
                self.end_date.setDate(today)
    
    def get_search_criteria(self):
        """获取搜索条件"""
        criteria = {}
        
        # 基本搜索条件
        quick_text = self.quick_search_edit.text().strip()
        if quick_text:
            criteria['quick_search'] = quick_text
            criteria['search_scope'] = self.search_scope_combo.currentText()

        # 高级搜索条件（无论在哪个标签页都检查）
        if self.subject_edit.text().strip():
            criteria['subject'] = self.subject_edit.text().strip()
        if self.sender_edit.text().strip():
            criteria['sender'] = self.sender_edit.text().strip()
        if self.recipient_edit.text().strip():
            criteria['recipient'] = self.recipient_edit.text().strip()
        if self.content_edit.toPlainText().strip():
            criteria['content'] = self.content_edit.toPlainText().strip()
        
        # 时间范围
        time_range = self.time_range_combo.currentText()
        if time_range != "不限制":
            criteria['time_range'] = time_range
            if time_range == "自定义":
                criteria['start_date'] = self.start_date.date().toPython()
                criteria['end_date'] = self.end_date.date().toPython()
        
        # 其他选项
        criteria['case_sensitive'] = self.case_sensitive_cb.isChecked()
        criteria['whole_word'] = self.whole_word_cb.isChecked()
        criteria['include_attachments'] = self.include_attachments_cb.isChecked()
        
        # 高级选项
        if hasattr(self, 'status_combo'):
            status = self.status_combo.currentText()
            if status != "不限制":
                criteria['read_status'] = status
        
        return criteria
    
    def perform_search(self):
        """执行搜索"""
        criteria = self.get_search_criteria()
        
        if not any(criteria.values()):
            QMessageBox.warning(self, "提示", "请输入搜索条件")
            return
        
        # 保存到历史
        self.add_to_history(criteria)
        
        # 发送搜索信号
        self.search_requested.emit(criteria)
        
        # 关闭对话框
        self.accept()
    
    def reset_form(self):
        """重置表单"""
        # 清空所有输入
        self.quick_search_edit.clear()
        self.subject_edit.clear()
        self.sender_edit.clear()
        self.recipient_edit.clear()
        self.content_edit.clear()
        
        # 重置选择
        self.search_scope_combo.setCurrentIndex(0)
        self.time_range_combo.setCurrentIndex(0)
        self.status_combo.setCurrentIndex(0)
        self.folder_combo.setCurrentIndex(0)
        
        # 重置复选框
        self.case_sensitive_cb.setChecked(False)
        self.whole_word_cb.setChecked(False)
        self.include_attachments_cb.setChecked(False)
    
    def save_search(self):
        """保存搜索条件"""
        criteria = self.get_search_criteria()
        if not any(criteria.values()):
            QMessageBox.warning(self, "提示", "请输入搜索条件")
            return
        
        # 添加到历史并保存
        self.add_to_history(criteria)
        QMessageBox.information(self, "提示", "搜索条件已保存到历史")
    
    def add_to_history(self, criteria):
        """添加到搜索历史"""
        # 创建历史项目
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M")
        description = self.create_search_description(criteria)
        
        history_item = {
            'timestamp': timestamp,
            'description': description,
            'criteria': criteria
        }
        
        # 避免重复
        for item in self.search_history:
            if item['criteria'] == criteria:
                return
        
        self.search_history.insert(0, history_item)
        
        # 限制历史数量
        if len(self.search_history) > 20:
            self.search_history = self.search_history[:20]
        
        self.update_history_list()
    
    def create_search_description(self, criteria):
        """创建搜索描述"""
        parts = []
        
        if 'quick_search' in criteria:
            parts.append(f"关键词: {criteria['quick_search']}")
        if 'subject' in criteria:
            parts.append(f"主题: {criteria['subject']}")
        if 'sender' in criteria:
            parts.append(f"发件人: {criteria['sender']}")
        if 'time_range' in criteria and criteria['time_range'] != "不限制":
            parts.append(f"时间: {criteria['time_range']}")
        
        return "; ".join(parts) if parts else "搜索条件"
    
    def update_history_list(self):
        """更新历史列表"""
        self.history_list.clear()
        
        for item in self.search_history:
            list_item = QListWidgetItem()
            list_item.setText(f"{item['timestamp']} - {item['description']}")
            list_item.setData(Qt.ItemDataRole.UserRole, item)
            self.history_list.addItem(list_item)
    
    def load_selected_history(self):
        """加载选中的历史"""
        current_item = self.history_list.currentItem()
        if not current_item:
            return
        
        history_data = current_item.data(Qt.ItemDataRole.UserRole)
        criteria = history_data['criteria']
        
        # 加载搜索条件到表单
        self.load_criteria_to_form(criteria)
    
    def load_criteria_to_form(self, criteria):
        """将搜索条件加载到表单"""
        # 重置表单
        self.reset_form()
        
        # 加载条件
        if 'quick_search' in criteria:
            self.quick_search_edit.setText(criteria['quick_search'])
            self.tab_widget.setCurrentIndex(0)  # 切换到基本搜索
        
        if 'subject' in criteria:
            self.subject_edit.setText(criteria['subject'])
            self.tab_widget.setCurrentIndex(1)  # 切换到高级搜索
        
        if 'sender' in criteria:
            self.sender_edit.setText(criteria['sender'])
        
        if 'time_range' in criteria:
            time_range = criteria['time_range']
            index = self.time_range_combo.findText(time_range)
            if index >= 0:
                self.time_range_combo.setCurrentIndex(index)
    
    def delete_selected_history(self):
        """删除选中的历史"""
        current_row = self.history_list.currentRow()
        if current_row >= 0:
            del self.search_history[current_row]
            self.update_history_list()
    
    def clear_search_history(self):
        """清空搜索历史"""
        reply = QMessageBox.question(
            self, "确认", "确定要清空所有搜索历史吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.search_history.clear()
            self.update_history_list()
    
    def load_search_history(self):
        """加载搜索历史"""
        # 这里可以从文件或数据库加载历史
        # 暂时使用空列表
        self.update_history_list()
    
    # 预设搜索方法
    def search_today_unread(self):
        """搜索今天的未读邮件"""
        self.reset_form()
        self.time_range_combo.setCurrentText("今天")
        self.status_combo.setCurrentText("仅未读")
        self.tab_widget.setCurrentIndex(1)
    
    def search_week_important(self):
        """搜索本周的重要邮件"""
        self.reset_form()
        self.time_range_combo.setCurrentText("本周")
        self.subject_edit.setText("重要")
        self.tab_widget.setCurrentIndex(1)
    
    def search_with_attachments(self):
        """搜索有附件的邮件"""
        self.reset_form()
        self.include_attachments_cb.setChecked(True)
        self.tab_widget.setCurrentIndex(0)
    
    def search_large_emails(self):
        """搜索大邮件"""
        self.reset_form()
        self.size_min_spin.setValue(1024)  # 1MB
        self.tab_widget.setCurrentIndex(1)

if __name__ == "__main__":
    from PySide6.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    
    dialog = AdvancedSearchDialog()
    dialog.show()
    
    sys.exit(app.exec())
