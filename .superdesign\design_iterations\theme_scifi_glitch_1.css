:root {
  --background: oklch(0.05 0.01 260);
  --foreground: oklch(0.9 0.15 180);
  --card: oklch(0.1 0.02 260 / 0.7);
  --card-foreground: oklch(0.9 0.15 180);
  --popover: oklch(0.08 0.02 260);
  --popover-foreground: oklch(0.9 0.15 180);
  --primary: oklch(0.8 0.2 180);
  --primary-foreground: oklch(0.05 0.01 260);
  --secondary: oklch(0.2 0.02 260);
  --secondary-foreground: oklch(0.9 0.15 180);
  --muted: oklch(0.2 0.02 260);
  --muted-foreground: oklch(0.6 0.1 180);
  --accent: oklch(0.8 0.2 180);
  --accent-foreground: oklch(0.05 0.01 260);
  --destructive: oklch(0.7 0.25 5);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.4 0.15 180 / 0.3);
  --input: oklch(0.4 0.15 180 / 0.3);
  --ring: oklch(0.8 0.2 180);
  --chart-1: oklch(0.8 0.2 180);
  --chart-2: oklch(0.9 0.25 90);
  --chart-3: oklch(0.7 0.25 5);
  --chart-4: oklch(0.7 0.25 280);
  --chart-5: oklch(0.8 0.25 320);
  --sidebar: oklch(0.05 0.01 260);
  --sidebar-foreground: oklch(0.9 0.15 180);
  --sidebar-primary: oklch(0.8 0.2 180);
  --sidebar-primary-foreground: oklch(0.05 0.01 260);
  --sidebar-accent: oklch(0.8 0.2 180);
  --sidebar-accent-foreground: oklch(0.05 0.01 260);
  --sidebar-border: oklch(0.4 0.15 180 / 0.3);
  --sidebar-ring: oklch(0.8 0.2 180);
  --font-sans: 'Geist Mono', monospace;
  --font-serif: 'Geist Mono', monospace;
  --font-mono: 'Geist Mono', monospace;
  --radius: 0.25rem;
  --shadow-xl: 0 0 25px 0px oklch(from var(--primary) l a c / 0.25), 0 0 8px 0px oklch(from var(--primary) l a c / 0.3);
  --tracking-normal: 0.02em;
  --spacing: 0.25rem;
}
