#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量选择邮箱收件对话框
允许用户选择特定邮箱进行同步
"""

import logging
from typing import List, Dict, Set
from datetime import datetime, timezone

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QTableWidget, QTableWidgetItem, QCheckBox, QWidget,
    QHeaderView, QMessageBox, QProgressDialog, QGroupBox,
    QAbstractItemView
)
from PySide6.QtCore import Qt, QThread, Signal
from PySide6.QtGui import QFont

class SelectiveFetchDialog(QDialog):
    """批量选择邮箱收件对话框"""
    
    def __init__(self, real_account_manager, parent=None):
        super().__init__(parent)
        self.real_account_manager = real_account_manager
        self.logger = logging.getLogger(__name__)
        self.selected_accounts = set()  # 选中的账户ID集合
        
        self.setWindowTitle("选择邮箱同步")
        self.setMinimumSize(800, 500)
        self.resize(900, 600)
        
        self.setup_ui()
        self.load_accounts()
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("选择要同步的邮箱账户")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # 说明文字
        info_label = QLabel("请选择要进行邮件同步的账户，支持多选。只有启用的账户才会显示在列表中。")
        info_label.setStyleSheet("color: #666; margin-bottom: 10px;")
        layout.addWidget(info_label)
        
        # 操作按钮组
        button_group = QGroupBox("快捷操作")
        button_layout = QHBoxLayout(button_group)
        
        self.select_all_btn = QPushButton("✅ 全选")
        self.select_all_btn.clicked.connect(self.select_all)
        button_layout.addWidget(self.select_all_btn)
        
        self.select_none_btn = QPushButton("❌ 全不选")
        self.select_none_btn.clicked.connect(self.select_none)
        button_layout.addWidget(self.select_none_btn)
        
        button_layout.addStretch()
        
        self.refresh_btn = QPushButton("🔄 刷新")
        self.refresh_btn.clicked.connect(self.load_accounts)
        button_layout.addWidget(self.refresh_btn)
        
        layout.addWidget(button_group)
        
        # 账户表格
        self.accounts_table = QTableWidget()
        self.accounts_table.setColumnCount(6)
        self.accounts_table.setHorizontalHeaderLabels([
            "选择", "邮箱地址", "状态", "上次同步时间", "邮件数量", "连接状态"
        ])
        
        # 设置表格属性
        self.accounts_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.accounts_table.setAlternatingRowColors(True)
        self.accounts_table.verticalHeader().setVisible(False)
        
        # 设置列宽
        header = self.accounts_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # 选择列固定宽度
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # 邮箱地址自适应
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # 状态
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # 同步时间
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # 邮件数量
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # 连接状态
        
        self.accounts_table.setColumnWidth(0, 60)  # 选择列宽度
        
        layout.addWidget(self.accounts_table)
        
        # 底部按钮
        bottom_layout = QHBoxLayout()
        
        self.selected_count_label = QLabel("已选择: 0 个账户")
        self.selected_count_label.setStyleSheet("font-weight: bold; color: #2196F3;")
        bottom_layout.addWidget(self.selected_count_label)
        
        bottom_layout.addStretch()
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        bottom_layout.addWidget(self.cancel_btn)
        
        self.sync_btn = QPushButton("🚀 开始同步")
        self.sync_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                font-weight: bold;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        self.sync_btn.clicked.connect(self.start_sync)
        self.sync_btn.setEnabled(False)
        bottom_layout.addWidget(self.sync_btn)
        
        layout.addLayout(bottom_layout)
    
    def load_accounts(self):
        """加载账户数据"""
        try:
            # 🔄 实时获取最新的账户数据
            accounts = self.real_account_manager.get_enabled_accounts()

            self.logger.info(f"开始加载账户数据，总数: {len(accounts)}")
            for i, acc in enumerate(accounts):
                self.logger.info(f"账户 {i+1}: ID={acc.account_id}, Email={acc.email}, 启用状态: {acc.enabled}")

            # 清空现有数据
            self.accounts_table.setRowCount(0)
            self.selected_accounts.clear()

            # 设置新的行数
            self.accounts_table.setRowCount(len(accounts))
            
            for row, account_config in enumerate(accounts):
                # 选择复选框
                checkbox_widget = QWidget()
                checkbox_layout = QHBoxLayout(checkbox_widget)
                checkbox_layout.setContentsMargins(0, 0, 0, 0)
                checkbox_layout.setAlignment(Qt.AlignCenter)
                
                checkbox = QCheckBox()
                checkbox.setProperty("account_id", account_config.account_id)
                checkbox.stateChanged.connect(self.on_checkbox_changed)
                checkbox_layout.addWidget(checkbox)

                # 添加调试信息
                self.logger.info(f"创建复选框: account_id={account_config.account_id}, email={account_config.email}")
                
                self.accounts_table.setCellWidget(row, 0, checkbox_widget)
                
                # 邮箱地址
                email_item = QTableWidgetItem(account_config.email)
                email_item.setFlags(email_item.flags() & ~Qt.ItemIsEditable)
                self.accounts_table.setItem(row, 1, email_item)
                
                # 状态
                status_text = "🟢 启用" if account_config.enabled else "🔴 禁用"
                status_item = QTableWidgetItem(status_text)
                status_item.setFlags(status_item.flags() & ~Qt.ItemIsEditable)
                self.accounts_table.setItem(row, 2, status_item)
                
                # 上次同步时间
                sync_state = self.real_account_manager.database.get_account_sync_state(account_config.account_id)
                if sync_state and sync_state.last_incremental_sync_time:
                    sync_time = sync_state.last_incremental_sync_time
                    if hasattr(sync_time, 'strftime'):
                        sync_text = sync_time.strftime("%Y-%m-%d %H:%M")
                    else:
                        sync_text = str(sync_time)
                else:
                    sync_text = "从未同步"
                
                sync_item = QTableWidgetItem(sync_text)
                sync_item.setFlags(sync_item.flags() & ~Qt.ItemIsEditable)
                self.accounts_table.setItem(row, 3, sync_item)
                
                # 邮件数量
                email_count = self.real_account_manager.database.get_folder_email_count(
                    account_config.account_id, "INBOX"
                )
                count_item = QTableWidgetItem(str(email_count))
                count_item.setFlags(count_item.flags() & ~Qt.ItemIsEditable)
                self.accounts_table.setItem(row, 4, count_item)
                
                # 连接状态（简化显示）
                connection_item = QTableWidgetItem("🔗 就绪")
                connection_item.setFlags(connection_item.flags() & ~Qt.ItemIsEditable)
                self.accounts_table.setItem(row, 5, connection_item)
            
            self.update_selection_count()
            
        except Exception as e:
            self.logger.error(f"加载账户数据失败: {e}")
            QMessageBox.warning(self, "错误", f"加载账户数据失败: {e}")

    def refresh_accounts_data(self):
        """刷新账户数据（公共方法，供外部调用）"""
        try:
            self.logger.info("刷新批量选择对话框中的账户数据")

            # 重新加载账户数据
            self.load_accounts()

            # 更新选择计数
            self.update_selection_count()

            self.logger.info("账户数据刷新完成")

        except Exception as e:
            self.logger.error(f"刷新账户数据失败: {e}")
            QMessageBox.warning(self, "错误", f"刷新账户数据失败: {e}")

    def on_checkbox_changed(self, state):
        """复选框状态变更"""
        try:
            checkbox = self.sender()
            account_id = checkbox.property("account_id")

            # 添加详细调试信息
            self.logger.info(f"复选框状态变更: account_id={account_id}")
            self.logger.info(f"状态值: state={state}, type={type(state)}")
            self.logger.info(f"Qt常量: Qt.Checked={Qt.Checked}, Qt.Unchecked={Qt.Unchecked}")

            if not account_id:
                self.logger.warning("复选框没有设置account_id属性")
                return

            # 修复状态判断逻辑：正确处理Qt枚举类型
            # 将状态转换为整数进行比较，或直接与Qt枚举比较
            if state == Qt.Checked or (isinstance(state, int) and state == 2):  # 选中状态
                self.selected_accounts.add(account_id)
                self.logger.info(f"✅ 添加账户到选择列表: {account_id}")
            else:  # 未选中状态
                self.selected_accounts.discard(account_id)
                self.logger.info(f"❌ 从选择列表移除账户: {account_id}")

            self.logger.info(f"当前选择的账户数量: {len(self.selected_accounts)}")
            self.logger.info(f"当前选择的账户列表: {list(self.selected_accounts)}")
            self.update_selection_count()

        except Exception as e:
            self.logger.error(f"处理复选框变更失败: {e}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")
    
    def select_all(self):
        """全选"""
        try:
            self.logger.info("执行全选操作")
            for row in range(self.accounts_table.rowCount()):
                checkbox_widget = self.accounts_table.cellWidget(row, 0)
                if checkbox_widget:
                    checkbox = checkbox_widget.findChild(QCheckBox)
                    if checkbox:
                        self.logger.info(f"设置第 {row+1} 行复选框为选中状态")
                        checkbox.setChecked(True)
            self.logger.info(f"全选完成，当前选择数量: {len(self.selected_accounts)}")
        except Exception as e:
            self.logger.error(f"全选失败: {e}")

    def select_none(self):
        """全不选"""
        try:
            self.logger.info("执行全不选操作")
            for row in range(self.accounts_table.rowCount()):
                checkbox_widget = self.accounts_table.cellWidget(row, 0)
                if checkbox_widget:
                    checkbox = checkbox_widget.findChild(QCheckBox)
                    if checkbox:
                        self.logger.info(f"设置第 {row+1} 行复选框为未选中状态")
                        checkbox.setChecked(False)
            self.logger.info(f"全不选完成，当前选择数量: {len(self.selected_accounts)}")
        except Exception as e:
            self.logger.error(f"全不选失败: {e}")
    
    def update_selection_count(self):
        """更新选择计数"""
        count = len(self.selected_accounts)
        self.selected_count_label.setText(f"已选择: {count} 个账户")
        self.sync_btn.setEnabled(count > 0)

        # 添加调试信息
        self.logger.info(f"更新选择计数: {count} 个账户, 同步按钮启用: {count > 0}")
        if count > 0:
            self.logger.info(f"选中的账户列表: {list(self.selected_accounts)}")
    
    def get_selected_accounts(self) -> List[str]:
        """获取选中的账户ID列表"""
        return list(self.selected_accounts)
    
    def start_sync(self):
        """开始同步"""
        try:
            self.logger.info(f"开始同步按钮被点击，当前选择的账户: {list(self.selected_accounts)}")

            if not self.selected_accounts:
                self.logger.warning("没有选择任何账户")
                QMessageBox.information(self, "提示", "请至少选择一个账户进行同步。")
                return

            # 确认对话框
            count = len(self.selected_accounts)
            self.logger.info(f"显示确认对话框，选中账户数量: {count}")

            reply = QMessageBox.question(
                self, "确认同步",
                f"即将同步 {count} 个选中的邮箱账户:\n\n" +
                "\n".join([f"• {acc_id}" for acc_id in self.selected_accounts]) +
                f"\n\n这可能需要几分钟时间，是否继续？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.Yes
            )

            if reply == QMessageBox.StandardButton.Yes:
                self.logger.info("用户确认开始同步，关闭对话框")
                self.accept()
            else:
                self.logger.info("用户取消同步")

        except Exception as e:
            self.logger.error(f"开始同步失败: {e}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")
