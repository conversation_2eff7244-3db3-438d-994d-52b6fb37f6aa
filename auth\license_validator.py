#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
许可证网络验证客户端模块
负责与远程服务器进行卡密验证通信
"""

import requests
import json
import hashlib
import platform
import urllib3
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Tuple
import time

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


class LicenseValidator:
    """许可证网络验证客户端"""
    
    def __init__(self, api_base_url: str = "https://ka.915277.xyz/api/v1"):
        """
        初始化验证客户端
        
        Args:
            api_base_url: API服务器基础URL
        """
        self.api_base_url = api_base_url.rstrip('/')
        self.timeout = 30  # 请求超时时间（秒）
        self.max_retries = 3  # 最大重试次数
        self.retry_delay = 2  # 重试延迟（秒）
    
    def _get_machine_fingerprint(self) -> str:
        """获取机器指纹"""
        try:
            # 收集机器信息
            machine_info = {
                'platform': platform.platform(),
                'processor': platform.processor(),
                'machine': platform.machine(),
                'node': platform.node(),
                'system': platform.system(),
                'release': platform.release()
            }
            
            # 生成指纹
            info_str = json.dumps(machine_info, sort_keys=True)
            fingerprint = hashlib.sha256(info_str.encode('utf-8')).hexdigest()
            return fingerprint[:32]  # 取前32位
            
        except Exception:
            return "unknown-fingerprint"
    
    def _make_request(self, endpoint: str, data: Dict[str, Any], method: str = 'POST') -> Tuple[bool, Dict[str, Any]]:
        """
        发送HTTP请求
        
        Args:
            endpoint: API端点
            data: 请求数据
            method: HTTP方法
            
        Returns:
            Tuple[bool, Dict]: (成功标志, 响应数据)
        """
        url = f"{self.api_base_url}/{endpoint.lstrip('/')}"
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'EnterpriseEmailManager/2.1.0',
            'Accept': 'application/json'
        }
        
        for attempt in range(self.max_retries):
            try:
                if method.upper() == 'POST':
                    response = requests.post(
                        url,
                        json=data,
                        headers=headers,
                        timeout=self.timeout,
                        verify=False  # 临时禁用SSL验证
                    )
                elif method.upper() == 'GET':
                    response = requests.get(
                        url,
                        params=data if data else None,
                        headers=headers,
                        timeout=self.timeout,
                        verify=False  # 临时禁用SSL验证
                    )
                else:
                    return False, {'error': f'不支持的HTTP方法: {method}'}
                
                # 检查响应状态
                if response.status_code == 200:
                    try:
                        result = response.json()
                        # 适配新的API响应格式
                        if result.get('success', False):
                            return True, result.get('data', result)
                        else:
                            return False, {'error': result.get('message', '验证失败')}
                    except json.JSONDecodeError:
                        return False, {'error': '服务器响应格式错误'}
                
                elif response.status_code == 401:
                    return False, {'error': '无效的许可证密钥'}
                
                elif response.status_code == 403:
                    return False, {'error': '许可证已被禁用或过期'}
                
                elif response.status_code == 429:
                    return False, {'error': '请求过于频繁，请稍后重试'}
                
                else:
                    return False, {'error': f'服务器错误: {response.status_code}'}
                
            except requests.exceptions.Timeout:
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay)
                    continue
                return False, {'error': '网络请求超时'}
            
            except requests.exceptions.ConnectionError:
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay)
                    continue
                return False, {'error': '无法连接到验证服务器'}
            
            except Exception as e:
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay)
                    continue
                return False, {'error': f'网络请求失败: {str(e)}'}
        
        return False, {'error': '网络请求失败，已达到最大重试次数'}
    
    def validate_license(self, license_key: str) -> Tuple[bool, Dict[str, Any]]:
        """
        验证许可证密钥
        
        Args:
            license_key: 许可证密钥
            
        Returns:
            Tuple[bool, Dict]: (验证是否成功, 验证结果数据)
        """
        if not license_key or not license_key.strip():
            return False, {'error': '许可证密钥不能为空'}
        
        # 准备验证数据（简化版）
        validation_data = {
            'license_key': license_key.strip(),
            'timestamp': datetime.now().isoformat(),
        }
        
        # 发送验证请求
        success, response = self._make_request('validate', validation_data)
        
        if success:
            # 验证成功，解析响应数据（简化版）
            if response.get('valid', False):
                return True, {
                    'status': response.get('status', 'active'),
                    'license_key': license_key,
                    'expiry_time': response.get('expires_at'),
                    'validation_time': datetime.now().isoformat()
                }
            else:
                return False, {
                    'error': response.get('message', '密钥验证失败'),
                    'reason': response.get('reason', 'unknown')
                }
        else:
            return False, response
    
    def check_license_status(self, license_key: str) -> Tuple[bool, Dict[str, Any]]:
        """
        检查许可证状态（不进行完整验证）
        
        Args:
            license_key: 许可证密钥
            
        Returns:
            Tuple[bool, Dict]: (检查是否成功, 状态数据)
        """
        if not license_key or not license_key.strip():
            return False, {'error': '许可证密钥不能为空'}
        
        # 准备检查数据
        check_data = {
            'license_key': license_key.strip(),
            'action': 'status_check'
        }
        
        # 发送状态检查请求
        success, response = self._make_request('status', check_data, method='GET')
        
        if success:
            return True, {
                'status': response.get('status', 'unknown'),
                'expiry_time': response.get('expires_at'),
            }
        else:
            return False, response
    

    

    
    def test_connection(self) -> Tuple[bool, str]:
        """
        测试与验证服务器的连接
        
        Returns:
            Tuple[bool, str]: (连接是否成功, 结果消息)
        """
        try:
            # ping端点不需要数据参数
            success, response = self._make_request('ping', None, method='GET')

            if success:
                # ping端点成功返回数据就表示连接正常
                if 'api_version' in response or 'server_time' in response:
                    return True, '连接验证服务器成功'
                else:
                    return False, response.get('message', '连接验证服务器失败')
            else:
                return False, response.get('error', '连接验证服务器失败')
                
        except Exception as e:
            return False, f'连接测试异常: {str(e)}'
