#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IMAP命令优化模块
提供高效的IMAP命令使用和批量处理机制
"""

import time
import logging
import re
from typing import List, Dict, Optional, Tuple, Set
from enum import Enum
from dataclasses import dataclass
import threading

class IMAPCommand(Enum):
    """IMAP命令枚举"""
    SEARCH = "SEARCH"
    FETCH = "FETCH"
    SELECT = "SELECT"
    LIST = "LIST"
    STATUS = "STATUS"
    UID = "UID"

@dataclass
class CommandOptimization:
    """命令优化配置"""
    use_uid: bool = True                    # 使用UID命令
    batch_fetch: bool = True                # 批量获取
    pipeline_commands: bool = True          # 命令流水线
    compress_responses: bool = False        # 响应压缩
    max_batch_size: int = 100              # 最大批量大小
    command_timeout: int = 30              # 命令超时
    retry_count: int = 3                   # 重试次数

class IMAPOptimizer:
    """IMAP命令优化器"""
    
    def __init__(self, optimization: CommandOptimization = None):
        self.optimization = optimization or CommandOptimization()
        self.logger = logging.getLogger(__name__)
        self.command_stats = {}
        self.lock = threading.RLock()
        
        # 命令模板
        self.fetch_templates = {
            'headers': '(UID FLAGS RFC822.SIZE ENVELOPE INTERNALDATE)',
            'structure': '(UID BODYSTRUCTURE)',
            'text_only': '(UID BODY[TEXT])',
            'full': '(RFC822)',
            'partial': '(UID BODY.PEEK[HEADER] BODY.PEEK[TEXT]<0.1024>)'
        }
        
        # 搜索优化
        self.search_optimizations = {
            'recent': 'RECENT',
            'unseen': 'UNSEEN',
            'since_date': 'SINCE {}',
            'before_date': 'BEFORE {}',
            'size_larger': 'LARGER {}',
            'size_smaller': 'SMALLER {}',
            'from_sender': 'FROM "{}"',
            'subject_contains': 'SUBJECT "{}"'
        }
    
    def optimize_search_command(self, criteria: Dict[str, any]) -> str:
        """优化搜索命令"""
        try:
            search_parts = []
            
            # 基本搜索条件
            if criteria.get('all', False):
                search_parts.append('ALL')
            
            if criteria.get('recent', False):
                search_parts.append('RECENT')
            
            if criteria.get('unseen', False):
                search_parts.append('UNSEEN')
            
            # 日期范围
            if 'since_date' in criteria:
                date_str = self._format_imap_date(criteria['since_date'])
                search_parts.append(f'SINCE {date_str}')
            
            if 'before_date' in criteria:
                date_str = self._format_imap_date(criteria['before_date'])
                search_parts.append(f'BEFORE {date_str}')
            
            # 大小限制
            if 'min_size' in criteria:
                search_parts.append(f'LARGER {criteria["min_size"]}')
            
            if 'max_size' in criteria:
                search_parts.append(f'SMALLER {criteria["max_size"]}')
            
            # 发件人和主题
            if 'from_sender' in criteria:
                search_parts.append(f'FROM "{criteria["from_sender"]}"')
            
            if 'subject_contains' in criteria:
                search_parts.append(f'SUBJECT "{criteria["subject_contains"]}"')
            
            # 组合搜索条件
            if not search_parts:
                search_parts.append('ALL')
            
            search_command = ' '.join(search_parts)
            
            # 使用UID搜索
            if self.optimization.use_uid:
                return f'UID SEARCH {search_command}'
            else:
                return f'SEARCH {search_command}'
                
        except Exception as e:
            self.logger.error(f"优化搜索命令失败: {e}")
            return 'SEARCH ALL'
    
    def optimize_fetch_command(self, message_ids: List[int], fetch_type: str = 'headers') -> List[str]:
        """优化获取命令"""
        try:
            commands = []
            
            # 获取模板
            template = self.fetch_templates.get(fetch_type, self.fetch_templates['headers'])
            
            if self.optimization.batch_fetch and len(message_ids) > 1:
                # 批量获取
                batch_size = min(self.optimization.max_batch_size, len(message_ids))
                
                for i in range(0, len(message_ids), batch_size):
                    batch_ids = message_ids[i:i + batch_size]
                    id_range = self._format_id_range(batch_ids)
                    
                    if self.optimization.use_uid:
                        command = f'UID FETCH {id_range} {template}'
                    else:
                        command = f'FETCH {id_range} {template}'
                    
                    commands.append(command)
            else:
                # 单个获取
                for msg_id in message_ids:
                    if self.optimization.use_uid:
                        command = f'UID FETCH {msg_id} {template}'
                    else:
                        command = f'FETCH {msg_id} {template}'
                    commands.append(command)
            
            return commands
            
        except Exception as e:
            self.logger.error(f"优化获取命令失败: {e}")
            return []
    
    def _format_id_range(self, message_ids: List[int]) -> str:
        """格式化消息ID范围"""
        try:
            if not message_ids:
                return ""
            
            if len(message_ids) == 1:
                return str(message_ids[0])
            
            # 排序ID
            sorted_ids = sorted(message_ids)
            
            # 查找连续范围
            ranges = []
            start = sorted_ids[0]
            end = start
            
            for i in range(1, len(sorted_ids)):
                if sorted_ids[i] == end + 1:
                    end = sorted_ids[i]
                else:
                    # 添加当前范围
                    if start == end:
                        ranges.append(str(start))
                    else:
                        ranges.append(f"{start}:{end}")
                    
                    start = sorted_ids[i]
                    end = start
            
            # 添加最后一个范围
            if start == end:
                ranges.append(str(start))
            else:
                ranges.append(f"{start}:{end}")
            
            return ','.join(ranges)
            
        except Exception as e:
            self.logger.error(f"格式化ID范围失败: {e}")
            return ','.join(map(str, message_ids))
    
    def _format_imap_date(self, date_obj) -> str:
        """格式化IMAP日期"""
        try:
            if hasattr(date_obj, 'strftime'):
                return date_obj.strftime('%d-%b-%Y')
            else:
                # 假设是时间戳
                import datetime
                dt = datetime.datetime.fromtimestamp(date_obj)
                return dt.strftime('%d-%b-%Y')
        except Exception as e:
            self.logger.error(f"格式化日期失败: {e}")
            return "01-Jan-2020"
    
    def execute_optimized_command(self, client, command: str, timeout: int = None) -> Tuple[bool, any]:
        """执行优化的命令"""
        timeout = timeout or self.optimization.command_timeout
        start_time = time.time()
        
        try:
            # 记录命令统计
            self._record_command_start(command)
            
            # 执行命令
            if hasattr(client, 'imap') and client.imap:
                # 解析命令
                parts = command.split(' ', 2)
                if len(parts) >= 2:
                    if parts[0] == 'UID':
                        # UID命令
                        cmd = parts[1]
                        args = parts[2] if len(parts) > 2 else ''
                        result, data = client.imap.uid(cmd.lower(), args)
                    else:
                        # 普通命令
                        cmd = parts[0]
                        args = ' '.join(parts[1:]) if len(parts) > 1 else ''
                        
                        if cmd == 'SEARCH':
                            result, data = client.imap.search(None, args)
                        elif cmd == 'FETCH':
                            fetch_parts = args.split(' ', 1)
                            if len(fetch_parts) == 2:
                                result, data = client.imap.fetch(fetch_parts[0], fetch_parts[1])
                            else:
                                result, data = client.imap.fetch(args, '(FLAGS)')
                        elif cmd == 'SELECT':
                            result, data = client.imap.select(args)
                        elif cmd == 'LIST':
                            list_parts = args.split(' ', 1)
                            if len(list_parts) == 2:
                                result, data = client.imap.list(list_parts[0], list_parts[1])
                            else:
                                result, data = client.imap.list()
                        else:
                            # 通用命令执行
                            result, data = ('NO', [b'Unsupported command'])
                    
                    # 记录命令完成
                    execution_time = time.time() - start_time
                    self._record_command_complete(command, result == 'OK', execution_time)
                    
                    return result == 'OK', data
                else:
                    return False, None
            else:
                return False, None
                
        except Exception as e:
            execution_time = time.time() - start_time
            self._record_command_complete(command, False, execution_time)
            self.logger.error(f"执行命令失败: {command}, 错误: {e}")
            return False, None
    
    def execute_pipeline_commands(self, client, commands: List[str]) -> List[Tuple[bool, any]]:
        """执行流水线命令"""
        if not self.optimization.pipeline_commands or len(commands) <= 1:
            # 串行执行
            results = []
            for command in commands:
                result = self.execute_optimized_command(client, command)
                results.append(result)
            return results
        
        # 流水线执行（简化实现）
        results = []
        for command in commands:
            result = self.execute_optimized_command(client, command)
            results.append(result)
        
        return results
    
    def _record_command_start(self, command: str):
        """记录命令开始"""
        with self.lock:
            cmd_type = command.split()[0]
            if cmd_type not in self.command_stats:
                self.command_stats[cmd_type] = {
                    'count': 0,
                    'success_count': 0,
                    'total_time': 0,
                    'avg_time': 0
                }
            self.command_stats[cmd_type]['count'] += 1
    
    def _record_command_complete(self, command: str, success: bool, execution_time: float):
        """记录命令完成"""
        with self.lock:
            cmd_type = command.split()[0]
            if cmd_type in self.command_stats:
                stats = self.command_stats[cmd_type]
                if success:
                    stats['success_count'] += 1
                stats['total_time'] += execution_time
                stats['avg_time'] = stats['total_time'] / stats['count']
    
    def get_command_statistics(self) -> Dict[str, Dict]:
        """获取命令统计"""
        with self.lock:
            return self.command_stats.copy()
    
    def optimize_folder_selection(self, folder_name: str) -> str:
        """优化文件夹选择"""
        try:
            # 处理特殊字符
            if ' ' in folder_name or '"' in folder_name:
                folder_name = f'"{folder_name}"'
            
            return f'SELECT {folder_name}'
            
        except Exception as e:
            self.logger.error(f"优化文件夹选择失败: {e}")
            return f'SELECT {folder_name}'
    
    def create_incremental_search(self, last_uid: int = None, last_modseq: int = None) -> str:
        """创建增量搜索命令"""
        try:
            search_parts = []
            
            if last_uid:
                search_parts.append(f'UID {last_uid + 1}:*')
            
            if last_modseq:
                search_parts.append(f'MODSEQ {last_modseq + 1}')
            
            if not search_parts:
                search_parts.append('ALL')
            
            search_command = ' '.join(search_parts)
            
            if self.optimization.use_uid:
                return f'UID SEARCH {search_command}'
            else:
                return f'SEARCH {search_command}'
                
        except Exception as e:
            self.logger.error(f"创建增量搜索失败: {e}")
            return 'UID SEARCH ALL'

def main():
    """测试函数"""
    print("⚡ IMAP命令优化器测试")
    print("=" * 50)
    
    # 创建优化器
    optimization = CommandOptimization(
        use_uid=True,
        batch_fetch=True,
        max_batch_size=50
    )
    
    optimizer = IMAPOptimizer(optimization)
    
    # 测试搜索命令优化
    print("\n🔍 搜索命令优化测试:")
    search_criteria = {
        'recent': True,
        'min_size': 1024,
        'from_sender': '<EMAIL>'
    }
    
    search_cmd = optimizer.optimize_search_command(search_criteria)
    print(f"   优化后的搜索命令: {search_cmd}")
    
    # 测试获取命令优化
    print("\n📥 获取命令优化测试:")
    message_ids = [1, 2, 3, 5, 6, 7, 10, 11, 12]
    fetch_commands = optimizer.optimize_fetch_command(message_ids, 'headers')
    
    print(f"   消息ID: {message_ids}")
    print(f"   优化后的获取命令:")
    for i, cmd in enumerate(fetch_commands, 1):
        print(f"     {i}. {cmd}")
    
    # 测试ID范围格式化
    print(f"\n📋 ID范围格式化测试:")
    id_range = optimizer._format_id_range(message_ids)
    print(f"   格式化范围: {id_range}")
    
    # 测试增量搜索
    print(f"\n🔄 增量搜索测试:")
    incremental_cmd = optimizer.create_incremental_search(last_uid=100, last_modseq=500)
    print(f"   增量搜索命令: {incremental_cmd}")

if __name__ == "__main__":
    main()
