#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件列表视图
实现类似Outlook的邮件列表界面，支持排序、筛选和预览
"""

import json
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from dataclasses import dataclass, asdict
from production_optimized_v2 import ProductionOptimizedClientV2, EmailContent, EmailHeader

@dataclass
class EmailListItem:
    """邮件列表项"""
    uid: int
    subject: str
    sender: str
    sender_name: str
    sender_email: str
    date: datetime
    size: int
    flags: List[str]
    has_attachments: bool
    preview_text: str
    importance: str = "normal"
    
    @property
    def is_read(self) -> bool:
        return "\\Seen" in self.flags
    
    @property
    def is_flagged(self) -> bool:
        return "\\Flagged" in self.flags
    
    @property
    def is_recent(self) -> bool:
        return "\\Recent" in self.flags

@dataclass
class EmailListConfig:
    """邮件列表配置"""
    items_per_page: int = 50
    show_preview: bool = True
    preview_length: int = 100
    sort_by: str = "date"  # date, sender, subject, size
    sort_order: str = "desc"  # asc, desc
    group_by_date: bool = True
    compact_view: bool = False

class EmailListView:
    """邮件列表视图"""
    
    def __init__(self, config: EmailListConfig = None):
        self.config = config or EmailListConfig()
        self.logger = logging.getLogger(__name__)
        
    def create_email_list(self, emails: List[EmailContent], folder_name: str = "收件箱") -> str:
        """创建邮件列表HTML"""
        try:
            # 转换为列表项
            list_items = self._convert_to_list_items(emails)
            
            # 排序和分组
            sorted_items = self._sort_emails(list_items)
            grouped_items = self._group_emails(sorted_items) if self.config.group_by_date else {"所有邮件": sorted_items}
            
            # 生成HTML
            list_html = self._generate_list_html(grouped_items, folder_name)
            
            # 包装在完整页面中
            full_page = self._wrap_in_list_template(list_html, folder_name, len(emails))
            
            return full_page
            
        except Exception as e:
            self.logger.error(f"创建邮件列表失败: {e}")
            return self._create_error_list(str(e))
    
    def _convert_to_list_items(self, emails: List[EmailContent]) -> List[EmailListItem]:
        """转换邮件为列表项"""
        try:
            list_items = []
            
            for email in emails:
                # 解析发件人信息
                sender_name, sender_email = self._parse_sender(email.header.sender)
                
                # 生成预览文本
                preview_text = self._generate_preview_text(email)
                
                # 检查是否有附件
                has_attachments = bool(email.attachments and 
                                     any(not att.get('is_inline', False) for att in email.attachments))
                
                # 确定重要性
                importance = self._determine_importance(email.header.flags)
                
                list_item = EmailListItem(
                    uid=email.header.uid,
                    subject=email.header.subject or "(无主题)",
                    sender=email.header.sender,
                    sender_name=sender_name,
                    sender_email=sender_email,
                    date=email.header.date,
                    size=email.header.size,
                    flags=email.header.flags,
                    has_attachments=has_attachments,
                    preview_text=preview_text,
                    importance=importance
                )
                
                list_items.append(list_item)
            
            return list_items
            
        except Exception as e:
            self.logger.error(f"转换邮件列表项失败: {e}")
            return []
    
    def _parse_sender(self, sender: str) -> tuple:
        """解析发件人信息"""
        try:
            if '<' in sender and '>' in sender:
                name_part = sender.split('<')[0].strip()
                email_part = sender.split('<')[1].split('>')[0].strip()
                return name_part or email_part, email_part
            else:
                return sender, sender
        except:
            return sender, sender
    
    def _generate_preview_text(self, email: EmailContent) -> str:
        """生成预览文本"""
        try:
            # 优先使用文本正文
            if email.text_body:
                text = email.text_body.strip()
            elif email.html_body:
                # 从HTML中提取文本
                import re
                text = re.sub(r'<[^>]+>', '', email.html_body)
                text = text.strip()
            else:
                return ""
            
            # 清理文本
            text = ' '.join(text.split())  # 移除多余空白
            
            # 截取预览长度
            if len(text) > self.config.preview_length:
                return text[:self.config.preview_length] + "..."
            else:
                return text
                
        except Exception as e:
            self.logger.error(f"生成预览文本失败: {e}")
            return ""
    
    def _determine_importance(self, flags: List[str]) -> str:
        """确定邮件重要性"""
        try:
            if "\\Important" in flags or "\\Flagged" in flags:
                return "high"
            elif "\\Low" in flags:
                return "low"
            else:
                return "normal"
        except:
            return "normal"
    
    def _sort_emails(self, emails: List[EmailListItem]) -> List[EmailListItem]:
        """排序邮件"""
        try:
            reverse = self.config.sort_order == "desc"
            
            if self.config.sort_by == "date":
                return sorted(emails, key=lambda x: x.date, reverse=reverse)
            elif self.config.sort_by == "sender":
                return sorted(emails, key=lambda x: x.sender_name.lower(), reverse=reverse)
            elif self.config.sort_by == "subject":
                return sorted(emails, key=lambda x: x.subject.lower(), reverse=reverse)
            elif self.config.sort_by == "size":
                return sorted(emails, key=lambda x: x.size, reverse=reverse)
            else:
                return emails
                
        except Exception as e:
            self.logger.error(f"排序邮件失败: {e}")
            return emails
    
    def _group_emails(self, emails: List[EmailListItem]) -> Dict[str, List[EmailListItem]]:
        """按日期分组邮件"""
        try:
            groups = {}
            now = datetime.now()
            
            for email in emails:
                # 计算日期分组
                if hasattr(email.date, 'date'):
                    email_date = email.date.date()
                    today = now.date()
                    
                    if email_date == today:
                        group_name = "今天"
                    elif email_date == today - timedelta(days=1):
                        group_name = "昨天"
                    elif (today - email_date).days < 7:
                        weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
                        group_name = weekdays[email_date.weekday()]
                    elif email_date.year == today.year:
                        group_name = email_date.strftime('%m月%d日')
                    else:
                        group_name = email_date.strftime('%Y年%m月')
                else:
                    group_name = "未知日期"
                
                if group_name not in groups:
                    groups[group_name] = []
                groups[group_name].append(email)
            
            return groups
            
        except Exception as e:
            self.logger.error(f"分组邮件失败: {e}")
            return {"所有邮件": emails}
    
    def _generate_list_html(self, grouped_emails: Dict[str, List[EmailListItem]], folder_name: str) -> str:
        """生成列表HTML"""
        try:
            html_parts = []
            
            for group_name, emails in grouped_emails.items():
                if not emails:
                    continue
                
                # 分组标题
                if len(grouped_emails) > 1:
                    html_parts.append(f"""
                    <div class="email-group-header">
                        <h3 class="group-title">{group_name} ({len(emails)})</h3>
                    </div>
                    """)
                
                # 邮件列表
                for email in emails:
                    email_html = self._generate_email_item_html(email)
                    html_parts.append(email_html)
            
            return ''.join(html_parts)
            
        except Exception as e:
            self.logger.error(f"生成列表HTML失败: {e}")
            return f"<div class='error'>列表生成失败: {e}</div>"
    
    def _generate_email_item_html(self, email: EmailListItem) -> str:
        """生成单个邮件项HTML"""
        try:
            # CSS类名
            css_classes = ["email-list-item"]
            if not email.is_read:
                css_classes.append("unread")
            if email.is_flagged:
                css_classes.append("flagged")
            if email.importance == "high":
                css_classes.append("important")
            if self.config.compact_view:
                css_classes.append("compact")
            
            # 状态指示器
            status_indicators = self._generate_status_indicators(email)
            
            # 格式化日期
            formatted_date = self._format_list_date(email.date)
            
            # 格式化大小
            formatted_size = self._format_size(email.size)
            
            return f"""
            <div class="{' '.join(css_classes)}" data-uid="{email.uid}" onclick="openEmail({email.uid})">
                <div class="email-item-content">
                    <div class="email-item-header">
                        <div class="sender-info">
                            <div class="sender-avatar">
                                {self._get_sender_avatar(email.sender_name)}
                            </div>
                            <div class="sender-details">
                                <span class="sender-name">{self._escape_html(email.sender_name)}</span>
                                <span class="sender-email">{self._escape_html(email.sender_email)}</span>
                            </div>
                        </div>
                        <div class="email-meta">
                            <span class="email-date">{formatted_date}</span>
                            <span class="email-size">{formatted_size}</span>
                            {status_indicators}
                        </div>
                    </div>
                    
                    <div class="email-item-body">
                        <div class="subject-line">
                            <span class="email-subject">{self._escape_html(email.subject)}</span>
                        </div>
                        {self._generate_preview_html(email) if self.config.show_preview else ''}
                    </div>
                </div>
            </div>
            """
            
        except Exception as e:
            self.logger.error(f"生成邮件项HTML失败: {e}")
            return f"<div class='error'>邮件项生成失败: {e}</div>"
    
    def _generate_status_indicators(self, email: EmailListItem) -> str:
        """生成状态指示器"""
        try:
            indicators = []
            
            if not email.is_read:
                indicators.append('<span class="status-indicator unread-dot" title="未读">●</span>')
            
            if email.is_flagged:
                indicators.append('<span class="status-indicator flag-icon" title="已标记">🚩</span>')
            
            if email.has_attachments:
                indicators.append('<span class="status-indicator attachment-icon" title="有附件">📎</span>')
            
            if email.importance == "high":
                indicators.append('<span class="status-indicator important-icon" title="重要">❗</span>')
            
            return ' '.join(indicators)
            
        except:
            return ""
    
    def _get_sender_avatar(self, sender_name: str) -> str:
        """获取发件人头像"""
        try:
            if sender_name:
                # 处理中文姓名
                if any('\u4e00' <= char <= '\u9fff' for char in sender_name):
                    return sender_name[0]
                # 处理英文姓名
                else:
                    words = sender_name.split()
                    if len(words) >= 2:
                        return (words[0][0] + words[1][0]).upper()
                    elif len(words) == 1:
                        return words[0][0].upper()
            
            return '?'
        except:
            return '?'
    
    def _generate_preview_html(self, email: EmailListItem) -> str:
        """生成预览HTML"""
        if email.preview_text:
            return f"""
            <div class="email-preview">
                <span class="preview-text">{self._escape_html(email.preview_text)}</span>
            </div>
            """
        return ""
    
    def _format_list_date(self, date_obj) -> str:
        """格式化列表日期"""
        try:
            if hasattr(date_obj, 'strftime'):
                now = datetime.now()
                
                if date_obj.date() == now.date():
                    return date_obj.strftime('%H:%M')
                elif date_obj.year == now.year:
                    return date_obj.strftime('%m/%d')
                else:
                    return date_obj.strftime('%Y/%m/%d')
            else:
                return str(date_obj)
        except:
            return "未知"
    
    def _format_size(self, size: int) -> str:
        """格式化文件大小"""
        try:
            if size < 1024:
                return f"{size}B"
            elif size < 1024 * 1024:
                return f"{size // 1024}KB"
            else:
                return f"{size // (1024 * 1024)}MB"
        except:
            return "未知"
    
    def _escape_html(self, text: str) -> str:
        """转义HTML特殊字符"""
        try:
            import html
            return html.escape(str(text))
        except:
            return str(text)
    
    def _wrap_in_list_template(self, list_html: str, folder_name: str, total_count: int) -> str:
        """包装在列表模板中"""
        return f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{folder_name} - 邮件列表</title>
    <link rel="stylesheet" href="email_list_styles.css">
</head>
<body>
    <div class="email-list-container">
        <div class="list-header">
            <div class="folder-info">
                <h1 class="folder-name">📁 {folder_name}</h1>
                <span class="email-count">{total_count} 封邮件</span>
            </div>
            <div class="list-controls">
                <div class="sort-controls">
                    <select id="sortBy" onchange="changeSortBy(this.value)">
                        <option value="date">按日期排序</option>
                        <option value="sender">按发件人排序</option>
                        <option value="subject">按主题排序</option>
                        <option value="size">按大小排序</option>
                    </select>
                </div>
                <div class="view-controls">
                    <button id="compactView" onclick="toggleCompactView()" title="紧凑视图">⊞</button>
                    <button id="refreshList" onclick="refreshList()" title="刷新">🔄</button>
                </div>
            </div>
        </div>
        
        <div class="email-list-content">
            {list_html}
        </div>
    </div>
    
    <script src="email_list.js"></script>
</body>
</html>"""
    
    def _create_error_list(self, error_message: str) -> str:
        """创建错误列表"""
        return f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>邮件列表错误</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 50px; }}
        .error {{ color: #d32f2f; border: 1px solid #d32f2f; padding: 20px; border-radius: 4px; }}
    </style>
</head>
<body>
    <div class="error">
        <h2>❌ 邮件列表加载失败</h2>
        <p>{self._escape_html(error_message)}</p>
    </div>
</body>
</html>"""

def main():
    """测试邮件列表视图"""
    print("📋 邮件列表视图测试")
    print("=" * 50)
    
    list_view = EmailListView()
    print("✅ 邮件列表视图创建成功")

if __name__ == "__main__":
    main()
