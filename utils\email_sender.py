#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件发送器
提供SMTP邮件发送功能
"""

import smtplib
import ssl
import logging
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ipart
from email.mime.base import MIMEBase
from email import encoders
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime

class EmailSender:
    """邮件发送器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 常用SMTP服务器配置
        self.smtp_configs = {
            'outlook.com': {
                'server': 'smtp-mail.outlook.com',
                'port': 587,
                'use_tls': True
            },
            'hotmail.com': {
                'server': 'smtp-mail.outlook.com',
                'port': 587,
                'use_tls': True
            },
            'gmail.com': {
                'server': 'smtp.gmail.com',
                'port': 587,
                'use_tls': True
            },
            'qq.com': {
                'server': 'smtp.qq.com',
                'port': 587,
                'use_tls': True
            },
            '163.com': {
                'server': 'smtp.163.com',
                'port': 25,
                'use_tls': False
            },
            '126.com': {
                'server': 'smtp.126.com',
                'port': 25,
                'use_tls': False
            }
        }
    
    def get_smtp_config(self, email_address: str) -> Dict[str, Any]:
        """根据邮箱地址获取SMTP配置"""
        try:
            domain = email_address.split('@')[1].lower()
            return self.smtp_configs.get(domain, {
                'server': f'smtp.{domain}',
                'port': 587,
                'use_tls': True
            })
        except Exception as e:
            self.logger.error(f"获取SMTP配置失败: {e}")
            return {
                'server': 'smtp.gmail.com',
                'port': 587,
                'use_tls': True
            }
    
    def create_message(self, email_data: Dict[str, Any]) -> MIMEMultipart:
        """创建邮件消息"""
        try:
            # 创建邮件对象
            msg = MIMEMultipart('alternative')
            
            # 设置邮件头
            msg['From'] = email_data.get('from_email', '')
            msg['To'] = email_data.get('to', '')
            
            if email_data.get('cc'):
                msg['Cc'] = email_data['cc']
            
            msg['Subject'] = email_data.get('subject', '')
            msg['Date'] = email_data.get('timestamp', datetime.now()).strftime('%a, %d %b %Y %H:%M:%S %z')
            
            # 设置优先级
            priority = email_data.get('priority', '普通')
            if priority == '高':
                msg['X-Priority'] = '1'
                msg['X-MSMail-Priority'] = 'High'
            elif priority == '低':
                msg['X-Priority'] = '5'
                msg['X-MSMail-Priority'] = 'Low'
            
            # 设置阅读回执
            if email_data.get('request_receipt', False):
                msg['Disposition-Notification-To'] = email_data.get('from_email', '')
            
            # 添加邮件内容
            text_content = email_data.get('content', '')
            html_content = email_data.get('html_content', '')
            
            if text_content:
                text_part = MIMEText(text_content, 'plain', 'utf-8')
                msg.attach(text_part)
            
            if html_content:
                html_part = MIMEText(html_content, 'html', 'utf-8')
                msg.attach(html_part)
            
            # 添加附件
            attachments = email_data.get('attachments', [])
            for attachment in attachments:
                self.add_attachment(msg, attachment)
            
            return msg
            
        except Exception as e:
            self.logger.error(f"创建邮件消息失败: {e}")
            raise
    
    def add_attachment(self, msg: MIMEMultipart, attachment: Dict[str, Any]):
        """添加附件到邮件"""
        try:
            file_path = attachment['path']
            file_name = attachment['name']
            
            if not Path(file_path).exists():
                self.logger.warning(f"附件文件不存在: {file_path}")
                return
            
            # 读取文件内容
            with open(file_path, 'rb') as f:
                file_data = f.read()
            
            # 创建附件对象
            part = MIMEBase('application', 'octet-stream')
            part.set_payload(file_data)
            
            # 编码附件
            encoders.encode_base64(part)
            
            # 设置附件头
            part.add_header(
                'Content-Disposition',
                f'attachment; filename= {file_name}'
            )
            
            # 添加到邮件
            msg.attach(part)
            
            self.logger.info(f"已添加附件: {file_name}")
            
        except Exception as e:
            self.logger.error(f"添加附件失败: {e}")
            raise
    
    def send_email(self, email_data: Dict[str, Any], account_config: Any = None) -> bool:
        """发送邮件"""
        try:
            # 获取发件人邮箱
            from_email = email_data.get('from_email')
            if not from_email and account_config:
                from_email = account_config.email
            
            if not from_email:
                raise ValueError("未指定发件人邮箱")
            
            # 更新邮件数据
            email_data['from_email'] = from_email
            
            # 获取SMTP配置
            smtp_config = self.get_smtp_config(from_email)
            
            # 创建邮件消息
            msg = self.create_message(email_data)
            
            # 获取所有收件人
            recipients = self.get_all_recipients(email_data)
            
            # 连接SMTP服务器并发送
            self.send_via_smtp(msg, from_email, recipients, smtp_config, account_config)
            
            self.logger.info(f"邮件发送成功: {email_data.get('subject', '(无主题)')}")
            return True
            
        except Exception as e:
            self.logger.error(f"发送邮件失败: {e}")
            raise
    
    def get_all_recipients(self, email_data: Dict[str, Any]) -> List[str]:
        """获取所有收件人列表"""
        recipients = []
        
        # 收件人
        to_addresses = email_data.get('to', '')
        if to_addresses:
            recipients.extend([addr.strip() for addr in to_addresses.split(';') if addr.strip()])
        
        # 抄送
        cc_addresses = email_data.get('cc', '')
        if cc_addresses:
            recipients.extend([addr.strip() for addr in cc_addresses.split(';') if addr.strip()])
        
        # 密送
        bcc_addresses = email_data.get('bcc', '')
        if bcc_addresses:
            recipients.extend([addr.strip() for addr in bcc_addresses.split(';') if addr.strip()])
        
        return recipients
    
    def send_via_smtp(self, msg: MIMEMultipart, from_email: str, recipients: List[str], 
                      smtp_config: Dict[str, Any], account_config: Any = None):
        """通过SMTP发送邮件"""
        try:
            server = None
            
            # 连接SMTP服务器
            if smtp_config.get('use_tls', True):
                # 使用TLS
                server = smtplib.SMTP(smtp_config['server'], smtp_config['port'])
                server.starttls()
            else:
                # 不使用TLS
                server = smtplib.SMTP(smtp_config['server'], smtp_config['port'])
            
            # 登录
            if account_config:
                # 使用账户配置中的认证信息
                if hasattr(account_config, 'password'):
                    server.login(from_email, account_config.password)
                elif hasattr(account_config, 'access_token'):
                    # OAuth2认证（需要特殊处理）
                    self.oauth2_login(server, from_email, account_config.access_token)
                else:
                    raise ValueError("账户配置中缺少认证信息")
            else:
                # 使用默认密码（实际应用中应该从安全存储中获取）
                raise ValueError("需要账户配置进行认证")
            
            # 发送邮件
            server.send_message(msg, from_email, recipients)
            
            # 关闭连接
            server.quit()
            
            self.logger.info(f"SMTP发送成功: {len(recipients)} 个收件人")
            
        except Exception as e:
            if server:
                try:
                    server.quit()
                except:
                    pass
            self.logger.error(f"SMTP发送失败: {e}")
            raise
    
    def oauth2_login(self, server: smtplib.SMTP, email: str, access_token: str):
        """OAuth2登录"""
        try:
            # 构造OAuth2认证字符串
            auth_string = f'user={email}\x01auth=Bearer {access_token}\x01\x01'
            auth_string_b64 = auth_string.encode('ascii').hex()
            
            # 执行OAuth2认证
            server.docmd('AUTH', f'XOAUTH2 {auth_string_b64}')
            
            self.logger.info("OAuth2认证成功")
            
        except Exception as e:
            self.logger.error(f"OAuth2认证失败: {e}")
            raise
    
    def test_smtp_connection(self, email: str, password: str = None, access_token: str = None) -> bool:
        """测试SMTP连接"""
        try:
            smtp_config = self.get_smtp_config(email)
            
            server = None
            if smtp_config.get('use_tls', True):
                server = smtplib.SMTP(smtp_config['server'], smtp_config['port'])
                server.starttls()
            else:
                server = smtplib.SMTP(smtp_config['server'], smtp_config['port'])
            
            # 测试登录
            if password:
                server.login(email, password)
            elif access_token:
                self.oauth2_login(server, email, access_token)
            else:
                raise ValueError("需要密码或访问令牌")
            
            server.quit()
            
            self.logger.info(f"SMTP连接测试成功: {email}")
            return True
            
        except Exception as e:
            if server:
                try:
                    server.quit()
                except:
                    pass
            self.logger.error(f"SMTP连接测试失败: {e}")
            return False
    
    def validate_email_address(self, email: str) -> bool:
        """验证邮箱地址格式"""
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    def get_email_size(self, email_data: Dict[str, Any]) -> int:
        """计算邮件大小"""
        try:
            msg = self.create_message(email_data)
            return len(msg.as_string().encode('utf-8'))
        except Exception as e:
            self.logger.error(f"计算邮件大小失败: {e}")
            return 0

class MockEmailSender(EmailSender):
    """模拟邮件发送器（用于测试）"""
    
    def __init__(self):
        super().__init__()
        self.sent_emails = []
    
    def send_email(self, email_data: Dict[str, Any], account_config: Any = None) -> bool:
        """模拟发送邮件"""
        try:
            # 验证邮件数据
            if not email_data.get('to'):
                raise ValueError("缺少收件人")
            
            if not email_data.get('subject'):
                raise ValueError("缺少邮件主题")
            
            # 模拟发送延迟
            import time
            time.sleep(1)
            
            # 记录发送的邮件
            sent_email = {
                'timestamp': datetime.now(),
                'from': email_data.get('from_email', '<EMAIL>'),
                'to': email_data.get('to'),
                'subject': email_data.get('subject'),
                'content': email_data.get('content', ''),
                'attachments': len(email_data.get('attachments', []))
            }
            
            self.sent_emails.append(sent_email)
            
            self.logger.info(f"模拟发送邮件成功: {sent_email['subject']}")
            return True
            
        except Exception as e:
            self.logger.error(f"模拟发送邮件失败: {e}")
            raise
    
    def get_sent_emails(self) -> List[Dict[str, Any]]:
        """获取已发送邮件列表"""
        return self.sent_emails.copy()
    
    def clear_sent_emails(self):
        """清空已发送邮件列表"""
        self.sent_emails.clear()

if __name__ == "__main__":
    # 测试邮件发送器
    logging.basicConfig(level=logging.INFO)
    
    # 使用模拟发送器进行测试
    sender = MockEmailSender()
    
    # 测试邮件数据
    test_email = {
        'from_email': '<EMAIL>',
        'to': '<EMAIL>',
        'subject': '测试邮件',
        'content': '这是一封测试邮件的内容。',
        'format': '纯文本',
        'priority': '普通',
        'attachments': [],
        'timestamp': datetime.now()
    }
    
    try:
        success = sender.send_email(test_email)
        if success:
            print("✅ 邮件发送测试成功")
            
            sent_emails = sender.get_sent_emails()
            print(f"📧 已发送邮件数量: {len(sent_emails)}")
            
            for email in sent_emails:
                print(f"   - {email['subject']} -> {email['to']}")
        else:
            print("❌ 邮件发送测试失败")
            
    except Exception as e:
        print(f"❌ 邮件发送测试异常: {e}")
