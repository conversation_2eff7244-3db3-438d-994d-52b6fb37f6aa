#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
注意事项面板对话框
显示系统使用注意事项、常见问题和安全建议
"""

import sys
import logging
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTextEdit, QPushButton,
    QTabWidget, QWidget, QLabel, QFrame, QScrollArea,
    QApplication, QMessageBox
)
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont, QPixmap

class NoticePanelDialog(QDialog):
    """注意事项面板对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("⚠️ 注意事项 - 微软ou批量管理")
        self.setModal(False)
        self.resize(800, 600)
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
        
        self.setup_ui()
        self.setup_connections()
        
        self.logger.info("注意事项面板已打开")
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 标题区域
        title_frame = QFrame()
        title_frame.setStyleSheet("""
            QFrame {
                background-color: #FF5722;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        title_layout = QVBoxLayout(title_frame)
        
        title_label = QLabel("⚠️ 微软ou批量管理使用注意事项")
        title_label.setFont(QFont("Arial", 18, QFont.Weight.Bold))
        title_label.setStyleSheet("color: white; text-align: center;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_layout.addWidget(title_label)
        
        subtitle_label = QLabel("请仔细阅读以下内容，确保系统正常运行")
        subtitle_label.setFont(QFont("Arial", 12))
        subtitle_label.setStyleSheet("color: white; text-align: center;")
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_layout.addWidget(subtitle_label)
        
        layout.addWidget(title_frame)
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #ddd;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #f0f0f0;
                padding: 10px 20px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #2196F3;
            }
            QTabBar::tab:hover {
                background-color: #e0e0e0;
            }
        """)
        
        # 添加各个选项卡
        self.add_network_tab()
        self.add_account_tab()
        self.add_troubleshooting_tab()
        self.add_security_tab()
        self.add_compatibility_tab()
        
        layout.addWidget(self.tab_widget)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        
        self.reset_settings_btn = QPushButton("🔄 重置启动提示")
        self.reset_settings_btn.setToolTip("重新启用启动时的网络提示对话框")
        self.reset_settings_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                padding: 8px 16px;
                font-weight: bold;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
        """)
        button_layout.addWidget(self.reset_settings_btn)
        
        button_layout.addStretch()
        
        self.close_btn = QPushButton("❌ 关闭")
        self.close_btn.setStyleSheet("""
            QPushButton {
                background-color: #757575;
                color: white;
                border: none;
                padding: 8px 16px;
                font-weight: bold;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #616161;
            }
        """)
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)
    
    def add_network_tab(self):
        """添加网络环境要求选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        content = QTextEdit()
        content.setReadOnly(True)
        content.setHtml("""
        <div style="font-family: Arial; line-height: 1.6; padding: 20px;">
        
        <h2 style="color: #FF5722;">🌐 网络环境要求</h2>
        
        <h3 style="color: #2196F3;">⚠️ 重要提醒</h3>
        <div style="background-color: #FFF3E0; border-left: 4px solid #FF9800; padding: 15px; margin: 10px 0;">
        <strong>使用本系统前，请务必关闭以下网络工具：</strong>
        <ul>
            <li><strong>VPN客户端</strong>（如ExpressVPN、NordVPN、Shadowsocks等）</li>
            <li><strong>网络代理工具</strong>（如Clash、V2Ray、Proxifier等）</li>
            <li><strong>网络加速器</strong>（如游戏加速器、网页加速器等）</li>
            <li><strong>防火墙代理设置</strong>（企业防火墙的代理配置）</li>
        </ul>
        </div>
        
        <h3 style="color: #2196F3;">🔍 为什么需要关闭这些工具？</h3>
        <ol>
            <li><strong>连接稳定性</strong>：VPN和代理可能导致邮件服务器连接不稳定</li>
            <li><strong>IP地址限制</strong>：某些邮件提供商会阻止来自代理IP的连接</li>
            <li><strong>认证问题</strong>：代理可能影响OAuth2认证流程</li>
            <li><strong>性能影响</strong>：额外的网络层会降低邮件同步速度</li>
            <li><strong>安全考虑</strong>：避免敏感邮件数据通过第三方代理</li>
        </ol>
        
        <h3 style="color: #2196F3;">✅ 推荐的网络环境</h3>
        <ul>
            <li>直接连接到互联网（无代理）</li>
            <li>稳定的宽带或WiFi连接</li>
            <li>确保防火墙允许邮件客户端访问网络</li>
            <li>如果在企业网络中，请联系IT部门确认邮件端口开放</li>
        </ul>
        
        <h3 style="color: #2196F3;">🔧 如何检查网络设置</h3>
        <div style="background-color: #E3F2FD; border-left: 4px solid #2196F3; padding: 15px; margin: 10px 0;">
        <strong>Windows系统：</strong>
        <ol>
            <li>打开"设置" → "网络和Internet" → "代理"</li>
            <li>确保"自动检测设置"已关闭</li>
            <li>确保"使用代理服务器"已关闭</li>
        </ol>
        
        <strong>检查VPN状态：</strong>
        <ol>
            <li>查看系统托盘是否有VPN图标</li>
            <li>打开"设置" → "网络和Internet" → "VPN"</li>
            <li>确保所有VPN连接都已断开</li>
        </ol>
        </div>
        
        </div>
        """)
        layout.addWidget(content)
        
        self.tab_widget.addTab(tab, "🌐 网络环境")
    
    def add_account_tab(self):
        """添加邮箱账户配置选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        content = QTextEdit()
        content.setReadOnly(True)
        content.setHtml("""
        <div style="font-family: Arial; line-height: 1.6; padding: 20px;">
        
        <h2 style="color: #FF5722;">📧 邮箱账户配置注意点</h2>
        
        <h3 style="color: #2196F3;">🔐 Microsoft账户配置</h3>
        <div style="background-color: #E8F5E8; border-left: 4px solid #4CAF50; padding: 15px; margin: 10px 0;">
        <strong>支持的账户类型：</strong>
        <ul>
            <li>Outlook.com个人账户</li>
            <li>Hotmail.com账户</li>
            <li>Live.com账户</li>
            <li>Microsoft 365企业账户</li>
            <li>Exchange Online账户</li>
        </ul>
        </div>
        
        <h3 style="color: #2196F3;">⚙️ 账户设置要求</h3>
        <ol>
            <li><strong>启用两步验证</strong>：建议为Microsoft账户启用两步验证以提高安全性</li>
            <li><strong>应用密码</strong>：如果启用了两步验证，可能需要生成应用专用密码</li>
            <li><strong>IMAP设置</strong>：确保账户已启用IMAP访问</li>
            <li><strong>安全设置</strong>：允许"安全性较低的应用"访问（如果需要）</li>
        </ol>
        
        <h3 style="color: #2196F3;">🚫 常见配置错误</h3>
        <div style="background-color: #FFEBEE; border-left: 4px solid #F44336; padding: 15px; margin: 10px 0;">
        <ul>
            <li><strong>错误的服务器设置</strong>：使用了错误的IMAP/SMTP服务器地址</li>
            <li><strong>端口配置错误</strong>：使用了错误的端口号或加密设置</li>
            <li><strong>认证方式错误</strong>：选择了不支持的认证方式</li>
            <li><strong>账户权限不足</strong>：账户没有IMAP访问权限</li>
        </ul>
        </div>
        
        <h3 style="color: #2196F3;">✅ 推荐配置步骤</h3>
        <ol>
            <li>使用系统的"账户管理"功能添加账户</li>
            <li>选择"Microsoft账户"类型</li>
            <li>按照向导完成OAuth2认证</li>
            <li>测试账户连接是否正常</li>
            <li>配置同步选项和文件夹设置</li>
        </ol>
        
        <h3 style="color: #2196F3;">🔄 批量导入注意事项</h3>
        <ul>
            <li>确保CSV文件格式正确</li>
            <li>每个账户信息完整且有效</li>
            <li>避免重复导入相同账户</li>
            <li>导入后及时测试账户连接</li>
        </ul>
        
        </div>
        """)
        layout.addWidget(content)
        
        self.tab_widget.addTab(tab, "📧 账户配置")
    
    def add_troubleshooting_tab(self):
        """添加常见问题解决方案选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        content = QTextEdit()
        content.setReadOnly(True)
        content.setHtml("""
        <div style="font-family: Arial; line-height: 1.6; padding: 20px;">
        
        <h2 style="color: #FF5722;">🔧 常见问题解决方案</h2>
        
        <h3 style="color: #2196F3;">❌ 连接问题</h3>
        <div style="background-color: #FFF3E0; border-left: 4px solid #FF9800; padding: 15px; margin: 10px 0;">
        <strong>问题：无法连接到邮件服务器</strong><br>
        <strong>解决方案：</strong>
        <ol>
            <li>检查网络连接是否正常</li>
            <li>确认已关闭VPN和代理</li>
            <li>检查防火墙设置</li>
            <li>验证账户凭据是否正确</li>
            <li>尝试重新添加账户</li>
        </ol>
        </div>
        
        <h3 style="color: #2196F3;">🔐 认证问题</h3>
        <div style="background-color: #FFEBEE; border-left: 4px solid #F44336; padding: 15px; margin: 10px 0;">
        <strong>问题：认证失败或密码错误</strong><br>
        <strong>解决方案：</strong>
        <ol>
            <li>确认账户密码是否正确</li>
            <li>检查是否需要应用专用密码</li>
            <li>验证两步验证设置</li>
            <li>尝试重新进行OAuth2认证</li>
            <li>检查账户是否被锁定</li>
        </ol>
        </div>
        
        <h3 style="color: #2196F3;">📧 邮件同步问题</h3>
        <div style="background-color: #E3F2FD; border-left: 4px solid #2196F3; padding: 15px; margin: 10px 0;">
        <strong>问题：邮件无法同步或同步缓慢</strong><br>
        <strong>解决方案：</strong>
        <ol>
            <li>检查网络速度和稳定性</li>
            <li>调整同步设置和频率</li>
            <li>清理本地缓存数据</li>
            <li>检查邮箱存储空间</li>
            <li>尝试手动刷新邮件列表</li>
        </ol>
        </div>
        
        <h3 style="color: #2196F3;">💾 数据问题</h3>
        <div style="background-color: #E8F5E8; border-left: 4px solid #4CAF50; padding: 15px; margin: 10px 0;">
        <strong>问题：邮件数据丢失或显示异常</strong><br>
        <strong>解决方案：</strong>
        <ol>
            <li>检查数据库文件完整性</li>
            <li>重新同步邮件数据</li>
            <li>恢复备份数据（如果有）</li>
            <li>重建邮件索引</li>
            <li>联系技术支持</li>
        </ol>
        </div>
        
        <h3 style="color: #2196F3;">🚀 性能优化</h3>
        <ul>
            <li><strong>定期清理</strong>：清理临时文件和缓存数据</li>
            <li><strong>合理设置</strong>：调整同步频率和邮件数量限制</li>
            <li><strong>硬件要求</strong>：确保足够的内存和存储空间</li>
            <li><strong>网络优化</strong>：使用稳定的网络连接</li>
        </ul>
        
        <h3 style="color: #2196F3;">📞 获取帮助</h3>
        <div style="background-color: #F3E5F5; border-left: 4px solid #9C27B0; padding: 15px; margin: 10px 0;">
        如果以上解决方案无法解决您的问题，请：
        <ul>
            <li>查看系统日志获取详细错误信息</li>
            <li>记录问题发生的具体步骤</li>
            <li>准备相关的错误截图</li>
            <li>联系技术支持团队</li>
        </ul>
        </div>
        
        </div>
        """)
        layout.addWidget(content)
        
        self.tab_widget.addTab(tab, "🔧 问题解决")
    
    def add_security_tab(self):
        """添加安全使用建议选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        content = QTextEdit()
        content.setReadOnly(True)
        content.setHtml("""
        <div style="font-family: Arial; line-height: 1.6; padding: 20px;">
        
        <h2 style="color: #FF5722;">🔒 安全使用建议</h2>
        
        <h3 style="color: #2196F3;">🛡️ 账户安全</h3>
        <div style="background-color: #E8F5E8; border-left: 4px solid #4CAF50; padding: 15px; margin: 10px 0;">
        <strong>强烈建议：</strong>
        <ul>
            <li><strong>启用两步验证</strong>：为所有邮箱账户启用两步验证</li>
            <li><strong>使用强密码</strong>：设置复杂且唯一的密码</li>
            <li><strong>定期更换密码</strong>：建议每3-6个月更换一次密码</li>
            <li><strong>应用专用密码</strong>：为第三方应用生成专用密码</li>
        </ul>
        </div>
        
        <h3 style="color: #2196F3;">💻 系统安全</h3>
        <ol>
            <li><strong>保持系统更新</strong>：及时安装操作系统和软件更新</li>
            <li><strong>使用杀毒软件</strong>：安装并保持杀毒软件最新</li>
            <li><strong>防火墙设置</strong>：正确配置防火墙规则</li>
            <li><strong>定期备份</strong>：备份重要的邮件数据</li>
        </ol>
        
        <h3 style="color: #2196F3;">🌐 网络安全</h3>
        <div style="background-color: #FFF3E0; border-left: 4px solid #FF9800; padding: 15px; margin: 10px 0;">
        <strong>注意事项：</strong>
        <ul>
            <li><strong>避免公共WiFi</strong>：不要在公共WiFi下处理敏感邮件</li>
            <li><strong>加密连接</strong>：确保使用SSL/TLS加密连接</li>
            <li><strong>验证证书</strong>：注意SSL证书的有效性</li>
            <li><strong>监控异常</strong>：留意异常的网络活动</li>
        </ul>
        </div>
        
        <h3 style="color: #2196F3;">📧 邮件安全</h3>
        <ul>
            <li><strong>谨慎点击链接</strong>：不要点击可疑邮件中的链接</li>
            <li><strong>验证发件人</strong>：确认邮件发件人的真实性</li>
            <li><strong>附件安全</strong>：谨慎下载和打开邮件附件</li>
            <li><strong>敏感信息</strong>：避免在邮件中发送敏感信息</li>
        </ul>
        
        <h3 style="color: #2196F3;">🔐 数据保护</h3>
        <div style="background-color: #E3F2FD; border-left: 4px solid #2196F3; padding: 15px; margin: 10px 0;">
        <strong>数据安全措施：</strong>
        <ol>
            <li><strong>本地加密</strong>：重要数据进行本地加密存储</li>
            <li><strong>访问控制</strong>：限制对邮件数据的访问权限</li>
            <li><strong>定期清理</strong>：定期清理不需要的邮件数据</li>
            <li><strong>安全删除</strong>：彻底删除敏感邮件数据</li>
        </ol>
        </div>
        
        <h3 style="color: #2196F3;">⚠️ 风险提醒</h3>
        <div style="background-color: #FFEBEE; border-left: 4px solid #F44336; padding: 15px; margin: 10px 0;">
        <strong>请注意以下安全风险：</strong>
        <ul>
            <li>钓鱼邮件和恶意链接</li>
            <li>恶意软件和病毒</li>
            <li>数据泄露和隐私侵犯</li>
            <li>未授权的账户访问</li>
            <li>中间人攻击</li>
        </ul>
        </div>
        
        <h3 style="color: #2196F3;">📋 安全检查清单</h3>
        <ul>
            <li>✅ 所有账户都启用了两步验证</li>
            <li>✅ 使用了强密码和应用专用密码</li>
            <li>✅ 系统和软件保持最新版本</li>
            <li>✅ 防火墙和杀毒软件正常运行</li>
            <li>✅ 定期备份重要邮件数据</li>
            <li>✅ 网络连接安全可靠</li>
        </ul>
        
        </div>
        """)
        layout.addWidget(content)
        
        self.tab_widget.addTab(tab, "🔒 安全建议")
    
    def add_compatibility_tab(self):
        """添加系统兼容性说明选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        content = QTextEdit()
        content.setReadOnly(True)
        content.setHtml("""
        <div style="font-family: Arial; line-height: 1.6; padding: 20px;">
        
        <h2 style="color: #FF5722;">💻 系统兼容性说明</h2>
        
        <h3 style="color: #2196F3;">🖥️ 支持的操作系统</h3>
        <div style="background-color: #E8F5E8; border-left: 4px solid #4CAF50; padding: 15px; margin: 10px 0;">
        <strong>Windows系统：</strong>
        <ul>
            <li>Windows 10 (版本1903及以上)</li>
            <li>Windows 11 (所有版本)</li>
            <li>Windows Server 2019/2022</li>
        </ul>
        
        <strong>推荐配置：</strong>
        <ul>
            <li>64位操作系统</li>
            <li>最新的系统更新</li>
            <li>.NET Framework 4.8或更高版本</li>
        </ul>
        </div>
        
        <h3 style="color: #2196F3;">⚙️ 硬件要求</h3>
        <table style="border-collapse: collapse; width: 100%; margin: 10px 0;">
            <tr style="background-color: #f0f0f0;">
                <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">组件</th>
                <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">最低要求</th>
                <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">推荐配置</th>
            </tr>
            <tr>
                <td style="border: 1px solid #ddd; padding: 8px;">处理器</td>
                <td style="border: 1px solid #ddd; padding: 8px;">双核 1.6GHz</td>
                <td style="border: 1px solid #ddd; padding: 8px;">四核 2.4GHz或更高</td>
            </tr>
            <tr style="background-color: #f9f9f9;">
                <td style="border: 1px solid #ddd; padding: 8px;">内存</td>
                <td style="border: 1px solid #ddd; padding: 8px;">4GB RAM</td>
                <td style="border: 1px solid #ddd; padding: 8px;">8GB RAM或更高</td>
            </tr>
            <tr>
                <td style="border: 1px solid #ddd; padding: 8px;">存储空间</td>
                <td style="border: 1px solid #ddd; padding: 8px;">2GB可用空间</td>
                <td style="border: 1px solid #ddd; padding: 8px;">10GB可用空间</td>
            </tr>
            <tr style="background-color: #f9f9f9;">
                <td style="border: 1px solid #ddd; padding: 8px;">网络</td>
                <td style="border: 1px solid #ddd; padding: 8px;">宽带连接</td>
                <td style="border: 1px solid #ddd; padding: 8px;">稳定的高速宽带</td>
            </tr>
        </table>
        
        <h3 style="color: #2196F3;">🔧 依赖组件</h3>
        <div style="background-color: #E3F2FD; border-left: 4px solid #2196F3; padding: 15px; margin: 10px 0;">
        <strong>必需组件：</strong>
        <ul>
            <li><strong>Python 3.8+</strong>：核心运行环境</li>
            <li><strong>PyQt6</strong>：用户界面框架</li>
            <li><strong>Microsoft Visual C++ Redistributable</strong>：运行时库</li>
            <li><strong>OpenSSL</strong>：加密通信支持</li>
        </ul>
        
        <strong>可选组件：</strong>
        <ul>
            <li><strong>QtWebEngine</strong>：HTML邮件显示</li>
            <li><strong>Pillow</strong>：图像处理</li>
            <li><strong>cryptography</strong>：高级加密功能</li>
        </ul>
        </div>
        
        <h3 style="color: #2196F3;">🌐 网络要求</h3>
        <ul>
            <li><strong>端口访问</strong>：需要访问IMAP(993)、SMTP(587)端口</li>
            <li><strong>防火墙</strong>：允许程序访问网络</li>
            <li><strong>代理设置</strong>：不支持代理环境</li>
            <li><strong>DNS解析</strong>：需要正常的DNS解析功能</li>
        </ul>
        
        <h3 style="color: #2196F3;">⚠️ 已知兼容性问题</h3>
        <div style="background-color: #FFF3E0; border-left: 4px solid #FF9800; padding: 15px; margin: 10px 0;">
        <strong>可能遇到的问题：</strong>
        <ul>
            <li><strong>Windows 7/8</strong>：不再支持，建议升级到Windows 10/11</li>
            <li><strong>32位系统</strong>：性能可能受限，推荐64位系统</li>
            <li><strong>虚拟机环境</strong>：可能存在性能和网络问题</li>
            <li><strong>企业防火墙</strong>：可能需要特殊配置</li>
        </ul>
        </div>
        
        <h3 style="color: #2196F3;">🔄 更新和维护</h3>
        <ol>
            <li><strong>自动更新</strong>：系统会自动检查并提示更新</li>
            <li><strong>手动更新</strong>：可以手动下载最新版本</li>
            <li><strong>配置备份</strong>：更新前会自动备份配置</li>
            <li><strong>回滚支持</strong>：支持回滚到之前的版本</li>
        </ol>
        
        <h3 style="color: #2196F3;">📞 技术支持</h3>
        <div style="background-color: #F3E5F5; border-left: 4px solid #9C27B0; padding: 15px; margin: 10px 0;">
        如果遇到兼容性问题，请提供以下信息：
        <ul>
            <li>操作系统版本和架构</li>
            <li>硬件配置信息</li>
            <li>网络环境描述</li>
            <li>错误信息和日志</li>
            <li>问题重现步骤</li>
        </ul>
        </div>
        
        </div>
        """)
        layout.addWidget(content)
        
        self.tab_widget.addTab(tab, "💻 系统兼容性")
    
    def setup_connections(self):
        """设置信号连接"""
        self.close_btn.clicked.connect(self.close)
        self.reset_settings_btn.clicked.connect(self.reset_startup_settings)
    
    def reset_startup_settings(self):
        """重置启动设置"""
        try:
            from ui.startup_notice_dialog import StartupNoticeDialog
            
            if StartupNoticeDialog.reset_startup_settings():
                QMessageBox.information(
                    self,
                    "设置重置",
                    "启动提示设置已重置！\n下次启动程序时将重新显示网络环境提示。"
                )
                self.logger.info("启动提示设置已重置")
            else:
                QMessageBox.information(
                    self,
                    "设置重置",
                    "启动提示设置已经是默认状态。"
                )
                
        except Exception as e:
            self.logger.error(f"重置启动设置失败: {e}")
            QMessageBox.critical(
                self,
                "错误",
                f"重置启动设置失败：\n{str(e)}"
            )

if __name__ == "__main__":
    app = QApplication(sys.argv)
    dialog = NoticePanelDialog()
    dialog.show()
    sys.exit(app.exec())
