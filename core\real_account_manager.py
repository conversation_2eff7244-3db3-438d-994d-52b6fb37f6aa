#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实邮件账户管理器
管理和使用真实的邮件账户
"""

import json
import logging
import threading
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
import time

from .production_optimized_v2 import ProductionOptimizedClientV2, AuthMethod
from .email_database import EmailDatabase, EmailRecord

@dataclass
class RealAccountConfig:
    """真实账户配置"""
    account_id: str
    email: str
    display_name: str
    imap_server: str
    imap_port: int
    smtp_server: str
    smtp_port: int
    use_ssl: bool
    client_id: str
    refresh_token: str
    password: str = ""  # 添加密码字段（可选）
    enabled: bool = True
    priority: int = 1
    max_retries: int = 3
    timeout: int = 30
    keep_alive: bool = True
    sync_interval: int = 5
    max_emails: int = 1000
    download_attachments: bool = False
    last_sync: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        if self.last_sync:
            data['last_sync'] = self.last_sync.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'RealAccountConfig':
        """从字典创建"""
        if 'last_sync' in data and data['last_sync']:
            data['last_sync'] = datetime.fromisoformat(data['last_sync'])
        return cls(**data)

class RealAccountManager:
    """真实邮件账户管理器"""
    
    def __init__(self, database: EmailDatabase):
        self.database = database
        self.logger = logging.getLogger(__name__)
        self.accounts: Dict[str, RealAccountConfig] = {}
        self.clients: Dict[str, ProductionOptimizedClientV2] = {}
        self.sync_threads: Dict[str, threading.Thread] = {}
        self.stop_sync = threading.Event()
        self.sync_callback = None  # 同步完成回调函数

        # 配置目录
        self.config_dir = Path("config")
        self.config_dir.mkdir(exist_ok=True)

        # 加载账户配置
        self.load_accounts()

    def set_sync_callback(self, callback):
        """设置同步完成回调函数"""
        self.sync_callback = callback

    def load_accounts(self):
        """加载所有账户配置"""
        try:
            self.logger.info("正在加载账户配置...")
            
            # 扫描配置文件
            for config_file in self.config_dir.glob("account_*.json"):
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config_data = json.load(f)
                    
                    # 生成账户ID
                    account_id = config_data['email'].replace('@', '_').replace('.', '_')
                    config_data['account_id'] = account_id
                    
                    # 创建账户配置
                    account_config = RealAccountConfig.from_dict(config_data)
                    self.accounts[account_id] = account_config
                    
                    self.logger.info(f"加载账户配置: {account_config.email}")
                    
                except Exception as e:
                    self.logger.error(f"加载配置文件 {config_file} 失败: {e}")
            
            self.logger.info(f"共加载 {len(self.accounts)} 个账户配置")
            
        except Exception as e:
            self.logger.error(f"加载账户配置失败: {e}")
    
    def save_account(self, account_config: RealAccountConfig):
        """保存账户配置"""
        try:
            config_file = self.config_dir / f"account_{account_config.email.replace('@', '_').replace('.', '_')}.json"
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(account_config.to_dict(), f, indent=2, ensure_ascii=False)
            
            # 更新内存中的配置
            self.accounts[account_config.account_id] = account_config
            
            self.logger.info(f"保存账户配置: {account_config.email}")
            
        except Exception as e:
            self.logger.error(f"保存账户配置失败: {e}")
            raise
    
    def add_account(self, config_data: Dict[str, Any]) -> bool:
        """添加新账户"""
        try:
            # 生成账户ID
            account_id = config_data['email'].replace('@', '_').replace('.', '_')
            config_data['account_id'] = account_id

            # 创建账户配置
            account_config = RealAccountConfig.from_dict(config_data)

            # 检查是否有现有的邮件数据
            existing_emails = self.check_existing_emails(account_id)
            if existing_emails > 0:
                self.logger.info(f"发现账户 {account_config.email} 的现有邮件数据: {existing_emails} 封")

            # 测试账户连接
            if not self.test_account_connection(account_config):
                return False

            # 保存账户
            self.save_account(account_config)

            # 如果账户启用，开始同步
            if account_config.enabled:
                self.start_account_sync(account_id)

            return True

        except Exception as e:
            self.logger.error(f"添加账户失败: {e}")
            return False

    def check_existing_emails(self, account_id: str) -> int:
        """检查账户是否有现有的邮件数据"""
        try:
            return self.database.count_emails_by_account(account_id)
        except Exception as e:
            self.logger.error(f"检查现有邮件失败: {e}")
            return 0
    
    def remove_account(self, account_id: str, preserve_emails: bool = True) -> bool:
        """删除账户

        Args:
            account_id: 账户ID
            preserve_emails: 是否保留邮件数据，默认为True
        """
        try:
            if account_id not in self.accounts:
                return False

            account_config = self.accounts[account_id]
            self.logger.info(f"开始删除账户: {account_config.email} (保留邮件: {preserve_emails})")

            # 1. 停止同步
            self.stop_account_sync(account_id)

            # 2. 删除客户端
            if account_id in self.clients:
                try:
                    # 尝试关闭客户端连接
                    client = self.clients[account_id]
                    if hasattr(client, 'disconnect'):
                        client.disconnect()
                except Exception as e:
                    self.logger.warning(f"关闭客户端连接失败: {e}")
                del self.clients[account_id]

            # 3. 根据参数决定是否删除邮件数据
            if not preserve_emails:
                try:
                    deleted_emails = self.database.delete_emails_by_account(account_id)
                    self.logger.info(f"删除了 {deleted_emails} 封邮件数据")
                except Exception as e:
                    self.logger.error(f"删除邮件数据失败: {e}")
                    # 继续执行，不因为邮件删除失败而中断账户删除
            else:
                self.logger.info(f"保留账户 {account_config.email} 的邮件数据")

            # 4. 删除配置文件
            config_file = self.config_dir / f"account_{account_config.email.replace('@', '_').replace('.', '_')}.json"
            if config_file.exists():
                config_file.unlink()
                self.logger.info(f"删除配置文件: {config_file}")

            # 5. 从内存中删除
            del self.accounts[account_id]

            self.logger.info(f"账户删除完成: {account_config.email}")
            return True

        except Exception as e:
            self.logger.error(f"删除账户失败: {e}")
            return False

    def remove_account_with_emails(self, account_id: str) -> bool:
        """删除账户及其所有邮件数据（完全删除）"""
        return self.remove_account(account_id, preserve_emails=False)

    def remove_account_config_only(self, account_id: str) -> bool:
        """仅删除账户配置，保留邮件数据"""
        return self.remove_account(account_id, preserve_emails=True)
    
    def test_account_connection(self, account_config: RealAccountConfig) -> bool:
        """测试账户连接"""
        try:
            self.logger.info(f"测试账户连接: {account_config.email}")
            
            # 创建客户端
            client = ProductionOptimizedClientV2(
                client_id=account_config.client_id,
                email=account_config.email
            )
            
            # 设置账户配置并获取访问令牌
            client.set_account_config(
                account_config.email,
                account_config.client_id,
                account_config.refresh_token
            )
            if not client.get_access_token_optimized(force_refresh=True):
                self.logger.error("获取访问令牌失败")
                return False
            
            # 连接IMAP
            if not client.connect_and_authenticate_fast():
                self.logger.error("IMAP连接失败")
                return False
            
            # 获取文件夹列表
            folders = client.list_folders_optimized()
            if not folders:
                self.logger.error("获取文件夹列表失败")
                return False
            
            self.logger.info(f"账户连接测试成功: {account_config.email}")
            return True
            
        except Exception as e:
            self.logger.error(f"账户连接测试失败: {e}")
            return False
    
    def get_client(self, account_id: str) -> Optional[ProductionOptimizedClientV2]:
        """获取账户客户端"""
        try:
            if account_id not in self.accounts:
                return None
            
            if account_id not in self.clients:
                # 创建新客户端
                account_config = self.accounts[account_id]
                client = ProductionOptimizedClientV2(
                    client_id=account_config.client_id,
                    email=account_config.email
                )
                
                # 设置账户配置并获取访问令牌
                client.set_account_config(
                    account_config.email,
                    account_config.client_id,
                    account_config.refresh_token
                )
                if not client.get_access_token_optimized(force_refresh=True):
                    self.logger.error(f"获取访问令牌失败: {account_config.email}")
                    return None
                
                # 连接IMAP
                if not client.connect_and_authenticate_fast():
                    self.logger.error(f"IMAP连接失败: {account_config.email}")
                    return None
                
                self.clients[account_id] = client
            
            return self.clients[account_id]
            
        except Exception as e:
            self.logger.error(f"获取客户端失败: {e}")
            return None
    
    def sync_account_emails(self, account_id: str, folder_name: str = "INBOX") -> List[EmailRecord]:
        """同步账户邮件（智能增量同步）"""
        # 添加调试日志来追踪调用来源
        import traceback
        self.logger.warning(f"调用了旧的sync_account_emails方法，账户: {account_id}")
        self.logger.warning("调用栈:")
        for line in traceback.format_stack()[-5:]:
            self.logger.warning(line.strip())
        return self.sync_account_emails_smart(account_id, folder_name)

    def sync_account_emails_smart(self, account_id: str, folder_name: str = "INBOX") -> List[EmailRecord]:
        """智能同步账户邮件（增量同步）- 终极时区防护版"""
        try:
            # 终极时区防护：在方法入口处立即修复所有可能的时区问题
            self._ensure_account_timezone_consistency(account_id)

            if account_id not in self.accounts:
                return []

            account_config = self.accounts[account_id]
            client = self.get_client(account_id)

            if not client:
                return []

            # 获取同步状态（带时区防护）
            sync_state = self.database.get_account_sync_state(account_id)
            if not sync_state:
                self.logger.error(f"无法获取账户同步状态: {account_id}")
                return []

            # 再次确保同步状态的时区一致性
            sync_state = self._ensure_sync_state_timezone_consistency(sync_state)

            # 确定同步策略（带终极防护）
            sync_strategy = self._determine_sync_strategy_safe(sync_state)
            self.logger.info(f"开始智能同步邮件: {account_config.email}/{folder_name}, 策略: {sync_strategy}")

            # 根据策略执行同步
            if sync_strategy == "first_sync":
                fetch_result = client.fetch_emails_first_time(
                    folder_name=folder_name,
                    limit=sync_state.first_sync_limit,
                    fetch_body=True,
                    fetch_attachments=account_config.download_attachments
                )
            elif sync_strategy == "incremental":
                search_criteria = self._build_incremental_search_criteria(sync_state)
                fetch_result = client.fetch_emails_incremental(
                    folder_name=folder_name,
                    search_criteria=search_criteria,
                    limit=50,  # 增量同步限制
                    fetch_body=True,
                    fetch_attachments=account_config.download_attachments
                )
            else:  # full_sync
                fetch_result = client.fetch_emails_from_folder(
                    folder_name=folder_name,
                    limit=account_config.max_emails,
                    fetch_body=True,
                    fetch_attachments=account_config.download_attachments
                )

            if not fetch_result.success:
                self.logger.error(f"获取邮件失败: {fetch_result.error_message}")
                return []

            synced_emails = []
            self.logger.info(f"开始处理 {len(fetch_result.emails)} 封邮件")

            for i, email_content in enumerate(fetch_result.emails):
                message_id = None
                try:
                    # 检查邮件是否已存在（使用邮件的某个唯一标识）
                    header = email_content.header
                    # 使用更可靠的message_id生成方式
                    if header.message_id:
                        message_id = f"{account_id}_{folder_name}_{header.message_id}"
                        self.logger.debug(f"使用原始message_id: {message_id}")
                    else:
                        # 如果没有message_id，使用主题、发送者、日期的组合生成唯一标识
                        unique_str = f"{header.subject}_{header.sender}_{header.date}_{header.size}"
                        message_id = f"{account_id}_{folder_name}_{hash(unique_str)}"
                        self.logger.debug(f"生成新message_id: {message_id}")

                    existing_email = self.database.get_email_by_message_id(
                        account_id, message_id
                    )

                    if existing_email:
                        self.logger.debug(f"邮件已存在，跳过: {header.subject[:50]}")
                        continue  # 邮件已存在，跳过

                    # 创建邮件记录
                    email_record = EmailRecord(
                        account_id=account_id,
                        folder_name=folder_name,
                        message_id=message_id,
                        uid=f"{account_id}_{folder_name}_{i+1}_{hash(message_id)}",  # 生成唯一UID
                        subject=header.subject or "(无主题)",
                        sender=header.sender or "",
                        recipients=','.join(header.recipients) if header.recipients else "",
                        date_sent=header.date or datetime.now(),
                        size=header.size or 0,
                        flags=','.join(header.flags) if header.flags else "",
                        text_body=email_content.text_body or "",
                        html_body=email_content.html_body or "",
                        created_at=datetime.now()
                    )

                    # 保存到数据库
                    email_id = self.database.save_email(email_record)
                    if email_id:
                        email_record.id = email_id
                        synced_emails.append(email_record)
                        self.logger.info(f"成功保存邮件: {header.subject[:50]} (ID: {email_id})")
                    else:
                        self.logger.error(f"保存邮件失败: {header.subject[:50]}")

                except Exception as e:
                    error_id = message_id if message_id else f"邮件#{i+1}"
                    self.logger.error(f"同步邮件失败: {error_id}, {e}")
                    continue
            
            # 更新最后同步时间
            account_config.last_sync = datetime.now()
            self.save_account(account_config)

            self.logger.info(f"同步完成: {account_config.email}, 新邮件: {len(synced_emails)}")

            # 调用同步完成回调
            if self.sync_callback and len(synced_emails) > 0:
                try:
                    self.sync_callback(account_id, len(synced_emails))
                except Exception as e:
                    self.logger.error(f"调用同步回调失败: {e}")

            # 更新同步状态
            max_uid = max([email.header.uid for email in fetch_result.emails]) if fetch_result.emails else 0
            self.database.update_sync_state_after_sync(
                account_id=account_id,
                sync_type=sync_strategy,
                new_emails_count=len(synced_emails),
                max_uid=max_uid
            )

            self.logger.info(f"智能同步完成: {account_config.email}, 策略: {sync_strategy}, 新邮件: {len(synced_emails)}")
            return synced_emails

        except Exception as e:
            self.logger.error(f"智能同步账户邮件失败: {e}")
            # 如果是时区错误，尝试最后的修复
            if "can't subtract offset-naive and offset-aware datetimes" in str(e):
                self.logger.error("🚨 在智能同步中检测到时区错误，尝试紧急修复...")
                try:
                    self._emergency_timezone_fix(account_id)
                    self.logger.info("紧急时区修复完成，返回空结果以避免崩溃")
                except Exception as fix_error:
                    self.logger.error(f"紧急时区修复失败: {fix_error}")
            return []

    def _ensure_account_timezone_consistency(self, account_id: str):
        """确保账户的时区一致性"""
        try:
            from datetime import datetime, timezone

            # 获取并修复账户的同步状态
            sync_state = self.database.get_account_sync_state(account_id)
            if sync_state:
                modified = False

                # 检查并修复所有时间字段
                if sync_state.last_full_sync_time and sync_state.last_full_sync_time.tzinfo is None:
                    sync_state.last_full_sync_time = sync_state.last_full_sync_time.replace(tzinfo=timezone.utc)
                    modified = True
                    self.logger.warning(f"修复账户 {account_id} 的 last_full_sync_time 时区")

                if sync_state.last_incremental_sync_time and sync_state.last_incremental_sync_time.tzinfo is None:
                    sync_state.last_incremental_sync_time = sync_state.last_incremental_sync_time.replace(tzinfo=timezone.utc)
                    modified = True
                    self.logger.warning(f"修复账户 {account_id} 的 last_incremental_sync_time 时区")

                if sync_state.created_at and sync_state.created_at.tzinfo is None:
                    sync_state.created_at = sync_state.created_at.replace(tzinfo=timezone.utc)
                    modified = True

                if sync_state.updated_at and sync_state.updated_at.tzinfo is None:
                    sync_state.updated_at = sync_state.updated_at.replace(tzinfo=timezone.utc)
                    modified = True

                # 如果有修改，保存回数据库
                if modified:
                    self.database.save_account_sync_state(sync_state)
                    self.logger.info(f"账户 {account_id} 时区一致性修复完成")

        except Exception as e:
            self.logger.error(f"确保账户时区一致性失败: {e}")

    def _ensure_sync_state_timezone_consistency(self, sync_state):
        """确保同步状态的时区一致性"""
        try:
            from datetime import timezone

            # 创建一个副本以避免修改原对象
            if sync_state.last_full_sync_time and sync_state.last_full_sync_time.tzinfo is None:
                sync_state.last_full_sync_time = sync_state.last_full_sync_time.replace(tzinfo=timezone.utc)

            if sync_state.last_incremental_sync_time and sync_state.last_incremental_sync_time.tzinfo is None:
                sync_state.last_incremental_sync_time = sync_state.last_incremental_sync_time.replace(tzinfo=timezone.utc)

            return sync_state

        except Exception as e:
            self.logger.error(f"确保同步状态时区一致性失败: {e}")
            return sync_state

    def _determine_sync_strategy_safe(self, sync_state) -> str:
        """安全的同步策略确定（终极防护版）"""
        try:
            # 使用原有的增强版方法
            return self._determine_sync_strategy(sync_state)
        except Exception as e:
            self.logger.error(f"安全同步策略确定失败: {e}")
            # 如果是时区错误，默认使用增量同步
            if "can't subtract offset-naive and offset-aware datetimes" in str(e):
                self.logger.warning("时区错误导致策略确定失败，使用增量同步作为默认策略")
                return "incremental"
            else:
                return "incremental"

    def _emergency_timezone_fix(self, account_id: str):
        """紧急时区修复"""
        try:
            from datetime import datetime, timezone

            self.logger.info(f"开始紧急时区修复: {account_id}")

            # 强制重新创建同步状态
            sync_state = self.database.get_account_sync_state(account_id)
            if sync_state:
                # 强制所有时间字段使用UTC
                current_utc = datetime.now(timezone.utc)

                sync_state.last_full_sync_time = current_utc
                sync_state.last_incremental_sync_time = current_utc
                sync_state.created_at = current_utc
                sync_state.updated_at = current_utc

                # 保存修复后的状态
                self.database.save_account_sync_state(sync_state)
                self.logger.info(f"紧急时区修复完成: {account_id}")

        except Exception as e:
            self.logger.error(f"紧急时区修复失败: {e}")

    def _determine_sync_strategy(self, sync_state) -> str:
        """确定同步策略（增强版时区防护）"""
        try:
            from datetime import datetime, timedelta, timezone

            # 首次同步
            if sync_state.is_first_sync:
                return "first_sync"

            # 检查是否需要强制全量同步
            if sync_state.last_full_sync_time:
                try:
                    # 多重时区防护
                    current_time = datetime.now(timezone.utc)
                    last_full_time = sync_state.last_full_sync_time

                    # 防护1: 确保current_time有时区
                    if current_time.tzinfo is None:
                        current_time = current_time.replace(tzinfo=timezone.utc)
                        self.logger.warning("current_time缺少时区信息，已修复为UTC")

                    # 防护2: 确保last_full_time有时区
                    if last_full_time.tzinfo is None:
                        last_full_time = last_full_time.replace(tzinfo=timezone.utc)
                        self.logger.warning("last_full_time缺少时区信息，已修复为UTC")

                    # 防护3: 验证时区类型
                    if not hasattr(current_time, 'tzinfo') or not hasattr(last_full_time, 'tzinfo'):
                        self.logger.error("datetime对象缺少tzinfo属性")
                        return "incremental"

                    # 安全的时间差计算
                    time_diff = current_time - last_full_time
                    days_since_last_full = time_diff.days

                    if days_since_last_full >= sync_state.force_full_sync_days:
                        self.logger.info(f"距离上次全量同步已 {days_since_last_full} 天，执行强制全量同步")
                        return "full_sync"

                except Exception as time_error:
                    self.logger.error(f"时间比较失败: {time_error}")
                    self.logger.error(f"current_time: {current_time} (type: {type(current_time)})")
                    self.logger.error(f"last_full_time: {last_full_time} (type: {type(last_full_time)})")
                    # 时间比较失败时，默认使用增量同步
                    return "incremental"

            # 默认增量同步
            return "incremental"

        except Exception as e:
            self.logger.error(f"确定同步策略失败: {e}")
            import traceback
            self.logger.error("详细错误信息:")
            for line in traceback.format_exc().split('\n'):
                if line.strip():
                    self.logger.error(f"  {line}")
            return "incremental"  # 默认增量同步

    def _build_incremental_search_criteria(self, sync_state) -> str:
        """构建增量同步的搜索条件（增强版时区防护）"""
        try:
            from datetime import datetime, timedelta, timezone

            # 基于时间的搜索
            since_date = None

            if sync_state.last_incremental_sync_time:
                try:
                    since_date = sync_state.last_incremental_sync_time

                    # 多重时区防护
                    if since_date.tzinfo is None:
                        since_date = since_date.replace(tzinfo=timezone.utc)
                        self.logger.warning("last_incremental_sync_time缺少时区信息，已修复为UTC")

                    # 验证时间对象的有效性
                    if not isinstance(since_date, datetime):
                        self.logger.error(f"since_date不是datetime对象: {type(since_date)}")
                        since_date = None

                except Exception as date_error:
                    self.logger.error(f"处理last_incremental_sync_time失败: {date_error}")
                    since_date = None

            # 如果没有有效的上次同步时间，回溯指定天数
            if since_date is None:
                try:
                    current_time = datetime.now(timezone.utc)
                    since_date = current_time - timedelta(days=sync_state.incremental_days_back)
                    self.logger.info(f"使用回溯时间: {since_date}")
                except Exception as fallback_error:
                    self.logger.error(f"创建回溯时间失败: {fallback_error}")
                    # 最后的防护：使用固定的回溯时间
                    since_date = datetime.now(timezone.utc) - timedelta(days=7)

            # 安全的时区转换
            try:
                # 转换为本地时间用于IMAP搜索
                local_since_date = since_date.astimezone()

                # 格式化为IMAP搜索条件
                search_criteria = f"SINCE {local_since_date.strftime('%d-%b-%Y')}"

            except Exception as format_error:
                self.logger.error(f"时间格式化失败: {format_error}")
                # 使用UTC时间直接格式化
                search_criteria = f"SINCE {since_date.strftime('%d-%b-%Y')}"

            # 如果有UID信息，添加UID条件（更精确）
            try:
                if hasattr(sync_state, 'max_uid_synced') and sync_state.max_uid_synced > 0:
                    search_criteria += f" UID {sync_state.max_uid_synced + 1}:*"
            except Exception as uid_error:
                self.logger.error(f"添加UID条件失败: {uid_error}")

            self.logger.debug(f"增量同步搜索条件: {search_criteria}")
            return search_criteria

        except Exception as e:
            self.logger.error(f"构建搜索条件失败: {e}")
            import traceback
            self.logger.error("详细错误信息:")
            for line in traceback.format_exc().split('\n'):
                if line.strip():
                    self.logger.error(f"  {line}")
            return "ALL"  # 默认搜索所有邮件
    
    def start_account_sync(self, account_id: str):
        """开始账户同步"""
        try:
            if account_id not in self.accounts:
                return
            
            if account_id in self.sync_threads and self.sync_threads[account_id].is_alive():
                return  # 同步已在进行中
            
            account_config = self.accounts[account_id]
            if not account_config.enabled:
                return
            
            # 创建同步线程
            sync_thread = threading.Thread(
                target=self._sync_worker,
                args=(account_id,),
                daemon=True
            )
            sync_thread.start()
            self.sync_threads[account_id] = sync_thread
            
            self.logger.info(f"开始账户同步: {account_config.email}")
            
        except Exception as e:
            self.logger.error(f"启动账户同步失败: {e}")
    
    def stop_account_sync(self, account_id: str):
        """停止账户同步"""
        try:
            if account_id in self.sync_threads:
                # 设置停止标志
                self.stop_sync.set()
                
                # 等待线程结束
                sync_thread = self.sync_threads[account_id]
                if sync_thread.is_alive():
                    sync_thread.join(timeout=5)
                
                del self.sync_threads[account_id]
                
                # 清除停止标志
                self.stop_sync.clear()
            
            account_config = self.accounts.get(account_id)
            if account_config:
                self.logger.info(f"停止账户同步: {account_config.email}")
            
        except Exception as e:
            self.logger.error(f"停止账户同步失败: {e}")
    
    def _sync_worker(self, account_id: str):
        """同步工作线程"""
        try:
            account_config = self.accounts[account_id]
            
            while not self.stop_sync.is_set() and account_config.enabled:
                try:
                    # 使用智能同步INBOX文件夹
                    self.sync_account_emails_smart(account_id, "INBOX")
                    
                    # 等待下次同步
                    for _ in range(account_config.sync_interval * 60):  # 转换为秒
                        if self.stop_sync.is_set():
                            break
                        time.sleep(1)
                    
                except Exception as e:
                    self.logger.error(f"同步工作线程错误: {e}")
                    time.sleep(30)  # 出错后等待30秒再重试
            
        except Exception as e:
            self.logger.error(f"同步工作线程异常: {e}")
    
    def start_all_syncs(self):
        """启动所有启用账户的同步"""
        for account_id, account_config in self.accounts.items():
            if account_config.enabled:
                self.start_account_sync(account_id)
    
    def stop_all_syncs(self):
        """停止所有同步"""
        for account_id in list(self.sync_threads.keys()):
            self.stop_account_sync(account_id)
    
    def get_account_folders(self, account_id: str) -> List[str]:
        """获取账户文件夹列表"""
        try:
            client = self.get_client(account_id)
            if not client:
                return []
            
            return client.list_folders_optimized()
            
        except Exception as e:
            self.logger.error(f"获取账户文件夹失败: {e}")
            return []
    
    def get_account_emails(self, account_id: str, folder_name: str = "INBOX", 
                          limit: int = 100, offset: int = 0) -> List[EmailRecord]:
        """获取账户邮件"""
        try:
            return self.database.get_emails_by_account(
                account_id, folder_name, limit, offset
            )
        except Exception as e:
            self.logger.error(f"获取账户邮件失败: {e}")
            return []
    
    def get_all_accounts(self) -> Dict[str, RealAccountConfig]:
        """获取所有账户配置"""
        return self.accounts.copy()

    def get_enabled_accounts(self) -> List[RealAccountConfig]:
        """获取启用的账户列表"""
        return [config for config in self.accounts.values() if config.enabled]

    def get_account_config(self, account_id: str) -> Optional[RealAccountConfig]:
        """获取账户配置"""
        return self.accounts.get(account_id)
    
    def update_account_config(self, account_id: str, updates: Dict[str, Any]) -> bool:
        """更新账户配置"""
        try:
            if account_id not in self.accounts:
                return False
            
            account_config = self.accounts[account_id]
            
            # 更新配置
            for key, value in updates.items():
                if hasattr(account_config, key):
                    setattr(account_config, key, value)
            
            # 保存配置
            self.save_account(account_config)
            
            # 如果启用状态改变，重新启动同步
            if 'enabled' in updates:
                if updates['enabled']:
                    self.start_account_sync(account_id)
                else:
                    self.stop_account_sync(account_id)
            
            return True
            
        except Exception as e:
            self.logger.error(f"更新账户配置失败: {e}")
            return False
