#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的数据库初始化脚本
避免复杂的Flask应用初始化问题
"""

import os
import json
from datetime import datetime, timedelta
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 设置最小化的Flask应用
from flask import Flask
from models import db, License, Admin

def init_database():
    """初始化数据库"""
    print("🗄️  初始化数据库...")
    
    app = Flask(__name__)
    app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'dev-key')
    app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL', 'sqlite:///license_server_prod.db')
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    db.init_app(app)
    
    with app.app_context():
        print("✅ 创建数据库表...")
        db.create_all()
        print("✅ 数据库表创建完成")
        
        # 创建默认管理员
        admin_username = os.environ.get('ADMIN_USERNAME', 'admin')
        admin_password = os.environ.get('ADMIN_PASSWORD', 'admin123')
        
        existing_admin = Admin.query.filter_by(username=admin_username).first()
        if not existing_admin:
            admin = Admin(
                username=admin_username,
                email='<EMAIL>'
            )
            admin.set_password(admin_password)
            db.session.add(admin)
            db.session.commit()
            print(f"✅ 创建默认管理员: {admin_username}")
        else:
            print(f"ℹ️  管理员已存在: {admin_username}")
        
        # 创建示例许可证
        print("📝 创建示例许可证...")
        
        sample_licenses = [
            {
                'license_type': 'standard',
                'max_activations': 1,
                'expires_days': 365,
                'user_name': '测试用户',
                'user_email': '<EMAIL>',
                'company_name': '测试公司',
                'features': ['email_management'],
                'notes': '这是一个测试许可证'
            },
            {
                'license_type': 'premium',
                'max_activations': 3,
                'expires_days': 730,
                'user_name': '高级用户',
                'user_email': '<EMAIL>',
                'company_name': '高级公司',
                'features': ['email_management', 'batch_import', 'advanced_search'],
                'notes': '高级版许可证示例'
            },
            {
                'license_type': 'enterprise',
                'max_activations': 10,
                'expires_days': None,  # 永久
                'user_name': '企业用户',
                'user_email': '<EMAIL>',
                'company_name': '企业公司',
                'features': ['email_management', 'batch_import', 'advanced_search', 'auto_sync', 'multi_account'],
                'notes': '企业版许可证示例（永久有效）'
            }
        ]
        
        created_count = 0
        for license_data in sample_licenses:
            # 创建许可证
            license_obj = License(
                license_type=license_data['license_type'],
                max_activations=license_data['max_activations'],
                user_name=license_data['user_name'],
                user_email=license_data['user_email'],
                company_name=license_data['company_name'],
                features=json.dumps(license_data['features']),
                notes=license_data['notes']
            )
            
            # 设置过期时间
            if license_data['expires_days']:
                license_obj.expires_at = datetime.utcnow() + timedelta(days=license_data['expires_days'])
            
            db.session.add(license_obj)
            created_count += 1
        
        db.session.commit()
        print(f"✅ 创建了 {created_count} 个示例许可证")
        
        # 显示创建的许可证
        print("\n📋 创建的示例许可证:")
        licenses = License.query.all()
        for license_obj in licenses:
            print(f"   {license_obj.license_key} - {license_obj.license_type} - {license_obj.user_name}")
        
        print("🎉 数据库初始化完成！")


if __name__ == '__main__':
    init_database()
