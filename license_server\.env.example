# 许可证服务器环境配置文件示例
# 复制此文件为 .env 并修改相应的值

# Flask环境配置
FLASK_ENV=production
SECRET_KEY=your-secret-key-here-change-this-in-production
JWT_SECRET_KEY=your-jwt-secret-key-here-change-this-in-production

# 服务器配置
HOST=0.0.0.0
PORT=8080
DEBUG=False

# 数据库配置
DATABASE_URL=sqlite:///license_server_prod.db

# 许可证配置
DEFAULT_LICENSE_DURATION_DAYS=365
MAX_ACTIVATIONS_PER_LICENSE=1

# 速率限制配置
RATELIMIT_STORAGE_URL=memory://
RATELIMIT_DEFAULT=100 per hour

# 管理员配置
ADMIN_USERNAME=admin
ADMIN_PASSWORD=change-this-password-in-production

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=license_server.log

# 邮件配置（可选）
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password
