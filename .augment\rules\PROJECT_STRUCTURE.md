---
type: "agent_requested"
description: "# Project Rules"
---
# Project Rules
-1.Please always reply in Chinese 
-2..Instead of creating simple test scripts to verify the code after each generation, I will test it and give you the verification results.
-3.. Let me know when you have written your code and need it tested, and I will test it for you immediately.  
What I will do during testing:  
Run the code on the computer terminal.  
Send you the log records generated by the program.  
Important requirement: Remember to add log recording functionality every time you write code.
Logs act like a “black box” for the program, recording the specific details of its operation.  
The more detailed the logs, the faster and more accurately I can identify the source of the error.  
Once the issue is identified, it can be corrected immediately.  
Simple rule of thumb: Write more logs → Facilitate testing → Quickly troubleshoot → Immediately fix.  
It's like a car equipped with numerous sensors—when something goes wrong, the dashboard immediately displays the issue, and the mechanic can instantly identify the problem area.
-4. Don't create new files repeatedly: For example, if you write a documentation file called User Guide.md today, then write another one called User Guide_v2.md tomorrow, and then write another one called User Guide_Latest Version.md the day after tomorrow... this will result in an increasing number of files, scattered content, and difficulty in management.
Edit the original file directly: Always use the same ‘User Guide.md’ file. Whenever updates are needed, directly modify or supplement the content of this file. This ensures the document remains the ‘latest version’ and avoids version confusion.
Analogy example:  
Like taking notes—  
❌ Incorrect approach: Start a new notebook every day, ending up with 10 notebooks, making it difficult to find information.
✅ Correct approach: Use only one notebook, adding new content at the end or modifying existing content, keeping all information centralised.
The benefits of this approach are: avoiding duplication, facilitating searchability, and maintaining content consistency.
-5. Translate the user's Chinese message into English before understanding and processing it.
First, convert the user's Chinese message into English.
Based on the understanding of the user's intention in the English version,
then reply to the user in Chinese.
-6.All use PowerShell commands