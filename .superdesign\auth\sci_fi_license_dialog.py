#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
科幻风格授权成功对话框 - PySide6 & QWebEngineView 版本
"""

import sys
import os
import json
from typing import Dict, Any

from PySide6.QtWidgets import QDialog, QVBoxLayout
from PySide6.QtCore import Qt, QObject, Signal, Slot, QUrl
from PySide6.QtWebEngineWidgets import QWebEngineView
from PySide6.QtWebChannel import QWebChannel


class Bridge(QObject):
    """用于在Python和JavaScript之间通信的桥梁"""
    # 从JS接收到信号后，再发射出去给Python主逻辑
    start_application = Signal()
    close_dialog = Signal()

    @Slot()
    def start(self):
        """由JS调用，当用户点击'开始使用'按钮时"""
        print("Bridge: Received 'start' signal from JavaScript.")
        self.start_application.emit()

    @Slot()
    def close(self):
        """由JS调用，当用户点击'关闭'或'断开连接'按钮时"""
        print("Bridge: Received 'close' signal from JavaScript.")
        self.close_dialog.emit()


class SciFiLicenseDialog(QDialog):
    """
    使用QWebEngineView加载HTML/CSS/JS界面的授权成功对话框
    """
    start_application = Signal()

    def __init__(self, license_data: Dict[str, Any], parent=None):
        super().__init__(parent)
        self.license_data = license_data
        self.drag_position = None

        self.setWindowTitle("授权验证同步")
        self.setFixedSize(600, 860) # 增加高度以完整显示所有内容
        self.setModal(True)

        # --- Frameless Window Setup ---
        self.setWindowFlags(Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)

        self._setup_ui()
        self._setup_web_channel()

    def _setup_ui(self):
        """设置UI"""
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)

        self.webview = QWebEngineView()
        self.layout.addWidget(self.webview)

        # 构建HTML文件的绝对路径
        # 这是关键一步，确保无论从哪里运行脚本都能找到HTML文件
        current_dir = os.path.dirname(os.path.abspath(__file__))
        # 假设HTML文件在.superdesign/design_iterations/
        # 我们需要根据实际结构调整路径
        # 从 auth/ -> .superdesign/
        html_path = os.path.join(current_dir, '..', 'design_iterations', 'license_dialog_redesign_1_2.html')
        html_path = os.path.normpath(html_path)
        
        print(f"Attempting to load HTML from: {html_path}")

        if not os.path.exists(html_path):
            print(f"Error: HTML file not found at {html_path}")
            self.webview.setHtml(f"<h1>Error: File not found</h1><p>{html_path}</p>")
            return

        try:
            with open(html_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            # The base URL is the directory containing the HTML file.
            # This is crucial for resolving relative paths for CSS, JS, etc.
            base_url = QUrl.fromLocalFile(os.path.dirname(html_path) + os.path.sep)
            self.webview.setHtml(html_content, baseUrl=base_url)

        except IOError as e:
            print(f"Error reading HTML file: {e}")
            self.webview.setHtml(f"<h1>Error reading file</h1><p>{e}</p>")
            return

    def _setup_web_channel(self):
        """设置Python和JS之间的通信"""
        self.channel = QWebChannel()
        self.bridge = Bridge()
        
        # 将Python的信号连接到对话框的槽
        self.bridge.start_application.connect(self._on_start_application)
        self.bridge.close_dialog.connect(self.accept)

        # 将Python对象暴露给JavaScript
        self.channel.registerObject("py_bridge", self.bridge)
        self.webview.page().setWebChannel(self.channel)

        # 当网页加载完成后，将许可证数据发送给JS
        self.webview.loadFinished.connect(self._send_data_to_js)

    def _send_data_to_js(self, ok):
        """将license_data发送到JavaScript"""
        if ok:
            print("Web page loaded successfully. Sending data to JS...")
            # 将Python字典转换为JSON字符串
            data_json = json.dumps(self.license_data)
            # 调用在HTML中定义的全局JS函数
            js_code = f"updateUI({data_json});"
            self.webview.page().runJavaScript(js_code)
        else:
            print("Web page failed to load.")

    @Slot()
    def _on_start_application(self):
        """处理来自bridge的开始信号"""
        self.start_application.emit()
        self.accept()

    def closeEvent(self, event):
        """确保WebEngine被正确清理"""
        self.webview.page().setWebChannel(None)
        self.channel.deregisterObject(self.bridge)
        super().closeEvent(event)

    # --- Window Dragging Implementation ---
    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            event.accept()

    def mouseMoveEvent(self, event):
        if self.drag_position is not None and event.buttons() == Qt.LeftButton:
            self.move(event.globalPosition().toPoint() - self.drag_position)
            event.accept()


