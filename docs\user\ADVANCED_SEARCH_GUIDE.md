# 🔍 高级搜索功能使用指南

## 🎉 新功能完成！

✅ **专业级高级搜索功能已全面实现！**

现在您可以像使用Gmail或Outlook一样进行强大的邮件搜索：多条件搜索、实时搜索、搜索历史等。

## 🚀 主要功能特点

### 1. **多层次搜索界面** 📱
- **基本搜索**: 快速关键词搜索，支持实时搜索
- **高级搜索**: 专业的多条件搜索对话框
- **搜索历史**: 保存和重用搜索条件

### 2. **强大的搜索引擎** ⚡
- **数据库级搜索**: 高效的SQL查询，支持大量邮件
- **多字段搜索**: 主题、发件人、收件人、内容全覆盖
- **智能匹配**: 大小写不敏感，支持模糊匹配

### 3. **实时搜索体验** 🌐
- **即时反馈**: 输入时自动搜索，500ms延迟优化
- **智能防抖**: 避免频繁查询，提升性能
- **状态提示**: 实时显示搜索结果数量

### 4. **专业搜索条件** 🎯
- **时间范围**: 今天、本周、本月、自定义日期
- **阅读状态**: 已读、未读邮件筛选
- **邮件大小**: 按文件大小范围搜索
- **组合条件**: 多个条件同时使用

## 📋 界面布局

### 工具栏搜索区域
```
┌─────────────────────────────────────────────────────────┐
│ 搜索: [搜索框____________] [搜索] [高级搜索]              │
└─────────────────────────────────────────────────────────┘
```

### 高级搜索对话框
```
┌─────────────────────────────────────────────────────────┐
│ [基本搜索] [高级搜索] [搜索历史]                        │
├─────────────────────────────────────────────────────────┤
│ 快速搜索                                                │
│ 关键词: [________________] 范围: [全部内容 ▼]           │
│                                                         │
│ 时间范围                                                │
│ 时间: [不限制 ▼] 自定义: [开始日期] 到 [结束日期]       │
│                                                         │
│ 其他选项                                                │
│ ☐ 区分大小写  ☐ 全词匹配  ☐ 包含有附件的邮件           │
├─────────────────────────────────────────────────────────┤
│              [搜索] [重置] [保存搜索] [取消]             │
└─────────────────────────────────────────────────────────┘
```

## 🎯 使用方法

### 方法1: 快速搜索

#### 实时搜索
```
1. 在工具栏搜索框中输入关键词
2. 系统自动进行实时搜索（500ms延迟）
3. 邮件列表立即更新显示结果
4. 状态栏显示搜索结果数量
```

#### 手动搜索
```
1. 在搜索框中输入关键词
2. 按Enter键或点击"搜索"按钮
3. 查看搜索结果
```

### 方法2: 高级搜索

#### 打开高级搜索
```
1. 点击工具栏的"高级搜索"按钮
2. 弹出高级搜索对话框
3. 选择合适的搜索标签页
```

#### 基本搜索标签页
```
1. 输入关键词
2. 选择搜索范围（全部内容/仅主题/仅发件人等）
3. 设置时间范围
4. 选择其他选项
5. 点击"搜索"按钮
```

#### 高级搜索标签页
```
1. 分别输入主题、发件人、收件人、内容条件
2. 设置邮件属性（阅读状态、文件夹、大小）
3. 组合多个条件
4. 点击"搜索"按钮
```

### 方法3: 搜索历史

#### 使用搜索历史
```
1. 在高级搜索对话框中切换到"搜索历史"标签页
2. 查看之前的搜索记录
3. 双击或选择后点击"加载选中"
4. 搜索条件自动填入表单
```

#### 预设搜索
```
1. 使用内置的预设搜索按钮
2. 如"今天的未读邮件"、"有附件的邮件"等
3. 一键执行常用搜索
```

## 🔧 功能详解

### 1. **实时搜索** ⚡

#### 工作原理
- **输入监听**: 监听搜索框文本变化
- **延迟处理**: 500ms延迟避免频繁查询
- **自动搜索**: 文本变化后自动执行搜索
- **即时清空**: 清空搜索框立即恢复所有邮件

#### 性能优化
- **防抖机制**: 避免用户快速输入时的多次查询
- **查询限制**: 限制返回结果数量（1000封）
- **索引优化**: 数据库查询使用COLLATE NOCASE优化

### 2. **多条件搜索** 🎯

#### 支持的搜索字段
- **主题**: 邮件标题关键词搜索
- **发件人**: 发件人邮箱或姓名搜索
- **收件人**: 收件人邮箱或姓名搜索
- **内容**: 邮件正文内容搜索（文本和HTML）

#### 搜索范围选项
- **全部内容**: 搜索主题、发件人、内容
- **仅主题**: 只在邮件标题中搜索
- **仅发件人**: 只在发件人字段中搜索
- **仅内容**: 只在邮件正文中搜索
- **主题和发件人**: 在标题和发件人中搜索

### 3. **时间范围搜索** 📅

#### 预设时间范围
- **今天**: 当天的邮件
- **昨天**: 昨天的邮件
- **本周**: 本周的邮件
- **上周**: 上周的邮件
- **本月**: 本月的邮件
- **上月**: 上月的邮件

#### 自定义时间范围
- **开始日期**: 设置搜索的起始日期
- **结束日期**: 设置搜索的结束日期
- **日期选择器**: 友好的日期选择界面

### 4. **状态和属性搜索** 🏷️

#### 阅读状态
- **仅未读**: 只显示未读邮件
- **仅已读**: 只显示已读邮件
- **不限制**: 显示所有邮件

#### 邮件大小
- **最小大小**: 设置邮件大小下限（KB）
- **最大大小**: 设置邮件大小上限（KB）
- **大邮件搜索**: 快速找到占用空间大的邮件

### 5. **搜索历史管理** 📚

#### 自动保存
- **搜索记录**: 每次搜索自动保存条件
- **去重处理**: 避免重复的搜索记录
- **数量限制**: 最多保存20条历史记录

#### 历史操作
- **加载搜索**: 重新使用之前的搜索条件
- **删除记录**: 删除不需要的搜索历史
- **清空历史**: 一键清空所有历史记录

## 🎨 搜索技巧

### 1. **关键词搜索技巧**
- **多关键词**: 使用空格分隔多个关键词
- **精确匹配**: 使用引号包围精确短语
- **通配符**: 使用%作为通配符（数据库级别）

### 2. **组合搜索策略**
- **时间+状态**: 搜索特定时间的未读邮件
- **发件人+关键词**: 搜索特定人发送的特定主题邮件
- **大小+时间**: 找到最近的大附件邮件

### 3. **效率提升技巧**
- **保存常用搜索**: 将常用搜索条件保存到历史
- **使用预设搜索**: 利用内置的预设搜索快速查找
- **实时搜索**: 利用实时搜索快速缩小范围

## 🛡️ 性能和安全

### 性能优化
- **数据库索引**: 优化的数据库查询性能
- **结果限制**: 限制搜索结果数量避免内存问题
- **延迟搜索**: 防抖机制减少不必要的查询

### 搜索安全
- **SQL注入防护**: 使用参数化查询防止注入攻击
- **输入验证**: 验证搜索条件的合法性
- **权限控制**: 只搜索当前用户的邮件

## 🔍 故障排除

### 常见问题

1. **搜索无结果**
   - 检查搜索关键词是否正确
   - 尝试使用更宽泛的搜索条件
   - 确认邮件确实存在于当前文件夹

2. **实时搜索不工作**
   - 检查网络连接和数据库状态
   - 重启应用程序
   - 查看状态栏的错误信息

3. **高级搜索对话框无响应**
   - 确保已正确安装PySide6
   - 检查系统资源使用情况
   - 查看应用程序日志

### 性能优化建议

1. **大量邮件搜索**
   - 使用时间范围缩小搜索范围
   - 避免过于宽泛的关键词
   - 定期清理不需要的邮件

2. **搜索速度优化**
   - 使用具体的搜索条件
   - 避免在内容中搜索过短的关键词
   - 定期维护数据库

## 🎉 总结

### 新功能优势
✅ **专业级搜索体验** - 媲美Gmail和Outlook的搜索功能  
✅ **多维度搜索条件** - 支持各种复杂的搜索需求  
✅ **实时搜索反馈** - 即时显示搜索结果  
✅ **智能搜索历史** - 保存和重用搜索条件  
✅ **高性能数据库查询** - 支持大量邮件的快速搜索  

### 使用建议
1. **日常搜索**: 使用工具栏搜索框进行快速搜索
2. **复杂查询**: 使用高级搜索对话框进行精确搜索
3. **重复搜索**: 利用搜索历史提高效率
4. **性能优化**: 合理使用搜索条件，避免过于宽泛的查询

---

**高级搜索功能现已完全可用！** 🎉

享受专业级的邮件搜索体验，快速找到您需要的任何邮件！

**下一步建议**: 可以继续实现附件管理、邮件发送、标签分类等其他高级功能。
