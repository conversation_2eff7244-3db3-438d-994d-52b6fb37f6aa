#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生产优化版 v2.0 IMAP 客户端
基于最新测试结果优化：socket_direct 为主，优化令牌获取和性能
"""

import imaplib
import socket
import ssl
import time
import base64
import requests
import threading
import logging
import email
import email.utils
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from typing import Optional, List, Dict, Tuple, Union, Any
from enum import Enum
from dataclasses import dataclass, field
from concurrent.futures import ThreadPoolExecutor, TimeoutError, as_completed
import json
import hashlib
import os
from datetime import datetime, timezone

class AuthMethod(Enum):
    """认证方法枚举（基于最新测试结果重新排序）"""
    SOCKET_DIRECT = "socket_direct"        # 主力：100%成功率, 1.47s
    IMAPLIB_MANUAL = "imaplib_manual"      # 备选：当前不稳定
    IMAPLIB_STANDARD = "imaplib_standard"  # 保留：仅用于兼容性

@dataclass
class TokenCache:
    """令牌缓存"""
    access_token: str
    expires_at: float
    refresh_count: int = 0
    last_refresh_time: float = 0
    avg_refresh_time: float = 0

@dataclass
class EmailHeader:
    """邮件头信息"""
    message_id: str
    subject: str
    sender: str
    recipients: List[str]
    date: datetime
    uid: int
    size: int
    flags: List[str] = field(default_factory=list)

@dataclass
class EmailContent:
    """邮件内容"""
    header: EmailHeader
    text_body: Optional[str] = None
    html_body: Optional[str] = None
    attachments: List[Dict[str, Any]] = field(default_factory=list)
    raw_content: Optional[bytes] = None
    content_hash: Optional[str] = None

@dataclass
class FetchResult:
    """获取结果"""
    success: bool
    emails: List[EmailContent] = field(default_factory=list)
    error_message: Optional[str] = None
    fetch_time: float = 0
    total_size: int = 0

class ProductionOptimizedClientV2:
    """生产优化版 v2.0 IMAP 客户端"""
    
    def __init__(self, email: str = None, client_id: str = None, refresh_token: str = None, enable_fast_mode: bool = True):
        # 配置信息 - 支持动态配置或使用默认值
        self.email = email or "<EMAIL>"
        self.client_id = client_id or "9e5f94bc-e8a4-4e73-b8be-63364c29d753"
        self.refresh_token = refresh_token or "M.C553_BL2.0.U.-Cu95AlCo5F2ecNfP6eXPgKg2vMKcS*SulS6ospsomaeXcL8hnU1KodCx7YO833tuKNxW1v2HuliUqfU!*HE3A6LESe2MLb5nNeKZNyN73uC9e0dZH6Z1UhBuf0lQLd1!38GP2XfvohTnYDTe56OlYf*Oizlw58XX*LlTZ*QFUGVET7oDq9JBGR8ajeIQDHbLIVsv2ow7SXaWTEtYG0k2Q*t0rn7cDUN8jtW4eHpnWJd*0EkyF1ms7kZHFKGBIIDA!3Fq6X3WNOnfa7b22J6H03bkynsOowREmiChJAWIzSKu3qNrBPuHwY885OF8IAIKx4tTCUaANbdkyBIQ1zHt*VgHQ*Bg*1ZXH28sW6u!KFRGN!h1LUuiHywFfsGO!5mcARYOfF43ITUinVsCvP6NxjNy!UzPYlDNGMfkZQvAl7yC"
        
        # 服务器配置
        self.server = "outlook.office365.com"
        self.port = 993
        
        # 性能优化配置
        self.enable_fast_mode = enable_fast_mode
        self.connection_timeout = 6 if enable_fast_mode else 10
        self.token_timeout = 8 if enable_fast_mode else 15
        
        # 令牌缓存
        self.token_cache: Optional[TokenCache] = None
        
        # 连接对象
        self.sock = None
        self.ssl_sock = None
        self.imap = None
        
        # 状态管理
        self.current_method = None
        self.authenticated = False
        
        # 性能统计
        self.performance_stats = {
            'token_refresh_times': [],
            'auth_times': {},
            'method_success_count': {method: 0 for method in AuthMethod},
            'method_failure_count': {method: 0 for method in AuthMethod}
        }
        
        # 线程安全
        self.lock = threading.RLock()
        
        # 配置日志
        self.logger = logging.getLogger(__name__)

        # 记录初始化信息
        self.logger.info(f"初始化邮件客户端: {self.email}")

    def set_account_config(self, email: str, client_id: str, refresh_token: str):
        """设置账户配置"""
        self.email = email
        self.client_id = client_id
        self.refresh_token = refresh_token
        self.logger.info(f"更新账户配置: {email}")

        # 清除现有的认证状态和令牌缓存
        self.authenticated = False
        self.token_cache = None

        # 如果已连接，需要重新连接
        if self.imap:
            self.disconnect()

        # 基于最新测试结果的方法优先级
        self.method_priority = [
            AuthMethod.SOCKET_DIRECT,    # 主力方法：1.47s, 100%成功
            AuthMethod.IMAPLIB_MANUAL,   # 备选方法：当前不稳定
            AuthMethod.IMAPLIB_STANDARD  # 兼容性方法：仅测试用
        ]
    
    def get_access_token_optimized(self, force_refresh: bool = False) -> bool:
        """优化的访问令牌获取"""
        current_time = time.time()
        
        # 检查缓存的令牌是否仍然有效
        if not force_refresh and self.token_cache:
            if current_time < self.token_cache.expires_at:
                self.logger.debug("使用缓存的访问令牌")
                return True
        
        self.logger.info("获取新的访问令牌...")
        start_time = time.time()
        
        # 优化的请求配置
        url = "https://login.microsoftonline.com/common/oauth2/v2.0/token"
        data = {
            'client_id': self.client_id,
            'refresh_token': self.refresh_token,
            'grant_type': 'refresh_token',
            'scope': 'https://outlook.office.com/IMAP.AccessAsUser.All'
        }
        
        # 优化的请求头
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'User-Agent': 'EmailManager/2.0',
            'Accept': 'application/json',
            'Connection': 'close'  # 避免连接复用问题
        }
        
        try:
            # 使用优化的超时设置
            response = requests.post(
                url, 
                data=data, 
                headers=headers,
                timeout=self.token_timeout,
                verify=True
            )
            
            elapsed_time = time.time() - start_time
            
            if response.status_code == 200:
                token_data = response.json()
                access_token = token_data.get('access_token')
                expires_in = token_data.get('expires_in', 3600)
                
                # 更新令牌缓存
                self.token_cache = TokenCache(
                    access_token=access_token,
                    expires_at=current_time + expires_in - 300,  # 提前5分钟过期
                    refresh_count=self.token_cache.refresh_count + 1 if self.token_cache else 1,
                    last_refresh_time=elapsed_time
                )
                
                # 更新平均刷新时间
                if self.token_cache.refresh_count > 1:
                    self.token_cache.avg_refresh_time = (
                        self.token_cache.avg_refresh_time * (self.token_cache.refresh_count - 1) + elapsed_time
                    ) / self.token_cache.refresh_count
                else:
                    self.token_cache.avg_refresh_time = elapsed_time
                
                # 记录性能统计
                self.performance_stats['token_refresh_times'].append(elapsed_time)
                
                self.logger.info(f"访问令牌获取成功 ({elapsed_time:.2f}s, 有效期: {expires_in}s)")
                return True
            else:
                self.logger.error(f"获取访问令牌失败: {response.status_code}")
                self.logger.error(f"错误响应: {response.text}")
                return False
                
        except requests.exceptions.Timeout:
            elapsed_time = time.time() - start_time
            self.logger.error(f"令牌获取超时 ({elapsed_time:.2f}s)")
            return False
        except Exception as e:
            elapsed_time = time.time() - start_time
            self.logger.error(f"获取访问令牌异常 ({elapsed_time:.2f}s): {e}")
            return False
    
    def connect_and_authenticate_fast(self) -> bool:
        """快速连接和认证"""
        with self.lock:
            self.logger.info(f"快速连接到 {self.email}")
            
            # 获取访问令牌
            if not self.get_access_token_optimized():
                return False
            
            # 基于最新测试结果，优先使用 socket_direct
            for method in self.method_priority:
                self.logger.info(f"尝试方法: {method.value}")
                
                start_time = time.time()
                success = False
                
                try:
                    if method == AuthMethod.SOCKET_DIRECT:
                        success = self._authenticate_socket_direct_optimized()
                    elif method == AuthMethod.IMAPLIB_MANUAL:
                        success = self._authenticate_imaplib_manual_safe()
                    elif method == AuthMethod.IMAPLIB_STANDARD:
                        success = self._authenticate_imaplib_standard_safe()
                    
                    elapsed_time = time.time() - start_time
                    
                    # 更新统计
                    if success:
                        self.performance_stats['method_success_count'][method] += 1
                        self.performance_stats['auth_times'][method.value] = elapsed_time
                        self.current_method = method
                        self.authenticated = True
                        self.logger.info(f"认证成功: {method.value} ({elapsed_time:.2f}s)")
                        return True
                    else:
                        self.performance_stats['method_failure_count'][method] += 1
                        self.logger.warning(f"认证失败: {method.value} ({elapsed_time:.2f}s)")
                        self._cleanup()
                        
                except Exception as e:
                    elapsed_time = time.time() - start_time
                    self.performance_stats['method_failure_count'][method] += 1
                    self.logger.error(f"认证异常: {method.value} ({elapsed_time:.2f}s), 错误: {e}")
                    self._cleanup()
            
            self.logger.error("所有认证方法都失败")
            return False
    
    def _authenticate_socket_direct_optimized(self) -> bool:
        """优化的直接 socket 认证（主力方法，增强错误处理）"""
        max_retries = 2
        retry_delay = 1.0

        for attempt in range(max_retries + 1):
            try:
                if attempt > 0:
                    self.logger.info(f"Socket认证重试 {attempt}/{max_retries}")
                    time.sleep(retry_delay)
                    # 清理之前的连接
                    self._cleanup_connections()

                self.logger.debug("使用优化的socket直接认证")

                # 优化的连接设置
                self.sock = socket.create_connection(
                    (self.server, self.port),
                    timeout=self.connection_timeout
                )

                # 设置socket选项优化性能
                self.sock.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)
                self.sock.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)

                # SSL包装
                context = ssl.create_default_context()
                context.check_hostname = True
                context.verify_mode = ssl.CERT_REQUIRED

                self.ssl_sock = context.wrap_socket(
                    self.sock,
                    server_hostname=self.server
                )

                # 设置SSL socket超时
                self.ssl_sock.settimeout(self.connection_timeout)

                # 读取欢迎信息
                welcome = self.ssl_sock.recv(4096)
                self.logger.debug(f"服务器欢迎: {welcome.decode('utf-8', errors='ignore').strip()}")

                # 创建OAuth2字符串
                oauth2_string = self._create_oauth2_string()

                # 发送认证命令
                command = "A001 AUTHENTICATE XOAUTH2\r\n"
                self.ssl_sock.send(command.encode())

                # 读取响应
                response = self.ssl_sock.recv(4096).decode('utf-8', errors='ignore')
                self.logger.debug(f"认证响应: {response.strip()}")

                if '+' in response:
                    # 发送OAuth2数据
                    self.ssl_sock.send(oauth2_string.encode() + b'\r\n')

                    # 读取最终响应
                    final_response = self.ssl_sock.recv(4096).decode('utf-8', errors='ignore')
                    self.logger.debug(f"最终响应: {final_response.strip()}")

                    success = 'OK' in final_response and 'AUTHENTICATE completed' in final_response
                    if success:
                        self.logger.debug("Socket直接认证成功")
                        return success

                return False

            except (ssl.SSLError, OSError, socket.error) as e:
                self.logger.warning(f"Socket认证网络错误 (尝试 {attempt + 1}/{max_retries + 1}): {e}")
                if attempt == max_retries:
                    self.logger.error(f"Socket认证最终失败: {e}")
                    return False
                continue

            except Exception as e:
                self.logger.error(f"Socket认证异常: {e}")
                return False

        return False

    def _cleanup_connections(self):
        """清理现有连接"""
        try:
            if self.ssl_sock:
                self.ssl_sock.close()
                self.ssl_sock = None
            if self.sock:
                self.sock.close()
                self.sock = None
        except:
            pass
    
    def _authenticate_imaplib_manual_safe(self) -> bool:
        """安全的手动 imaplib 认证"""
        try:
            self.logger.debug("尝试安全的手动imaplib认证")
            
            context = ssl.create_default_context()
            self.imap = imaplib.IMAP4_SSL(
                self.server, 
                self.port, 
                ssl_context=context
            )
            
            # 设置超时
            self.imap.sock.settimeout(self.connection_timeout)
            
            oauth2_string = self._create_oauth2_string()
            
            # 手动发送认证命令
            tag = self.imap._new_tag().decode()
            command = f'{tag} AUTHENTICATE XOAUTH2'
            self.imap.send(command.encode() + b'\r\n')
            
            response = self.imap.readline()
            if b'+' in response:
                self.imap.send(oauth2_string.encode() + b'\r\n')
                final_response = self.imap.readline()
                
                if b'OK' in final_response and b'AUTHENTICATE completed' in final_response:
                    self.imap.state = 'AUTH'
                    self.logger.debug("手动imaplib认证成功")
                    return True
            
            return False
            
        except Exception as e:
            self.logger.debug(f"手动imaplib认证异常: {e}")
            return False
    
    def _authenticate_imaplib_standard_safe(self) -> bool:
        """安全的标准 imaplib 认证"""
        try:
            self.logger.debug("尝试标准imaplib认证")
            
            context = ssl.create_default_context()
            self.imap = imaplib.IMAP4_SSL(
                self.server, 
                self.port, 
                ssl_context=context
            )
            
            oauth2_string = self._create_oauth2_string()
            result = self.imap.authenticate('XOAUTH2', lambda x: oauth2_string)
            
            success = result[0] == 'OK'
            if success:
                self.logger.debug("标准imaplib认证成功")
            return success
            
        except Exception as e:
            self.logger.debug(f"标准imaplib认证异常: {e}")
            return False
    
    def _create_oauth2_string(self) -> str:
        """创建 OAuth2 认证字符串"""
        if not self.token_cache:
            raise ValueError("没有有效的访问令牌")
        
        auth_string = f"user={self.email}\x01auth=Bearer {self.token_cache.access_token}\x01\x01"
        return base64.b64encode(auth_string.encode('utf-8')).decode('utf-8')
    
    def list_folders_optimized(self) -> List[str]:
        """优化的文件夹列表获取"""
        if not self.authenticated:
            self.logger.error("未认证，无法获取文件夹")
            return []
        
        try:
            if self.current_method == AuthMethod.SOCKET_DIRECT:
                return self._list_folders_socket_optimized()
            elif self.current_method in [AuthMethod.IMAPLIB_MANUAL, AuthMethod.IMAPLIB_STANDARD]:
                return self._list_folders_imaplib()
        except Exception as e:
            self.logger.error(f"获取文件夹失败: {e}")
        
        return []
    
    def _list_folders_socket_optimized(self) -> List[str]:
        """优化的socket文件夹获取"""
        try:
            command = "A002 LIST \"\" \"*\"\r\n"
            self.ssl_sock.send(command.encode())
            
            folders = []
            response_buffer = ""
            
            # 优化的响应读取
            while True:
                try:
                    self.ssl_sock.settimeout(3.0)  # 短超时
                    data = self.ssl_sock.recv(4096).decode('utf-8', errors='ignore')
                    
                    if not data:
                        break
                    
                    response_buffer += data
                    
                    # 检查命令完成
                    if 'A002 OK' in data or 'A002 NO' in data or 'A002 BAD' in data:
                        break
                        
                except socket.timeout:
                    # 超时但可能已经有数据
                    if folders or 'A002' in response_buffer:
                        break
                    else:
                        raise
            
            # 提取文件夹
            for line in response_buffer.split('\r\n'):
                if line.startswith('* LIST'):
                    folders.append(line)
            
            return folders
            
        except Exception as e:
            self.logger.error(f"Socket文件夹获取失败: {e}")
            return []
    
    def _list_folders_imaplib(self) -> List[str]:
        """imaplib文件夹获取"""
        try:
            result, folders = self.imap.list()
            if result == 'OK' and folders:
                return [folder.decode('utf-8', errors='ignore') for folder in folders]
            return []
        except Exception as e:
            self.logger.error(f"imaplib文件夹获取失败: {e}")
            return []
    
    def get_performance_report_v2(self) -> Dict:
        """获取v2性能报告"""
        report = {
            'current_method': self.current_method.value if self.current_method else None,
            'authenticated': self.authenticated,
            'token_cache_info': {},
            'performance_summary': {},
            'method_reliability': {}
        }
        
        # 令牌缓存信息
        if self.token_cache:
            report['token_cache_info'] = {
                'refresh_count': self.token_cache.refresh_count,
                'avg_refresh_time': f"{self.token_cache.avg_refresh_time:.2f}s",
                'last_refresh_time': f"{self.token_cache.last_refresh_time:.2f}s",
                'expires_in': f"{(self.token_cache.expires_at - time.time()):.0f}s"
            }
        
        # 性能摘要
        if self.performance_stats['token_refresh_times']:
            avg_token_time = sum(self.performance_stats['token_refresh_times']) / len(self.performance_stats['token_refresh_times'])
            report['performance_summary']['avg_token_refresh_time'] = f"{avg_token_time:.2f}s"
        
        # 方法可靠性
        for method in AuthMethod:
            success = self.performance_stats['method_success_count'][method]
            failure = self.performance_stats['method_failure_count'][method]
            total = success + failure
            
            if total > 0:
                success_rate = (success / total) * 100
                report['method_reliability'][method.value] = {
                    'success_rate': f"{success_rate:.1f}%",
                    'success_count': success,
                    'failure_count': failure,
                    'auth_time': self.performance_stats['auth_times'].get(method.value, 'N/A')
                }
        
        return report
    
    def _cleanup(self):
        """清理连接"""
        if self.imap:
            try:
                self.imap.logout()
            except:
                pass
            self.imap = None
        
        if self.ssl_sock:
            try:
                self.ssl_sock.close()
            except:
                pass
            self.ssl_sock = None
        
        if self.sock:
            try:
                self.sock.close()
            except:
                pass
            self.sock = None
        
        self.authenticated = False
    
    def fetch_emails_from_folder(self, folder_name: str = "INBOX",
                                limit: int = 100,
                                fetch_body: bool = True,
                                fetch_attachments: bool = False,
                                sync_mode: str = "auto") -> FetchResult:
        """
        从指定文件夹获取邮件

        Args:
            folder_name: 文件夹名称
            limit: 获取邮件数量限制
            fetch_body: 是否获取邮件正文
            fetch_attachments: 是否获取附件
        """
        if not self.authenticated:
            return FetchResult(success=False, error_message="未认证，无法获取邮件")

        start_time = time.time()

        try:
            # 选择文件夹
            if not self._select_folder(folder_name):
                return FetchResult(success=False, error_message=f"无法选择文件夹: {folder_name}")

            # 获取邮件列表
            message_ids = self._get_message_ids(limit)
            if not message_ids:
                return FetchResult(success=True, emails=[], fetch_time=time.time() - start_time)

            # 批量获取邮件
            emails = []
            total_size = 0

            for msg_id in message_ids:
                try:
                    email_content = self._fetch_single_email(
                        msg_id,
                        fetch_body=fetch_body,
                        fetch_attachments=fetch_attachments
                    )
                    if email_content:
                        emails.append(email_content)
                        total_size += email_content.header.size

                except Exception as e:
                    self.logger.warning(f"获取邮件 {msg_id} 失败: {e}")
                    continue

            fetch_time = time.time() - start_time
            self.logger.info(f"成功获取 {len(emails)} 封邮件，耗时 {fetch_time:.2f}s")

            return FetchResult(
                success=True,
                emails=emails,
                fetch_time=fetch_time,
                total_size=total_size
            )

        except Exception as e:
            fetch_time = time.time() - start_time
            self.logger.error(f"获取邮件失败: {e}")
            return FetchResult(
                success=False,
                error_message=str(e),
                fetch_time=fetch_time
            )

    # ========================================
    # 📈 智能增量同步方法
    # ========================================

    def fetch_emails_incremental(self, folder_name: str = "INBOX",
                                search_criteria: str = "",
                                limit: int = 50,
                                fetch_body: bool = True,
                                fetch_attachments: bool = False) -> FetchResult:
        """增量获取邮件"""
        if not self.authenticated:
            return FetchResult(success=False, error_message="未认证，无法获取邮件")

        try:
            start_time = time.time()
            self.logger.info(f"开始增量同步邮件: {folder_name}, 搜索条件: {search_criteria}")

            # 选择文件夹
            if not self._select_folder(folder_name):
                return FetchResult(success=False, error_message=f"无法选择文件夹: {folder_name}")

            # 使用搜索条件获取邮件ID
            message_ids = self._search_emails_by_criteria(search_criteria, limit)
            if not message_ids:
                self.logger.info(f"增量同步未发现新邮件: {folder_name}")
                return FetchResult(success=True, emails=[], fetch_time=time.time() - start_time)

            # 批量获取邮件
            emails = []
            total_size = 0

            for msg_id in message_ids:
                try:
                    email_content = self._fetch_single_email(
                        msg_id,
                        fetch_body=fetch_body,
                        fetch_attachments=fetch_attachments
                    )
                    if email_content:
                        emails.append(email_content)
                        total_size += email_content.header.size

                        # 记录进度
                        if len(emails) % 10 == 0:
                            self.logger.debug(f"增量同步进度: {len(emails)}/{len(message_ids)}")

                except Exception as e:
                    self.logger.warning(f"获取邮件 {msg_id} 失败: {e}")
                    continue

            fetch_time = time.time() - start_time
            self.logger.info(f"增量同步完成: {len(emails)} 封邮件, 耗时 {fetch_time:.2f}s")

            return FetchResult(
                success=True,
                emails=emails,
                fetch_time=fetch_time,
                total_size=total_size
            )

        except Exception as e:
            self.logger.error(f"增量获取邮件失败: {e}")
            return FetchResult(success=False, error_message=f"增量获取邮件失败: {e}")

    def fetch_emails_first_time(self, folder_name: str = "INBOX",
                               limit: int = 100,
                               fetch_body: bool = True,
                               fetch_attachments: bool = False) -> FetchResult:
        """首次同步（限量获取最新邮件）"""
        if not self.authenticated:
            return FetchResult(success=False, error_message="未认证，无法获取邮件")

        try:
            start_time = time.time()
            self.logger.info(f"开始首次同步邮件: {folder_name}, 限制: {limit} 封")

            # 选择文件夹
            if not self._select_folder(folder_name):
                return FetchResult(success=False, error_message=f"无法选择文件夹: {folder_name}")

            # 获取最新的邮件ID（限量）
            message_ids = self._get_message_ids(limit)
            if not message_ids:
                return FetchResult(success=True, emails=[], fetch_time=time.time() - start_time)

            # 批量获取邮件
            emails = []
            total_size = 0

            for i, msg_id in enumerate(message_ids):
                try:
                    email_content = self._fetch_single_email(
                        msg_id,
                        fetch_body=fetch_body,
                        fetch_attachments=fetch_attachments
                    )
                    if email_content:
                        emails.append(email_content)
                        total_size += email_content.header.size

                        # 记录进度
                        if (i + 1) % 10 == 0:
                            self.logger.info(f"首次同步进度: {i + 1}/{len(message_ids)}")

                except Exception as e:
                    self.logger.warning(f"获取邮件 {msg_id} 失败: {e}")
                    continue

            fetch_time = time.time() - start_time
            self.logger.info(f"首次同步完成: {len(emails)} 封邮件, 耗时 {fetch_time:.2f}s")

            return FetchResult(
                success=True,
                emails=emails,
                fetch_time=fetch_time,
                total_size=total_size
            )

        except Exception as e:
            self.logger.error(f"首次获取邮件失败: {e}")
            return FetchResult(success=False, error_message=f"首次获取邮件失败: {e}")

    def _select_folder(self, folder_name: str) -> bool:
        """选择文件夹"""
        try:
            if self.current_method == AuthMethod.SOCKET_DIRECT:
                return self._select_folder_socket(folder_name)
            elif self.current_method in [AuthMethod.IMAPLIB_MANUAL, AuthMethod.IMAPLIB_STANDARD]:
                return self._select_folder_imaplib(folder_name)
        except Exception as e:
            self.logger.error(f"选择文件夹失败: {e}")
        return False

    def _select_folder_socket(self, folder_name: str) -> bool:
        """使用socket方式选择文件夹（增强错误处理）"""
        try:
            command = f"A003 SELECT {folder_name}\r\n"
            self.ssl_sock.send(command.encode())

            # 使用增强的响应读取
            response = self._read_socket_response("A003", max_size=1024*1024)  # 1MB限制
            if not response:
                self.logger.error(f"选择文件夹 {folder_name} 无响应")
                return False

            success = 'OK' in response and 'SELECT completed' in response
            if not success:
                self.logger.warning(f"选择文件夹 {folder_name} 失败，响应: {response[:200]}...")

            return success

        except (ssl.SSLError, OSError) as e:
            self.logger.error(f"Socket选择文件夹失败 (SSL/网络错误): {e}")
            return False
        except Exception as e:
            self.logger.error(f"Socket选择文件夹失败: {e}")
            return False

    def _select_folder_imaplib(self, folder_name: str) -> bool:
        """使用imaplib选择文件夹"""
        try:
            result, data = self.imap.select(folder_name)
            return result == 'OK'
        except Exception as e:
            self.logger.error(f"imaplib选择文件夹失败: {e}")
            return False

    def _get_message_ids(self, limit: int) -> List[int]:
        """获取邮件ID列表"""
        try:
            if self.current_method == AuthMethod.SOCKET_DIRECT:
                return self._get_message_ids_socket(limit)
            elif self.current_method in [AuthMethod.IMAPLIB_MANUAL, AuthMethod.IMAPLIB_STANDARD]:
                return self._get_message_ids_imaplib(limit)
        except Exception as e:
            self.logger.error(f"获取邮件ID失败: {e}")
        return []

    def _get_message_ids_socket(self, limit: int) -> List[int]:
        """使用socket方式获取邮件ID"""
        try:
            # 搜索所有邮件
            command = "A004 SEARCH ALL\r\n"
            self.ssl_sock.send(command.encode())

            response_buffer = ""
            while True:
                data = self.ssl_sock.recv(4096).decode('utf-8', errors='ignore')
                if not data:
                    break
                response_buffer += data
                if 'A004 OK' in data or 'A004 NO' in data:
                    break

            # 解析邮件ID
            message_ids = []
            for line in response_buffer.split('\r\n'):
                if line.startswith('* SEARCH'):
                    ids_str = line.replace('* SEARCH', '').strip()
                    if ids_str:
                        message_ids = [int(id_str) for id_str in ids_str.split() if id_str.isdigit()]
                    break

            # 返回最新的邮件（倒序取limit个）
            return sorted(message_ids)[-limit:] if message_ids else []

        except Exception as e:
            self.logger.error(f"Socket获取邮件ID失败: {e}")
            return []

    def _get_message_ids_imaplib(self, limit: int) -> List[int]:
        """使用imaplib获取邮件ID"""
        try:
            result, data = self.imap.search(None, 'ALL')
            if result == 'OK' and data[0]:
                message_ids = data[0].decode().split()
                message_ids = [int(msg_id) for msg_id in message_ids]
                return sorted(message_ids)[-limit:]
            return []
        except Exception as e:
            self.logger.error(f"imaplib获取邮件ID失败: {e}")
            return []

    def _search_emails_by_criteria(self, criteria: str, limit: int = 50) -> List[int]:
        """根据条件搜索邮件"""
        try:
            if self.current_method == AuthMethod.SOCKET_DIRECT:
                return self._search_emails_socket(criteria, limit)
            elif self.current_method in [AuthMethod.IMAPLIB_MANUAL, AuthMethod.IMAPLIB_STANDARD]:
                return self._search_emails_imaplib(criteria, limit)
        except Exception as e:
            self.logger.error(f"搜索邮件失败: {e}")
        return []

    def _search_emails_socket(self, criteria: str, limit: int) -> List[int]:
        """使用socket方式搜索邮件"""
        try:
            # 构建搜索命令
            if not criteria.strip():
                criteria = "ALL"

            command = f"A005 SEARCH {criteria}\r\n"
            self.ssl_sock.send(command.encode())

            response_buffer = ""
            while True:
                data = self.ssl_sock.recv(4096).decode('utf-8', errors='ignore')
                if not data:
                    break
                response_buffer += data
                if 'A005 OK' in data or 'A005 NO' in data:
                    break

            # 解析邮件ID
            message_ids = []
            for line in response_buffer.split('\r\n'):
                if line.startswith('* SEARCH'):
                    # 提取邮件ID
                    parts = line.split()[2:]  # 跳过 "* SEARCH"
                    for part in parts:
                        try:
                            message_ids.append(int(part))
                        except ValueError:
                            continue

            # 返回最新的邮件（限制数量）
            return sorted(message_ids)[-limit:] if message_ids else []

        except Exception as e:
            self.logger.error(f"Socket搜索邮件失败: {e}")
            return []

    def _search_emails_imaplib(self, criteria: str, limit: int) -> List[int]:
        """使用imaplib搜索邮件"""
        try:
            if not criteria.strip():
                criteria = "ALL"

            result, data = self.imap.search(None, criteria)
            if result == 'OK' and data[0]:
                message_ids = data[0].decode().split()
                message_ids = [int(msg_id) for msg_id in message_ids]
                # 返回最新的邮件（限制数量）
                return sorted(message_ids)[-limit:]
            return []
        except Exception as e:
            self.logger.error(f"imaplib搜索邮件失败: {e}")
            return []

    def _fetch_single_email(self, message_id: int, fetch_body: bool = True,
                           fetch_attachments: bool = False) -> Optional[EmailContent]:
        """获取单个邮件内容"""
        try:
            if self.current_method == AuthMethod.SOCKET_DIRECT:
                return self._fetch_email_socket(message_id, fetch_body, fetch_attachments)
            elif self.current_method in [AuthMethod.IMAPLIB_MANUAL, AuthMethod.IMAPLIB_STANDARD]:
                return self._fetch_email_imaplib(message_id, fetch_body, fetch_attachments)
        except Exception as e:
            self.logger.error(f"获取邮件 {message_id} 失败: {e}")
        return None

    def _fetch_email_socket(self, message_id: int, fetch_body: bool,
                           fetch_attachments: bool) -> Optional[EmailContent]:
        """使用socket直接获取邮件"""
        try:
            self.logger.debug(f"使用socket获取邮件 {message_id}")

            # 首先获取邮件头信息
            header_command = f"A005 FETCH {message_id} (UID FLAGS RFC822.SIZE ENVELOPE INTERNALDATE)\r\n"
            self.ssl_sock.send(header_command.encode())

            # 读取头信息响应
            header_response = self._read_socket_response("A005")
            if not header_response:
                self.logger.error(f"获取邮件 {message_id} 头信息失败")
                return None

            # 解析头信息
            header_info = self._parse_socket_header_response(header_response, message_id)
            if not header_info:
                self.logger.error(f"解析邮件 {message_id} 头信息失败")
                return None

            email_content = EmailContent(header=header_info)

            # 如果需要获取邮件正文
            if fetch_body:
                body_command = f"A006 FETCH {message_id} (RFC822)\r\n"
                self.ssl_sock.send(body_command.encode())

                # 读取正文响应
                body_response = self._read_socket_response("A006")
                if body_response:
                    # 解析邮件正文
                    raw_content = self._extract_rfc822_content(body_response)
                    if raw_content:
                        email_content.raw_content = raw_content
                        email_content.content_hash = hashlib.md5(raw_content).hexdigest()

                        # 解析邮件内容
                        self._parse_email_content(email_content, raw_content, fetch_attachments)

            return email_content

        except Exception as e:
            self.logger.error(f"Socket获取邮件 {message_id} 失败: {e}")
            return None

    def _read_socket_response(self, tag: str, max_size: int = 10 * 1024 * 1024) -> str:
        """读取socket响应直到指定标签完成"""
        try:
            response_buffer = ""
            self.ssl_sock.settimeout(30.0)  # 增加超时时间
            chunk_size = 8192  # 增加块大小

            while True:
                try:
                    data = self.ssl_sock.recv(chunk_size).decode('utf-8', errors='ignore')
                    if not data:
                        break

                    response_buffer += data

                    # 检查命令是否完成
                    if self._is_command_complete(response_buffer, tag):
                        self.logger.debug(f"命令 {tag} 完成，响应长度: {len(response_buffer)}")
                        return response_buffer

                    # 防止内存溢出，但允许更大的邮件
                    if len(response_buffer) > max_size:
                        self.logger.warning(f"响应数据过大 ({len(response_buffer)} 字节)，停止读取")
                        break

                except socket.timeout:
                    self.logger.warning(f"读取响应超时，当前缓冲区大小: {len(response_buffer)}")
                    # 检查是否有部分有效数据
                    if response_buffer and self._is_command_complete(response_buffer, tag):
                        self.logger.info(f"超时但命令 {tag} 已完成")
                        return response_buffer
                    break

                except (ssl.SSLError, OSError) as e:
                    self.logger.warning(f"SSL/网络错误: {e}")
                    # 检查是否有部分有效数据
                    if response_buffer and self._is_command_complete(response_buffer, tag):
                        self.logger.info(f"SSL错误但命令 {tag} 已完成")
                        return response_buffer
                    break

                except Exception as e:
                    self.logger.error(f"读取数据块失败: {e}")
                    break

            return response_buffer

        except Exception as e:
            self.logger.error(f"读取socket响应失败: {e}")
            return ""

    def _is_command_complete(self, response: str, tag: str) -> bool:
        """检查IMAP命令是否完成"""
        try:
            lines = response.split('\r\n')
            for line in lines:
                line = line.strip()
                if (line.startswith(f'{tag} OK') or
                    line.startswith(f'{tag} NO') or
                    line.startswith(f'{tag} BAD')):
                    return True
            return False
        except:
            return False

    def _parse_socket_header_response(self, response: str, message_id: int) -> Optional[EmailHeader]:
        """解析socket头信息响应"""
        try:
            # 简化的头信息解析
            # 在实际的IMAP响应中，我们需要解析ENVELOPE结构

            # 提取基本信息（简化版本，实际需要更复杂的解析）
            uid = message_id  # 简化处理
            size = 0
            flags = []

            # 尝试从响应中提取大小信息
            import re
            size_match = re.search(r'RFC822\.SIZE (\d+)', response)
            if size_match:
                size = int(size_match.group(1))

            # 尝试提取UID
            uid_match = re.search(r'UID (\d+)', response)
            if uid_match:
                uid = int(uid_match.group(1))

            # 尝试提取FLAGS
            flags_match = re.search(r'FLAGS \(([^)]*)\)', response)
            if flags_match:
                flags = flags_match.group(1).split()

            return EmailHeader(
                message_id=f"msg_{message_id}_{uid}",
                subject="[需要解析ENVELOPE]",
                sender="<EMAIL>",
                recipients=["<EMAIL>"],
                date=datetime.now(timezone.utc),
                uid=uid,
                size=size,
                flags=flags
            )

        except Exception as e:
            self.logger.error(f"解析socket头信息失败: {e}")
            return None

    def _extract_rfc822_content(self, response: str) -> Optional[bytes]:
        """从socket响应中提取RFC822邮件内容"""
        try:
            self.logger.debug(f"开始提取RFC822内容，响应长度: {len(response)}")

            # 多种模式查找RFC822内容
            patterns = [
                ('RFC822 {', '}'),
                ('BODY[] {', '}'),
                ('RFC822.TEXT {', '}'),
                ('BODY[TEXT] {', '}')
            ]

            for start_pattern, end_pattern in patterns:
                content = self._extract_with_pattern(response, start_pattern, end_pattern)
                if content:
                    self.logger.debug(f"使用模式 {start_pattern} 成功提取内容")
                    return content

            # 尝试基于行的解析
            content = self._extract_by_lines(response)
            if content:
                self.logger.debug("使用行解析成功提取内容")
                return content

            # 最后尝试简单的内容提取
            content = self._extract_simple_content(response)
            if content:
                self.logger.debug("使用简单解析成功提取内容")
                return content

            self.logger.warning("无法从响应中提取RFC822内容")
            return None

        except Exception as e:
            self.logger.error(f"提取RFC822内容失败: {e}")
            return None

    def _extract_with_pattern(self, response: str, start_pattern: str, end_pattern: str) -> Optional[bytes]:
        """使用指定模式提取内容"""
        try:
            start_pos = response.find(start_pattern)
            if start_pos == -1:
                return None

            # 查找大小信息
            size_start = start_pos + len(start_pattern)
            size_end = response.find(end_pattern, size_start)
            if size_end == -1:
                return None

            try:
                content_size = int(response[size_start:size_end])
                self.logger.debug(f"内容大小: {content_size} 字节")
            except ValueError:
                return None

            # 查找内容开始位置
            content_start = response.find('\r\n', size_end) + 2
            if content_start == 1:
                content_start = response.find('\n', size_end) + 1
                if content_start == 0:
                    return None

            # 提取内容
            content_end = min(content_start + content_size, len(response))
            content = response[content_start:content_end]

            if len(content) >= content_size * 0.8:  # 允许一些容差
                return content.encode('utf-8')

            return None

        except Exception as e:
            self.logger.debug(f"模式提取失败: {e}")
            return None

    def _extract_by_lines(self, response: str) -> Optional[bytes]:
        """基于行的内容提取"""
        try:
            lines = response.split('\r\n')
            rfc822_start = -1
            rfc822_end = -1

            for i, line in enumerate(lines):
                if ('RFC822' in line or 'BODY[]' in line) and '{' in line:
                    rfc822_start = i + 1
                elif rfc822_start != -1 and (line.strip() == ')' or line.startswith('A0')):
                    rfc822_end = i
                    break

            if rfc822_start != -1 and rfc822_end != -1:
                email_lines = lines[rfc822_start:rfc822_end]
                email_content = '\r\n'.join(email_lines)
                if email_content.strip():
                    return email_content.encode('utf-8')

            return None

        except Exception as e:
            self.logger.debug(f"行解析失败: {e}")
            return None

    def _extract_simple_content(self, response: str) -> Optional[bytes]:
        """简单内容提取"""
        try:
            # 查找邮件头部开始
            content_start = response.find('\r\n\r\n')
            if content_start == -1:
                content_start = response.find('\n\n')
                if content_start == -1:
                    return None
                content_start += 2
            else:
                content_start += 4

            # 查找内容结束
            content_part = response[content_start:]
            content_end = content_part.find('\r\n)')
            if content_end == -1:
                content_end = content_part.find('\n)')
                if content_end == -1:
                    content_end = len(content_part)

            content = content_part[:content_end]
            if content.strip():
                return content.encode('utf-8')

            return None

        except Exception as e:
            self.logger.debug(f"简单解析失败: {e}")
            return None

    def _fetch_email_imaplib(self, message_id: int, fetch_body: bool,
                            fetch_attachments: bool) -> Optional[EmailContent]:
        """使用imaplib获取邮件"""
        try:
            # 获取邮件头信息
            result, data = self.imap.fetch(str(message_id), '(UID FLAGS RFC822.SIZE ENVELOPE)')
            if result != 'OK' or not data[0]:
                return None

            # 解析头信息
            header_info = self._parse_email_header_imaplib(data[0], message_id)
            if not header_info:
                return None

            email_content = EmailContent(header=header_info)

            # 获取邮件正文
            if fetch_body:
                result, body_data = self.imap.fetch(str(message_id), '(RFC822)')
                if result == 'OK' and body_data[0]:
                    raw_content = body_data[0][1]
                    email_content.raw_content = raw_content
                    email_content.content_hash = hashlib.md5(raw_content).hexdigest()

                    # 解析邮件内容
                    self._parse_email_content(email_content, raw_content, fetch_attachments)

            return email_content

        except Exception as e:
            self.logger.error(f"imaplib获取邮件 {message_id} 失败: {e}")
            return None

    def _parse_email_header_imaplib(self, header_data: bytes, message_id: int) -> Optional[EmailHeader]:
        """解析imaplib邮件头信息"""
        try:
            # 这里需要解析IMAP响应格式
            # 简化实现，实际需要更复杂的解析逻辑
            header_str = header_data.decode('utf-8', errors='ignore')

            # 提取基本信息（简化版本）
            return EmailHeader(
                message_id=f"msg_{message_id}",
                subject="Subject parsing needed",
                sender="<EMAIL>",
                recipients=["<EMAIL>"],
                date=datetime.now(timezone.utc),
                uid=message_id,
                size=len(header_data),
                flags=[]
            )

        except Exception as e:
            self.logger.error(f"解析邮件头失败: {e}")
            return None

    def _parse_email_content(self, email_content: EmailContent, raw_content: bytes,
                           fetch_attachments: bool):
        """解析邮件内容"""
        try:
            # 使用email库解析
            msg = email.message_from_bytes(raw_content)

            # 更新头信息
            email_content.header.subject = self._decode_header(msg.get('Subject', ''))
            email_content.header.sender = self._decode_header(msg.get('From', ''))

            # 解析收件人
            to_header = msg.get('To', '')
            cc_header = msg.get('Cc', '')
            recipients = []
            if to_header:
                recipients.extend([addr.strip() for addr in to_header.split(',')])
            if cc_header:
                recipients.extend([addr.strip() for addr in cc_header.split(',')])
            email_content.header.recipients = recipients

            # 解析日期
            date_header = msg.get('Date')
            if date_header:
                try:
                    email_content.header.date = email.utils.parsedate_to_datetime(date_header)
                except:
                    email_content.header.date = datetime.now(timezone.utc)

            # 解析正文和附件
            self._extract_body_and_attachments(email_content, msg, fetch_attachments)

        except Exception as e:
            self.logger.error(f"解析邮件内容失败: {e}")

    def _decode_header(self, header_value: str) -> str:
        """解码邮件头"""
        try:
            if not header_value:
                return ""

            decoded_parts = email.header.decode_header(header_value)
            result = ""

            for part, charset in decoded_parts:
                if isinstance(part, bytes):
                    if charset:
                        try:
                            result += part.decode(charset)
                        except:
                            result += part.decode('utf-8', errors='replace')
                    else:
                        result += part.decode('utf-8', errors='replace')
                else:
                    result += str(part)

            return result.strip()

        except Exception as e:
            self.logger.error(f"解码邮件头失败: {e}")
            return header_value

    def _extract_body_and_attachments(self, email_content: EmailContent, msg: email.message.Message,
                                    fetch_attachments: bool):
        """提取邮件正文和附件"""
        try:
            if msg.is_multipart():
                for part in msg.walk():
                    content_type = part.get_content_type()
                    content_disposition = part.get('Content-Disposition', '')

                    if 'attachment' in content_disposition and fetch_attachments:
                        # 处理附件
                        self._process_attachment(email_content, part)
                    elif content_type == 'text/plain' and not email_content.text_body:
                        # 处理纯文本正文
                        email_content.text_body = self._decode_body_content(part)
                    elif content_type == 'text/html' and not email_content.html_body:
                        # 处理HTML正文
                        email_content.html_body = self._decode_body_content(part)
            else:
                # 单部分邮件
                content_type = msg.get_content_type()
                if content_type == 'text/plain':
                    email_content.text_body = self._decode_body_content(msg)
                elif content_type == 'text/html':
                    email_content.html_body = self._decode_body_content(msg)

        except Exception as e:
            self.logger.error(f"提取邮件正文和附件失败: {e}")

    def _decode_body_content(self, part: email.message.Message) -> str:
        """解码邮件正文内容"""
        try:
            payload = part.get_payload(decode=True)
            if payload:
                charset = part.get_content_charset() or 'utf-8'
                return payload.decode(charset, errors='replace')
            return ""
        except Exception as e:
            self.logger.error(f"解码正文内容失败: {e}")
            return ""

    def _process_attachment(self, email_content: EmailContent, part: email.message.Message):
        """处理邮件附件"""
        try:
            filename = part.get_filename()
            if filename:
                filename = self._decode_header(filename)

                attachment_info = {
                    'filename': filename,
                    'content_type': part.get_content_type(),
                    'size': len(part.get_payload(decode=True) or b''),
                    'content_id': part.get('Content-ID', ''),
                }

                email_content.attachments.append(attachment_info)

        except Exception as e:
            self.logger.error(f"处理附件失败: {e}")

    def close(self):
        """关闭连接"""
        with self.lock:
            self._cleanup()

def main():
    """主函数"""
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    print("🚀 生产优化版 v2.0 IMAP 客户端测试")
    print("=" * 50)
    
    # 启用快速模式
    client = ProductionOptimizedClientV2(enable_fast_mode=True)
    
    try:
        # 快速连接和认证
        if client.connect_and_authenticate_fast():
            print("\n📁 获取文件夹列表...")
            folders = client.list_folders_optimized()
            
            print(f"✅ 获取到 {len(folders)} 个文件夹:")
            for i, folder in enumerate(folders, 1):
                print(f"   {i}. {folder}")
            
            # v2性能报告
            print(f"\n📊 v2.0 性能报告:")
            report = client.get_performance_report_v2()
            
            print(f"   当前方法: {report['current_method']}")
            print(f"   认证状态: {report['authenticated']}")
            
            if report['token_cache_info']:
                print(f"   令牌信息:")
                for key, value in report['token_cache_info'].items():
                    print(f"     {key}: {value}")
            
            if report['method_reliability']:
                print(f"   方法可靠性:")
                for method, stats in report['method_reliability'].items():
                    print(f"     {method}: {stats['success_rate']} 成功率")
            
        else:
            print("❌ 连接和认证失败")
    
    finally:
        client.close()

if __name__ == "__main__":
    main()
