#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
许可证服务器主应用程序
"""

import os
import json
import logging
from datetime import datetime, timedelta
from flask import Flask, request, jsonify, render_template
from flask_cors import CORS
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from flask_migrate import Migrate

from config import config
from models import db, License, LicenseActivation, ValidationLog, Admin
from utils import generate_machine_fingerprint, validate_license_key_format


def create_app(config_name=None):
    """创建Flask应用程序"""
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')
    
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # 初始化扩展
    db.init_app(app)
    CORS(app)
    
    # 初始化数据库迁移
    migrate = Migrate(app, db)
    
    # 初始化速率限制
    limiter = Limiter(
        key_func=get_remote_address,
        default_limits=[app.config['RATELIMIT_DEFAULT']]
    )
    limiter.init_app(app)
    
    # 配置日志
    if not app.debug:
        logging.basicConfig(
            level=getattr(logging, app.config['LOG_LEVEL']),
            format='%(asctime)s %(levelname)s: %(message)s',
            handlers=[
                logging.FileHandler(app.config['LOG_FILE']),
                logging.StreamHandler()
            ]
        )
    
    # 注册蓝图
    from api import api_bp
    from admin import admin_bp
    
    app.register_blueprint(api_bp, url_prefix='/api/v1')
    app.register_blueprint(admin_bp, url_prefix='/admin')
    
    # 主页路由
    @app.route('/')
    def index():
        """主页"""
        return jsonify({
            'message': '企业邮件管理系统许可证服务器',
            'version': '1.0.0',
            'api_version': 'v1',
            'status': 'running',
            'timestamp': datetime.utcnow().isoformat()
        })
    
    # 健康检查
    @app.route('/health')
    def health_check():
        """健康检查"""
        try:
            # 检查数据库连接
            db.session.execute('SELECT 1')
            db_status = 'ok'
        except Exception as e:
            db_status = f'error: {str(e)}'
        
        return jsonify({
            'status': 'healthy' if db_status == 'ok' else 'unhealthy',
            'database': db_status,
            'timestamp': datetime.utcnow().isoformat()
        })
    
    # 错误处理
    @app.errorhandler(404)
    def not_found(error):
        return jsonify({'error': 'Not found'}), 404
    
    @app.errorhandler(429)
    def ratelimit_handler(e):
        return jsonify({'error': 'Rate limit exceeded', 'message': str(e)}), 429
    
    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        return jsonify({'error': 'Internal server error'}), 500
    
    # 创建数据库表
    with app.app_context():
        db.create_all()
        
        # 创建默认管理员用户
        admin = Admin.query.filter_by(username=app.config['ADMIN_USERNAME']).first()
        if not admin:
            admin = Admin(
                username=app.config['ADMIN_USERNAME'],
                email='<EMAIL>'
            )
            admin.set_password(app.config['ADMIN_PASSWORD'])
            db.session.add(admin)
            db.session.commit()
            app.logger.info(f"创建默认管理员用户: {admin.username}")
    
    return app


if __name__ == '__main__':
    app = create_app()
    app.run(
        host=app.config['HOST'],
        port=app.config['PORT'],
        debug=app.config['DEBUG']
    )
