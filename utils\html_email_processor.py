#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTML邮件处理器
专门处理HTML格式邮件的获取、解析和显示
"""

import re
import html
import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import base64

@dataclass
class HTMLEmailContent:
    """HTML邮件内容"""
    raw_html: str = ""
    cleaned_html: str = ""
    text_content: str = ""
    images: List[Dict] = None
    links: List[Dict] = None
    styles: str = ""
    
    def __post_init__(self):
        if self.images is None:
            self.images = []
        if self.links is None:
            self.links = []

class HTMLEmailProcessor:
    """HTML邮件处理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # HTML清理规则
        self.dangerous_tags = [
            'script', 'object', 'embed', 'applet', 'form', 'input', 
            'button', 'textarea', 'select', 'option', 'iframe'
        ]
        
        self.dangerous_attributes = [
            'onclick', 'onload', 'onerror', 'onmouseover', 'onmouseout',
            'onfocus', 'onblur', 'onchange', 'onsubmit', 'javascript:'
        ]
    
    def process_html_email(self, html_content: str, attachments: List[Dict] = None) -> HTMLEmailContent:
        """处理HTML邮件内容"""
        try:
            if not html_content:
                return HTMLEmailContent()
            
            # 创建结果对象
            result = HTMLEmailContent(raw_html=html_content)
            
            # 清理HTML内容
            result.cleaned_html = self._clean_html_content(html_content)
            
            # 提取文本内容
            result.text_content = self._extract_text_from_html(result.cleaned_html)
            
            # 提取图片信息
            result.images = self._extract_images(result.cleaned_html)
            
            # 提取链接信息
            result.links = self._extract_links(result.cleaned_html)
            
            # 提取样式
            result.styles = self._extract_styles(result.cleaned_html)
            
            # 处理内嵌图片
            if attachments:
                result.cleaned_html = self._process_inline_images(result.cleaned_html, attachments)
            
            return result
            
        except Exception as e:
            self.logger.error(f"处理HTML邮件失败: {e}")
            return HTMLEmailContent(raw_html=html_content)
    
    def _clean_html_content(self, html_content: str) -> str:
        """清理HTML内容，移除危险元素"""
        try:
            # 移除危险标签
            for tag in self.dangerous_tags:
                pattern = rf'<{tag}[^>]*>.*?</{tag}>'
                html_content = re.sub(pattern, '', html_content, flags=re.IGNORECASE | re.DOTALL)
                
                # 移除自闭合标签
                pattern = rf'<{tag}[^>]*/?>'
                html_content = re.sub(pattern, '', html_content, flags=re.IGNORECASE)
            
            # 移除危险属性
            for attr in self.dangerous_attributes:
                pattern = rf'{attr}=["\'][^"\']*["\']'
                html_content = re.sub(pattern, '', html_content, flags=re.IGNORECASE)
            
            # 清理多余的空白
            html_content = re.sub(r'\n\s*\n', '\n', html_content)
            html_content = re.sub(r'>\s+<', '><', html_content)
            
            return html_content.strip()
            
        except Exception as e:
            self.logger.error(f"清理HTML内容失败: {e}")
            return html_content
    
    def _extract_text_from_html(self, html_content: str) -> str:
        """从HTML中提取纯文本"""
        try:
            # 移除样式和脚本标签
            text = re.sub(r'<style[^>]*>.*?</style>', '', html_content, flags=re.IGNORECASE | re.DOTALL)
            text = re.sub(r'<script[^>]*>.*?</script>', '', text, flags=re.IGNORECASE | re.DOTALL)
            
            # 将某些标签转换为换行
            text = re.sub(r'<(br|p|div|h[1-6])[^>]*>', '\n', text, flags=re.IGNORECASE)
            text = re.sub(r'</(p|div|h[1-6])>', '\n', text, flags=re.IGNORECASE)
            
            # 移除所有HTML标签
            text = re.sub(r'<[^>]+>', '', text)
            
            # 解码HTML实体
            text = html.unescape(text)
            
            # 清理空白
            text = re.sub(r'\n\s*\n', '\n\n', text)
            text = re.sub(r' +', ' ', text)
            
            return text.strip()
            
        except Exception as e:
            self.logger.error(f"提取文本失败: {e}")
            return ""
    
    def _extract_images(self, html_content: str) -> List[Dict]:
        """提取图片信息"""
        try:
            images = []
            
            # 查找所有img标签
            img_pattern = r'<img[^>]*>'
            img_matches = re.finditer(img_pattern, html_content, re.IGNORECASE)
            
            for match in img_matches:
                img_tag = match.group(0)
                
                # 提取src属性
                src_match = re.search(r'src=["\']([^"\']*)["\']', img_tag, re.IGNORECASE)
                src = src_match.group(1) if src_match else ""
                
                # 提取alt属性
                alt_match = re.search(r'alt=["\']([^"\']*)["\']', img_tag, re.IGNORECASE)
                alt = alt_match.group(1) if alt_match else ""
                
                # 提取width和height
                width_match = re.search(r'width=["\']([^"\']*)["\']', img_tag, re.IGNORECASE)
                width = width_match.group(1) if width_match else ""
                
                height_match = re.search(r'height=["\']([^"\']*)["\']', img_tag, re.IGNORECASE)
                height = height_match.group(1) if height_match else ""
                
                images.append({
                    'src': src,
                    'alt': alt,
                    'width': width,
                    'height': height,
                    'tag': img_tag
                })
            
            return images
            
        except Exception as e:
            self.logger.error(f"提取图片信息失败: {e}")
            return []
    
    def _extract_links(self, html_content: str) -> List[Dict]:
        """提取链接信息"""
        try:
            links = []
            
            # 查找所有a标签
            link_pattern = r'<a[^>]*>(.*?)</a>'
            link_matches = re.finditer(link_pattern, html_content, re.IGNORECASE | re.DOTALL)
            
            for match in link_matches:
                link_tag = match.group(0)
                link_text = match.group(1)
                
                # 提取href属性
                href_match = re.search(r'href=["\']([^"\']*)["\']', link_tag, re.IGNORECASE)
                href = href_match.group(1) if href_match else ""
                
                # 提取title属性
                title_match = re.search(r'title=["\']([^"\']*)["\']', link_tag, re.IGNORECASE)
                title = title_match.group(1) if title_match else ""
                
                links.append({
                    'href': href,
                    'text': self._extract_text_from_html(link_text),
                    'title': title,
                    'tag': link_tag
                })
            
            return links
            
        except Exception as e:
            self.logger.error(f"提取链接信息失败: {e}")
            return []
    
    def _extract_styles(self, html_content: str) -> str:
        """提取CSS样式"""
        try:
            styles = []
            
            # 提取style标签中的CSS
            style_pattern = r'<style[^>]*>(.*?)</style>'
            style_matches = re.finditer(style_pattern, html_content, re.IGNORECASE | re.DOTALL)
            
            for match in style_matches:
                styles.append(match.group(1))
            
            return '\n'.join(styles)
            
        except Exception as e:
            self.logger.error(f"提取样式失败: {e}")
            return ""
    
    def _process_inline_images(self, html_content: str, attachments: List[Dict]) -> str:
        """处理内嵌图片"""
        try:
            # 创建Content-ID到附件的映射
            cid_map = {}
            for attachment in attachments:
                content_id = attachment.get('content_id', '')
                if content_id and attachment.get('is_inline', False):
                    cid_map[content_id] = attachment
            
            # 替换cid:引用
            def replace_cid(match):
                cid = match.group(1)
                if cid in cid_map:
                    attachment = cid_map[cid]
                    # 这里可以返回base64编码的图片或占位符
                    return f"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
                return match.group(0)
            
            html_content = re.sub(r'src=["\']cid:([^"\']*)["\']', replace_cid, html_content, flags=re.IGNORECASE)
            
            return html_content
            
        except Exception as e:
            self.logger.error(f"处理内嵌图片失败: {e}")
            return html_content
    
    def generate_safe_html(self, email_content: HTMLEmailContent) -> str:
        """生成安全的HTML用于显示"""
        try:
            if not email_content.cleaned_html:
                # 如果没有HTML内容，从文本生成
                if email_content.text_content:
                    return self._text_to_safe_html(email_content.text_content)
                return "<p>无邮件内容</p>"
            
            # 添加基本的HTML结构
            safe_html = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮件内容</title>
    <style>
        body {{ 
            font-family: Arial, sans-serif; 
            line-height: 1.6; 
            max-width: 800px; 
            margin: 0 auto; 
            padding: 20px; 
        }}
        img {{ max-width: 100%; height: auto; }}
        table {{ border-collapse: collapse; width: 100%; }}
        td, th {{ border: 1px solid #ddd; padding: 8px; }}
        {email_content.styles}
    </style>
</head>
<body>
    {email_content.cleaned_html}
</body>
</html>"""
            
            return safe_html
            
        except Exception as e:
            self.logger.error(f"生成安全HTML失败: {e}")
            return f"<p>HTML生成失败: {e}</p>"
    
    def _text_to_safe_html(self, text_content: str) -> str:
        """将文本转换为安全的HTML"""
        try:
            # 转义HTML特殊字符
            escaped_text = html.escape(text_content)
            
            # 将URL转换为链接
            url_pattern = r'(https?://[^\s]+)'
            escaped_text = re.sub(url_pattern, r'<a href="\1" target="_blank">\1</a>', escaped_text)
            
            # 将换行转换为<br>
            escaped_text = escaped_text.replace('\n', '<br>\n')
            
            return f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {{ font-family: Arial, sans-serif; line-height: 1.6; padding: 20px; }}
    </style>
</head>
<body>
    <div style="white-space: pre-wrap;">{escaped_text}</div>
</body>
</html>"""
            
        except Exception as e:
            self.logger.error(f"文本转HTML失败: {e}")
            return f"<pre>{text_content}</pre>"

def main():
    """测试函数"""
    print("🌐 HTML邮件处理器测试")
    print("=" * 50)
    
    # 创建处理器
    processor = HTMLEmailProcessor()
    
    # 测试HTML内容
    test_html = """
    <html>
    <head><title>Test Email</title></head>
    <body>
        <h1>测试邮件</h1>
        <p>这是一个<strong>HTML邮件</strong>测试。</p>
        <img src="cid:image1" alt="测试图片" width="100">
        <a href="https://example.com">测试链接</a>
        <script>alert('dangerous');</script>
    </body>
    </html>
    """
    
    # 处理HTML邮件
    result = processor.process_html_email(test_html)
    
    print(f"原始HTML长度: {len(result.raw_html)}")
    print(f"清理后HTML长度: {len(result.cleaned_html)}")
    print(f"文本内容: {result.text_content}")
    print(f"图片数量: {len(result.images)}")
    print(f"链接数量: {len(result.links)}")
    
    # 生成安全HTML
    safe_html = processor.generate_safe_html(result)
    print(f"安全HTML长度: {len(safe_html)}")

if __name__ == "__main__":
    main()
