
import requests
import os

def download_file(url, folder, filename):
    """Downloads a file from a URL and saves it to a local folder."""
    local_path = os.path.join(folder, filename)
    
    # Create the folder if it doesn't exist
    if not os.path.exists(folder):
        os.makedirs(folder)
        print(f"Created directory: {folder}")

    try:
        print(f"Downloading {filename} from {url}...")
        response = requests.get(url, timeout=20)
        response.raise_for_status()  # Raise an exception for bad status codes (4xx or 5xx)
        
        with open(local_path, 'w', encoding='utf-8') as f:
            f.write(response.text)
            
        print(f"Successfully saved to {local_path}")
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"Error downloading file: {e}")
        return False

if __name__ == "__main__":
    LUCIDE_URL = "https://unpkg.com/lucide@latest/dist/umd/lucide.min.js"
    ASSETS_FOLDER = "design_iterations"
    
    download_file(LUCIDE_URL, ASSETS_FOLDER, "lucide.min.js")
