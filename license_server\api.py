#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
许可证服务器API接口
"""

import json
from datetime import datetime
from flask import Blueprint, request, jsonify, current_app
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

from models import db, License, LicenseActivation, ValidationLog
from utils import (
    generate_machine_fingerprint, validate_license_key_format,
    get_client_info, validate_request_data, log_api_request,
    create_api_response, format_license_response, check_license_expiry,
    sanitize_user_input
)

# 创建API蓝图
api_bp = Blueprint('api', __name__)

# 创建限流器实例
limiter = Limiter(key_func=get_remote_address)


@api_bp.route('/ping', methods=['GET'])
@limiter.limit("10 per minute")
def ping():
    """服务器状态检查"""
    try:
        log_api_request('ping')
        
        response = create_api_response(
            success=True,
            message='许可证服务器运行正常',
            data={
                'server_time': datetime.utcnow().isoformat(),
                'version': '1.0.0',
                'api_version': 'v1'
            }
        )
        
        return jsonify(response), 200
        
    except Exception as e:
        current_app.logger.error(f"Ping接口错误: {e}")
        return jsonify(create_api_response(
            success=False,
            message='服务器内部错误',
            error_code='INTERNAL_ERROR'
        )), 500


@api_bp.route('/validate', methods=['POST'])
@limiter.limit("20 per minute")
@validate_request_data(['license_key'])
def validate_license():
    """验证许可证"""
    try:
        data = request.get_json()
        license_key = data['license_key'].strip().upper()
        machine_fingerprint = generate_machine_fingerprint(data)
        client_info = get_client_info(request)
        
        log_api_request('validate', {'license_key': license_key[:8] + '****'})
        
        # 验证许可证密钥格式
        if not validate_license_key_format(license_key):
            return jsonify(create_api_response(
                success=False,
                message='许可证密钥格式无效',
                error_code='INVALID_FORMAT'
            )), 400
        
        # 查找许可证
        license_obj = License.query.filter_by(license_key=license_key).first()
        
        if not license_obj:
            # 记录验证失败日志（不需要license_id）
            try:
                log = ValidationLog(
                    license_id=0,  # 使用0表示许可证不存在
                    machine_fingerprint=machine_fingerprint,
                    validation_result='failed',
                    validation_details=json.dumps({'reason': 'license_not_found'}),
                    ip_address=client_info['ip_address'],
                    user_agent=client_info['user_agent']
                )
                db.session.add(log)
                db.session.commit()
            except Exception as log_error:
                # 如果日志记录失败，不影响主要逻辑
                current_app.logger.warning(f"记录验证日志失败: {log_error}")
                db.session.rollback()
            
            return jsonify(create_api_response(
                success=False,
                message='许可证不存在',
                error_code='LICENSE_NOT_FOUND'
            )), 404
        
        # 检查许可证有效性
        if not license_obj.is_valid():
            reason = 'unknown'
            if license_obj.status != 'active':
                reason = f'status_{license_obj.status}'
            elif license_obj.expires_at and datetime.utcnow() > license_obj.expires_at:
                reason = 'expired'
            elif license_obj.current_activations >= license_obj.max_activations:
                reason = 'max_activations_reached'
            
            # 记录验证失败日志
            license_obj.log_validation(machine_fingerprint, 'failed', {'reason': reason})
            db.session.commit()
            
            return jsonify(create_api_response(
                success=False,
                message='许可证无效或已过期',
                error_code='LICENSE_INVALID',
                data={'reason': reason}
            )), 403
        
        # 验证成功，记录日志
        try:
            license_obj.log_validation(machine_fingerprint, 'success', json.dumps(client_info))
            db.session.commit()
        except Exception as log_error:
            current_app.logger.warning(f"记录验证日志失败: {log_error}")
            db.session.rollback()
            # 重新提交许可证更新
            db.session.commit()
        
        # 返回许可证信息
        response_data = format_license_response(license_obj)
        expiry_info = check_license_expiry(license_obj)
        response_data.update(expiry_info)
        
        return jsonify(create_api_response(
            success=True,
            message='许可证验证成功',
            data=response_data
        )), 200
        
    except Exception as e:
        current_app.logger.error(f"验证许可证错误: {e}")
        db.session.rollback()
        return jsonify(create_api_response(
            success=False,
            message='服务器内部错误',
            error_code='INTERNAL_ERROR'
        )), 500


@api_bp.route('/activate', methods=['POST'])
@limiter.limit("10 per minute")
@validate_request_data(['license_key'])
def activate_license():
    """激活许可证"""
    try:
        data = request.get_json()
        license_key = data['license_key'].strip().upper()
        machine_fingerprint = generate_machine_fingerprint(data)
        user_info = sanitize_user_input(data.get('user_info', {}))
        client_info = get_client_info(request)
        
        log_api_request('activate', {'license_key': license_key[:8] + '****'})
        
        # 验证许可证密钥格式
        if not validate_license_key_format(license_key):
            return jsonify(create_api_response(
                success=False,
                message='许可证密钥格式无效',
                error_code='INVALID_FORMAT'
            )), 400
        
        # 查找许可证
        license_obj = License.query.filter_by(license_key=license_key).first()
        
        if not license_obj:
            return jsonify(create_api_response(
                success=False,
                message='许可证不存在',
                error_code='LICENSE_NOT_FOUND'
            )), 404
        
        # 检查是否可以激活
        if not license_obj.can_activate():
            reason = 'unknown'
            if license_obj.status != 'active':
                reason = f'status_{license_obj.status}'
            elif license_obj.expires_at and datetime.utcnow() > license_obj.expires_at:
                reason = 'expired'
            elif license_obj.current_activations >= license_obj.max_activations:
                reason = 'max_activations_reached'
            
            return jsonify(create_api_response(
                success=False,
                message='许可证无法激活',
                error_code='ACTIVATION_FAILED',
                data={'reason': reason}
            )), 403
        
        # 执行激活
        if license_obj.activate(machine_fingerprint, user_info):
            # 更新激活记录的客户端信息
            activation = LicenseActivation.query.filter_by(
                license_id=license_obj.id,
                machine_fingerprint=machine_fingerprint,
                status='active'
            ).first()
            
            if activation:
                activation.ip_address = client_info['ip_address']
                activation.user_agent = client_info['user_agent']
                activation.last_seen_at = datetime.utcnow()
            
            db.session.commit()
            
            # 返回激活成功信息
            response_data = format_license_response(license_obj)
            response_data['activation_id'] = f"ACT-{license_obj.id}-{int(datetime.utcnow().timestamp())}"
            
            return jsonify(create_api_response(
                success=True,
                message='许可证激活成功',
                data=response_data
            )), 200
        else:
            return jsonify(create_api_response(
                success=False,
                message='激活失败',
                error_code='ACTIVATION_FAILED'
            )), 500
        
    except Exception as e:
        current_app.logger.error(f"激活许可证错误: {e}")
        db.session.rollback()
        return jsonify(create_api_response(
            success=False,
            message='服务器内部错误',
            error_code='INTERNAL_ERROR'
        )), 500


@api_bp.route('/deactivate', methods=['POST'])
@limiter.limit("10 per minute")
@validate_request_data(['license_key'])
def deactivate_license():
    """停用许可证"""
    try:
        data = request.get_json()
        license_key = data['license_key'].strip().upper()
        machine_fingerprint = generate_machine_fingerprint(data)
        
        log_api_request('deactivate', {'license_key': license_key[:8] + '****'})
        
        # 查找许可证
        license_obj = License.query.filter_by(license_key=license_key).first()
        
        if not license_obj:
            return jsonify(create_api_response(
                success=False,
                message='许可证不存在',
                error_code='LICENSE_NOT_FOUND'
            )), 404
        
        # 执行停用
        if license_obj.deactivate(machine_fingerprint):
            db.session.commit()
            
            return jsonify(create_api_response(
                success=True,
                message='许可证停用成功',
                data={
                    'deactivation_time': datetime.utcnow().isoformat(),
                    'remaining_activations': license_obj.max_activations - license_obj.current_activations
                }
            )), 200
        else:
            return jsonify(create_api_response(
                success=False,
                message='未找到激活记录',
                error_code='ACTIVATION_NOT_FOUND'
            )), 404
        
    except Exception as e:
        current_app.logger.error(f"停用许可证错误: {e}")
        db.session.rollback()
        return jsonify(create_api_response(
            success=False,
            message='服务器内部错误',
            error_code='INTERNAL_ERROR'
        )), 500


@api_bp.route('/status', methods=['GET'])
@limiter.limit("30 per minute")
def check_status():
    """检查许可证状态"""
    try:
        license_key = request.args.get('license_key', '').strip().upper()
        
        if not license_key:
            return jsonify(create_api_response(
                success=False,
                message='缺少许可证密钥参数',
                error_code='MISSING_PARAMETER'
            )), 400
        
        log_api_request('status', {'license_key': license_key[:8] + '****'})
        
        # 查找许可证
        license_obj = License.query.filter_by(license_key=license_key).first()
        
        if not license_obj:
            return jsonify(create_api_response(
                success=False,
                message='许可证不存在',
                error_code='LICENSE_NOT_FOUND'
            )), 404
        
        # 返回状态信息
        response_data = format_license_response(license_obj)
        expiry_info = check_license_expiry(license_obj)
        response_data.update(expiry_info)
        
        return jsonify(create_api_response(
            success=True,
            message='状态查询成功',
            data=response_data
        )), 200
        
    except Exception as e:
        current_app.logger.error(f"检查状态错误: {e}")
        return jsonify(create_api_response(
            success=False,
            message='服务器内部错误',
            error_code='INTERNAL_ERROR'
        )), 500
