#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
许可证服务器管理后台
"""

import json
from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify, render_template_string, session, redirect, url_for
from werkzeug.security import check_password_hash

from models import db, License, LicenseActivation, ValidationLog, Admin
from utils import validate_license_features, sanitize_user_input

# 创建管理后台蓝图
admin_bp = Blueprint('admin', __name__)


def login_required(f):
    """登录验证装饰器"""
    from functools import wraps
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'admin_id' not in session:
            return redirect(url_for('admin.login'))
        return f(*args, **kwargs)
    return decorated_function


@admin_bp.route('/login', methods=['GET', 'POST'])
def login():
    """管理员登录"""
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        admin = Admin.query.filter_by(username=username).first()
        
        if admin and admin.check_password(password) and admin.is_active:
            session['admin_id'] = admin.id
            admin.last_login_at = datetime.utcnow()
            db.session.commit()
            return redirect(url_for('admin.dashboard'))
        else:
            return render_template_string(LOGIN_TEMPLATE, error='用户名或密码错误')
    
    return render_template_string(LOGIN_TEMPLATE)


@admin_bp.route('/logout')
def logout():
    """管理员登出"""
    session.pop('admin_id', None)
    return redirect(url_for('admin.login'))


@admin_bp.route('/')
@admin_bp.route('/dashboard')
@login_required
def dashboard():
    """管理后台首页"""
    # 统计数据
    total_licenses = License.query.count()
    active_licenses = License.query.filter_by(status='active').count()
    expired_licenses = License.query.filter(
        License.expires_at < datetime.utcnow()
    ).count()
    
    recent_validations = ValidationLog.query.order_by(
        ValidationLog.validated_at.desc()
    ).limit(10).all()
    
    stats = {
        'total_licenses': total_licenses,
        'active_licenses': active_licenses,
        'expired_licenses': expired_licenses,
        'recent_validations': [log.to_dict() for log in recent_validations]
    }
    
    return render_template_string(DASHBOARD_TEMPLATE, stats=stats)


@admin_bp.route('/licenses')
@login_required
def list_licenses():
    """许可证列表"""
    page = request.args.get('page', 1, type=int)
    per_page = 20
    
    licenses = License.query.order_by(License.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return render_template_string(LICENSES_TEMPLATE, licenses=licenses)


@admin_bp.route('/licenses/create', methods=['GET', 'POST'])
@login_required
def create_license():
    """创建许可证"""
    if request.method == 'POST':
        try:
            data = request.form.to_dict()
            
            # 创建许可证
            license_obj = License(
                license_type=data.get('license_type', 'standard'),
                max_activations=int(data.get('max_activations', 1)),
                user_name=data.get('user_name', ''),
                user_email=data.get('user_email', ''),
                company_name=data.get('company_name', ''),
                notes=data.get('notes', '')
            )
            
            # 设置过期时间
            if data.get('expires_days'):
                days = int(data['expires_days'])
                license_obj.expires_at = datetime.utcnow() + timedelta(days=days)
            
            # 设置功能
            features = []
            if data.get('feature_email_management'):
                features.append('email_management')
            if data.get('feature_batch_import'):
                features.append('batch_import')
            if data.get('feature_advanced_search'):
                features.append('advanced_search')
            
            license_obj.features = json.dumps(features or ['email_management'])
            
            db.session.add(license_obj)
            db.session.commit()
            
            return redirect(url_for('admin.list_licenses'))
            
        except Exception as e:
            db.session.rollback()
            return render_template_string(CREATE_LICENSE_TEMPLATE, error=str(e))
    
    return render_template_string(CREATE_LICENSE_TEMPLATE)


@admin_bp.route('/licenses/<int:license_id>')
@login_required
def view_license(license_id):
    """查看许可证详情"""
    license_obj = License.query.get_or_404(license_id)

    # 获取激活记录
    activations = LicenseActivation.query.filter_by(license_id=license_id).all()

    # 获取验证日志
    validation_logs = ValidationLog.query.filter_by(license_id=license_id).order_by(
        ValidationLog.validated_at.desc()
    ).limit(20).all()

    return render_template_string(
        LICENSE_DETAIL_TEMPLATE,
        license=license_obj,
        activations=activations,
        validation_logs=validation_logs
    )


@admin_bp.route('/licenses/<int:license_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_license(license_id):
    """编辑许可证"""
    license_obj = License.query.get_or_404(license_id)

    if request.method == 'POST':
        try:
            data = request.form

            # 更新基本信息
            license_obj.license_type = data.get('license_type', 'standard')
            license_obj.max_activations = int(data.get('max_activations', 1))
            license_obj.status = data.get('status', 'active')

            # 更新用户信息
            license_obj.user_name = data.get('user_name', '').strip()
            license_obj.user_email = data.get('user_email', '').strip()
            license_obj.company_name = data.get('company_name', '').strip()
            license_obj.notes = data.get('notes', '').strip()

            # 更新过期时间
            expires_days = data.get('expires_days')
            if expires_days:
                try:
                    days = int(expires_days)
                    if days > 0:
                        license_obj.expires_at = datetime.utcnow() + timedelta(days=days)
                    else:
                        license_obj.expires_at = None  # 永久许可证
                except ValueError:
                    pass

            # 设置功能
            features = []
            if data.get('feature_email_management'):
                features.append('email_management')
            if data.get('feature_batch_import'):
                features.append('batch_import')
            if data.get('feature_advanced_search'):
                features.append('advanced_search')

            license_obj.features = json.dumps(features or ['email_management'])

            db.session.commit()

            return redirect(url_for('admin.view_license', license_id=license_id))

        except Exception as e:
            db.session.rollback()
            # 解析features用于模板显示
            try:
                license_features = json.loads(license_obj.features) if license_obj.features else ['email_management']
            except:
                license_features = ['email_management']
            return render_template_string(EDIT_LICENSE_TEMPLATE, license=license_obj, license_features=license_features, error=str(e))

    # 解析features用于模板显示
    try:
        license_features = json.loads(license_obj.features) if license_obj.features else ['email_management']
    except:
        license_features = ['email_management']

    return render_template_string(EDIT_LICENSE_TEMPLATE, license=license_obj, license_features=license_features)


@admin_bp.route('/licenses/<int:license_id>/delete', methods=['GET', 'POST'])
@login_required
def delete_license(license_id):
    """删除许可证"""
    license_obj = License.query.get_or_404(license_id)

    if request.method == 'POST':
        try:
            # 记录删除信息用于日志
            license_key = license_obj.license_key
            user_name = license_obj.user_name or '未知用户'

            # 删除许可证（会级联删除相关的激活记录和验证日志）
            db.session.delete(license_obj)
            db.session.commit()

            # 可以在这里添加删除日志记录
            # logger.info(f"许可证已删除: {license_key} (用户: {user_name})")

            return redirect(url_for('admin.list_licenses'))

        except Exception as e:
            db.session.rollback()
            return render_template_string(DELETE_LICENSE_TEMPLATE, license=license_obj, error=str(e))

    return render_template_string(DELETE_LICENSE_TEMPLATE, license=license_obj)


@admin_bp.route('/licenses/batch_create', methods=['GET', 'POST'])
@login_required
def batch_create_licenses():
    """批量创建许可证"""
    if request.method == 'POST':
        try:
            data = request.form

            # 获取批量创建参数
            count = int(data.get('count', 1))
            license_type = data.get('license_type', 'standard')
            max_activations = int(data.get('max_activations', 1))
            expires_days = data.get('expires_days')

            # 用户信息（可选）
            user_name_prefix = data.get('user_name_prefix', '').strip()
            company_name = data.get('company_name', '').strip()
            notes_prefix = data.get('notes_prefix', '').strip()

            # 功能权限
            features = []
            if data.get('feature_email_management'):
                features.append('email_management')
            if data.get('feature_batch_import'):
                features.append('batch_import')
            if data.get('feature_advanced_search'):
                features.append('advanced_search')

            # 验证数量限制
            if count < 1 or count > 100:
                raise ValueError("批量创建数量必须在1-100之间")

            created_licenses = []

            # 批量创建许可证
            for i in range(count):
                license_obj = License(
                    license_type=license_type,
                    max_activations=max_activations,
                    user_name=f"{user_name_prefix}{i+1}" if user_name_prefix else '',
                    company_name=company_name,
                    notes=f"{notes_prefix} #{i+1}" if notes_prefix else f"批量创建 #{i+1}"
                )

                # 设置过期时间
                if expires_days:
                    days = int(expires_days)
                    license_obj.expires_at = datetime.utcnow() + timedelta(days=days)

                # 设置功能
                license_obj.features = json.dumps(features or ['email_management'])

                db.session.add(license_obj)
                created_licenses.append(license_obj)

            db.session.commit()

            # 返回成功页面，显示创建的许可证
            return render_template_string(
                BATCH_CREATE_SUCCESS_TEMPLATE,
                licenses=created_licenses,
                count=count
            )

        except Exception as e:
            db.session.rollback()
            return render_template_string(BATCH_CREATE_TEMPLATE, error=str(e))

    return render_template_string(BATCH_CREATE_TEMPLATE)


@admin_bp.route('/api/licenses', methods=['GET'])
@login_required
def api_list_licenses():
    """API: 获取许可证列表"""
    try:
        licenses = License.query.all()
        return jsonify({
            'success': True,
            'data': [license.to_dict() for license in licenses]
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@admin_bp.route('/api/licenses', methods=['POST'])
@login_required
def api_create_license():
    """API: 创建许可证"""
    try:
        data = request.get_json()
        
        license_obj = License(
            license_type=data.get('license_type', 'standard'),
            max_activations=data.get('max_activations', 1),
            user_name=data.get('user_name', ''),
            user_email=data.get('user_email', ''),
            company_name=data.get('company_name', ''),
            notes=data.get('notes', '')
        )
        
        # 设置过期时间
        if data.get('expires_days'):
            days = int(data['expires_days'])
            license_obj.expires_at = datetime.utcnow() + timedelta(days=days)
        
        # 设置功能
        features = validate_license_features(data.get('features', []))
        license_obj.features = json.dumps(features)
        
        db.session.add(license_obj)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'data': license_obj.to_dict(include_sensitive=True)
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500


@admin_bp.route('/api/licenses/<int:license_id>', methods=['PUT'])
@login_required
def api_update_license(license_id):
    """API: 更新许可证"""
    try:
        license_obj = License.query.get_or_404(license_id)
        data = request.get_json()
        
        # 更新字段
        if 'status' in data:
            license_obj.status = data['status']
        if 'max_activations' in data:
            license_obj.max_activations = int(data['max_activations'])
        if 'user_name' in data:
            license_obj.user_name = data['user_name']
        if 'user_email' in data:
            license_obj.user_email = data['user_email']
        if 'company_name' in data:
            license_obj.company_name = data['company_name']
        if 'notes' in data:
            license_obj.notes = data['notes']
        
        # 更新过期时间
        if 'expires_days' in data:
            if data['expires_days']:
                days = int(data['expires_days'])
                license_obj.expires_at = datetime.utcnow() + timedelta(days=days)
            else:
                license_obj.expires_at = None
        
        # 更新功能
        if 'features' in data:
            features = validate_license_features(data['features'])
            license_obj.features = json.dumps(features)
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'data': license_obj.to_dict(include_sensitive=True)
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500


@admin_bp.route('/api/licenses/<int:license_id>', methods=['DELETE'])
@login_required
def api_delete_license(license_id):
    """API: 删除许可证"""
    try:
        license_obj = License.query.get_or_404(license_id)
        db.session.delete(license_obj)
        db.session.commit()
        
        return jsonify({'success': True, 'message': '许可证已删除'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500


# HTML模板
LOGIN_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>许可证服务器 - 管理登录</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 50px; background: #f5f5f5; }
        .login-form { max-width: 400px; margin: 100px auto; padding: 30px; background: white; border-radius: 5px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"], input[type="password"] { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 3px; box-sizing: border-box; }
        button { width: 100%; padding: 12px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; font-size: 16px; }
        button:hover { background: #005a87; }
        .error { color: red; margin-top: 10px; }
        h2 { text-align: center; color: #333; }
    </style>
</head>
<body>
    <div class="login-form">
        <h2>许可证服务器管理</h2>
        <form method="post">
            <div class="form-group">
                <label>用户名:</label>
                <input type="text" name="username" required>
            </div>
            <div class="form-group">
                <label>密码:</label>
                <input type="password" name="password" required>
            </div>
            <button type="submit">登录</button>
            {% if error %}
                <div class="error">{{ error }}</div>
            {% endif %}
        </form>
    </div>
</body>
</html>
'''

DASHBOARD_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>许可证服务器 - 管理后台</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 0; background: #f5f5f5; }
        .header { background: #007cba; color: white; padding: 15px 20px; }
        .header h1 { margin: 0; display: inline-block; }
        .header .logout { float: right; color: white; text-decoration: none; }
        .container { padding: 20px; }
        .stats { display: flex; gap: 20px; margin-bottom: 30px; }
        .stat-card { background: white; padding: 20px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); flex: 1; text-align: center; }
        .stat-number { font-size: 2em; font-weight: bold; color: #007cba; }
        .stat-label { color: #666; margin-top: 5px; }
        .nav { margin-bottom: 20px; }
        .nav a { display: inline-block; padding: 10px 20px; background: #007cba; color: white; text-decoration: none; margin-right: 10px; border-radius: 3px; }
        .nav a:hover { background: #005a87; }
    </style>
</head>
<body>
    <div class="header">
        <h1>许可证服务器管理后台</h1>
        <a href="{{ url_for('admin.logout') }}" class="logout">登出</a>
    </div>
    <div class="container">
        <div class="nav">
            <a href="{{ url_for('admin.dashboard') }}">首页</a>
            <a href="{{ url_for('admin.list_licenses') }}">许可证管理</a>
            <a href="{{ url_for('admin.create_license') }}">创建许可证</a>
            <a href="{{ url_for('admin.batch_create_licenses') }}">批量创建</a>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">{{ stats.total_licenses }}</div>
                <div class="stat-label">总许可证数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ stats.active_licenses }}</div>
                <div class="stat-label">活跃许可证</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ stats.expired_licenses }}</div>
                <div class="stat-label">过期许可证</div>
            </div>
        </div>
        
        <h3>最近验证记录</h3>
        <table style="width: 100%; background: white; border-collapse: collapse;">
            <tr style="background: #f0f0f0;">
                <th style="padding: 10px; text-align: left; border: 1px solid #ddd;">时间</th>
                <th style="padding: 10px; text-align: left; border: 1px solid #ddd;">结果</th>
                <th style="padding: 10px; text-align: left; border: 1px solid #ddd;">IP地址</th>
            </tr>
            {% for log in stats.recent_validations %}
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">{{ log.validated_at }}</td>
                <td style="padding: 10px; border: 1px solid #ddd;">{{ log.validation_result }}</td>
                <td style="padding: 10px; border: 1px solid #ddd;">{{ log.ip_address }}</td>
            </tr>
            {% endfor %}
        </table>
    </div>
</body>
</html>
'''

LICENSES_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>许可证管理</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 0; background: #f5f5f5; }
        .header { background: #007cba; color: white; padding: 15px 20px; }
        .header h1 { margin: 0; display: inline-block; }
        .header .logout { float: right; color: white; text-decoration: none; }
        .container { padding: 20px; }
        .nav { margin-bottom: 20px; }
        .nav a { display: inline-block; padding: 10px 20px; background: #007cba; color: white; text-decoration: none; margin-right: 10px; border-radius: 3px; }
        .nav a:hover { background: #005a87; }
        table { width: 100%; background: white; border-collapse: collapse; }
        th, td { padding: 10px; text-align: left; border: 1px solid #ddd; }
        th { background: #f0f0f0; }
        .status-active { color: green; font-weight: bold; }
        .status-expired { color: red; font-weight: bold; }
        .status-suspended { color: orange; font-weight: bold; }
        .action-links a { margin-right: 8px; padding: 4px 8px; text-decoration: none; border-radius: 3px; font-size: 12px; }
        .action-links a:hover { opacity: 0.8; }
        .action-view { background: #17a2b8; color: white; }
        .action-edit { background: #28a745; color: white; }
        .action-delete { background: #dc3545; color: white; }
    </style>
</head>
<body>
    <div class="header">
        <h1>许可证管理</h1>
        <a href="{{ url_for('admin.logout') }}" class="logout">登出</a>
    </div>
    <div class="container">
        <div class="nav">
            <a href="{{ url_for('admin.dashboard') }}">首页</a>
            <a href="{{ url_for('admin.list_licenses') }}">许可证管理</a>
            <a href="{{ url_for('admin.create_license') }}">创建许可证</a>
            <a href="{{ url_for('admin.batch_create_licenses') }}">批量创建</a>
        </div>

        <table>
            <tr>
                <th>许可证密钥</th>
                <th>类型</th>
                <th>状态</th>
                <th>用户</th>
                <th>激活数/最大数</th>
                <th>创建时间</th>
                <th>过期时间</th>
                <th>操作</th>
            </tr>
            {% for license in licenses.items %}
            <tr>
                <td>{{ license.license_key[:8] }}****</td>
                <td>{{ license.license_type }}</td>
                <td class="status-{{ license.status }}">{{ license.status }}</td>
                <td>{{ license.user_name or '未设置' }}</td>
                <td>{{ license.current_activations }}/{{ license.max_activations }}</td>
                <td>{{ license.created_at.strftime('%Y-%m-%d %H:%M') if license.created_at else '' }}</td>
                <td>{{ license.expires_at.strftime('%Y-%m-%d %H:%M') if license.expires_at else '永久' }}</td>
                <td class="action-links">
                    <a href="{{ url_for('admin.view_license', license_id=license.id) }}" class="action-view">查看</a>
                    <a href="{{ url_for('admin.edit_license', license_id=license.id) }}" class="action-edit">编辑</a>
                    <a href="{{ url_for('admin.delete_license', license_id=license.id) }}"
                       class="action-delete"
                       onclick="return confirm('确定要删除这个许可证吗？此操作不可撤销！')">删除</a>
                </td>
            </tr>
            {% endfor %}
        </table>

        <!-- 分页 -->
        {% if licenses.pages > 1 %}
        <div style="margin-top: 20px; text-align: center;">
            {% if licenses.has_prev %}
                <a href="{{ url_for('admin.list_licenses', page=licenses.prev_num) }}">&laquo; 上一页</a>
            {% endif %}

            {% for page_num in licenses.iter_pages() %}
                {% if page_num %}
                    {% if page_num != licenses.page %}
                        <a href="{{ url_for('admin.list_licenses', page=page_num) }}">{{ page_num }}</a>
                    {% else %}
                        <strong>{{ page_num }}</strong>
                    {% endif %}
                {% else %}
                    <span>…</span>
                {% endif %}
            {% endfor %}

            {% if licenses.has_next %}
                <a href="{{ url_for('admin.list_licenses', page=licenses.next_num) }}">下一页 &raquo;</a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</body>
</html>
'''

CREATE_LICENSE_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>创建许可证</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 0; background: #f5f5f5; }
        .header { background: #007cba; color: white; padding: 15px 20px; }
        .header h1 { margin: 0; display: inline-block; }
        .header .logout { float: right; color: white; text-decoration: none; }
        .container { padding: 20px; }
        .nav { margin-bottom: 20px; }
        .nav a { display: inline-block; padding: 10px 20px; background: #007cba; color: white; text-decoration: none; margin-right: 10px; border-radius: 3px; }
        .nav a:hover { background: #005a87; }
        .form { background: white; padding: 30px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); max-width: 600px; }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select, textarea { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 3px; box-sizing: border-box; }
        textarea { height: 100px; resize: vertical; }
        button { padding: 12px 30px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        .error { color: red; margin-top: 10px; }
        .checkbox-group { display: flex; flex-wrap: wrap; gap: 15px; }
        .checkbox-group label { display: flex; align-items: center; font-weight: normal; }
        .checkbox-group input { width: auto; margin-right: 5px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>创建许可证</h1>
        <a href="{{ url_for('admin.logout') }}" class="logout">登出</a>
    </div>
    <div class="container">
        <div class="nav">
            <a href="{{ url_for('admin.dashboard') }}">首页</a>
            <a href="{{ url_for('admin.list_licenses') }}">许可证管理</a>
            <a href="{{ url_for('admin.create_license') }}">创建许可证</a>
            <a href="{{ url_for('admin.batch_create_licenses') }}">批量创建</a>
        </div>

        <div class="form">
            <h2>创建新许可证</h2>
            <form method="post">
                <div class="form-group">
                    <label>许可证类型:</label>
                    <select name="license_type">
                        <option value="standard">标准版</option>
                        <option value="premium">高级版</option>
                        <option value="enterprise">企业版</option>
                    </select>
                </div>

                <div class="form-group">
                    <label>最大激活数:</label>
                    <input type="number" name="max_activations" value="1" min="1" max="100">
                </div>

                <div class="form-group">
                    <label>有效期（天数，留空表示永久）:</label>
                    <input type="number" name="expires_days" min="1" max="3650">
                </div>

                <div class="form-group">
                    <label>用户姓名:</label>
                    <input type="text" name="user_name">
                </div>

                <div class="form-group">
                    <label>用户邮箱:</label>
                    <input type="email" name="user_email">
                </div>

                <div class="form-group">
                    <label>公司名称:</label>
                    <input type="text" name="company_name">
                </div>

                <div class="form-group">
                    <label>功能权限:</label>
                    <div class="checkbox-group">
                        <label><input type="checkbox" name="feature_email_management" checked> 邮件管理</label>
                        <label><input type="checkbox" name="feature_batch_import"> 批量导入</label>
                        <label><input type="checkbox" name="feature_advanced_search"> 高级搜索</label>
                    </div>
                </div>

                <div class="form-group">
                    <label>备注:</label>
                    <textarea name="notes"></textarea>
                </div>

                <button type="submit">创建许可证</button>

                {% if error %}
                    <div class="error">{{ error }}</div>
                {% endif %}
            </form>
        </div>
    </div>
</body>
</html>
'''

LICENSE_DETAIL_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>许可证详情</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 0; background: #f5f5f5; }
        .header { background: #007cba; color: white; padding: 15px 20px; }
        .header h1 { margin: 0; display: inline-block; }
        .header .logout { float: right; color: white; text-decoration: none; }
        .container { padding: 20px; }
        .nav { margin-bottom: 20px; }
        .nav a { display: inline-block; padding: 10px 20px; background: #007cba; color: white; text-decoration: none; margin-right: 10px; border-radius: 3px; }
        .nav a:hover { background: #005a87; }
        .info-card { background: white; padding: 20px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .info-row { display: flex; margin-bottom: 10px; }
        .info-label { font-weight: bold; width: 150px; }
        .info-value { flex: 1; }
        table { width: 100%; background: white; border-collapse: collapse; }
        th, td { padding: 10px; text-align: left; border: 1px solid #ddd; }
        th { background: #f0f0f0; }
        .status-active { color: green; font-weight: bold; }
        .status-expired { color: red; font-weight: bold; }
        .license-key { font-family: monospace; background: #f0f0f0; padding: 5px; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>许可证详情</h1>
        <a href="{{ url_for('admin.logout') }}" class="logout">登出</a>
    </div>
    <div class="container">
        <div class="nav">
            <a href="{{ url_for('admin.dashboard') }}">首页</a>
            <a href="{{ url_for('admin.list_licenses') }}">许可证管理</a>
            <a href="{{ url_for('admin.create_license') }}">创建许可证</a>
            <a href="{{ url_for('admin.batch_create_licenses') }}">批量创建</a>
            <a href="{{ url_for('admin.edit_license', license_id=license.id) }}">编辑许可证</a>
            <a href="{{ url_for('admin.delete_license', license_id=license.id) }}"
               style="background: #dc3545;"
               onclick="return confirm('确定要删除这个许可证吗？此操作不可撤销！')">删除许可证</a>
        </div>

        <div class="info-card">
            <h3>基本信息</h3>
            <div class="info-row">
                <div class="info-label">许可证密钥:</div>
                <div class="info-value"><span class="license-key">{{ license.license_key }}</span></div>
            </div>
            <div class="info-row">
                <div class="info-label">类型:</div>
                <div class="info-value">{{ license.license_type }}</div>
            </div>
            <div class="info-row">
                <div class="info-label">状态:</div>
                <div class="info-value"><span class="status-{{ license.status }}">{{ license.status }}</span></div>
            </div>
            <div class="info-row">
                <div class="info-label">激活数:</div>
                <div class="info-value">{{ license.current_activations }}/{{ license.max_activations }}</div>
            </div>
            <div class="info-row">
                <div class="info-label">创建时间:</div>
                <div class="info-value">{{ license.created_at.strftime('%Y-%m-%d %H:%M:%S') if license.created_at else '' }}</div>
            </div>
            <div class="info-row">
                <div class="info-label">过期时间:</div>
                <div class="info-value">{{ license.expires_at.strftime('%Y-%m-%d %H:%M:%S') if license.expires_at else '永久' }}</div>
            </div>
            <div class="info-row">
                <div class="info-label">用户信息:</div>
                <div class="info-value">{{ license.user_name or '未设置' }} ({{ license.user_email or '未设置' }})</div>
            </div>
            <div class="info-row">
                <div class="info-label">公司:</div>
                <div class="info-value">{{ license.company_name or '未设置' }}</div>
            </div>
        </div>

        <div class="info-card">
            <h3>激活记录</h3>
            <table>
                <tr>
                    <th>机器指纹</th>
                    <th>状态</th>
                    <th>激活时间</th>
                    <th>最后访问</th>
                    <th>IP地址</th>
                </tr>
                {% for activation in activations %}
                <tr>
                    <td>{{ activation.machine_fingerprint[:16] }}...</td>
                    <td>{{ activation.status }}</td>
                    <td>{{ activation.activated_at.strftime('%Y-%m-%d %H:%M') if activation.activated_at else '' }}</td>
                    <td>{{ activation.last_seen_at.strftime('%Y-%m-%d %H:%M') if activation.last_seen_at else '' }}</td>
                    <td>{{ activation.ip_address or '未知' }}</td>
                </tr>
                {% endfor %}
            </table>
        </div>

        <div class="info-card">
            <h3>验证日志</h3>
            <table>
                <tr>
                    <th>时间</th>
                    <th>结果</th>
                    <th>机器指纹</th>
                    <th>IP地址</th>
                </tr>
                {% for log in validation_logs %}
                <tr>
                    <td>{{ log.validated_at.strftime('%Y-%m-%d %H:%M:%S') if log.validated_at else '' }}</td>
                    <td>{{ log.validation_result }}</td>
                    <td>{{ log.machine_fingerprint[:16] }}...</td>
                    <td>{{ log.ip_address or '未知' }}</td>
                </tr>
                {% endfor %}
            </table>
        </div>
    </div>
</body>
</html>
'''

EDIT_LICENSE_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>编辑许可证</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 0; background: #f5f5f5; }
        .header { background: #007cba; color: white; padding: 15px 20px; }
        .header h1 { margin: 0; display: inline-block; }
        .header .logout { float: right; color: white; text-decoration: none; }
        .container { padding: 20px; }
        .nav { margin-bottom: 20px; }
        .nav a { display: inline-block; padding: 10px 20px; background: #007cba; color: white; text-decoration: none; margin-right: 10px; border-radius: 3px; }
        .nav a:hover { background: #005a87; }
        .form { background: white; padding: 30px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); max-width: 600px; }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select, textarea { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 3px; box-sizing: border-box; }
        textarea { height: 100px; resize: vertical; }
        button { padding: 12px 30px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; margin-right: 10px; }
        button:hover { background: #005a87; }
        .btn-secondary { background: #6c757d; }
        .btn-secondary:hover { background: #545b62; }
        .error { color: red; margin-top: 10px; }
        .success { color: green; margin-top: 10px; }
        .checkbox-group { display: flex; flex-wrap: wrap; gap: 15px; }
        .checkbox-group label { display: flex; align-items: center; font-weight: normal; }
        .checkbox-group input { width: auto; margin-right: 5px; }
        .license-key { font-family: monospace; background: #f0f0f0; padding: 10px; border-radius: 3px; margin-bottom: 20px; }
        .readonly { background: #f8f9fa; color: #6c757d; }
    </style>
</head>
<body>
    <div class="header">
        <h1>编辑许可证</h1>
        <a href="{{ url_for('admin.logout') }}" class="logout">登出</a>
    </div>
    <div class="container">
        <div class="nav">
            <a href="{{ url_for('admin.dashboard') }}">首页</a>
            <a href="{{ url_for('admin.list_licenses') }}">许可证管理</a>
            <a href="{{ url_for('admin.create_license') }}">创建许可证</a>
            <a href="{{ url_for('admin.batch_create_licenses') }}">批量创建</a>
            <a href="{{ url_for('admin.view_license', license_id=license.id) }}">查看详情</a>
        </div>

        <div class="form">
            <h2>编辑许可证</h2>

            <div class="license-key">
                <strong>许可证密钥:</strong> {{ license.license_key }}
            </div>

            <form method="post">
                <div class="form-group">
                    <label>许可证类型:</label>
                    <select name="license_type">
                        <option value="standard" {% if license.license_type == 'standard' %}selected{% endif %}>标准版</option>
                        <option value="premium" {% if license.license_type == 'premium' %}selected{% endif %}>高级版</option>
                        <option value="enterprise" {% if license.license_type == 'enterprise' %}selected{% endif %}>企业版</option>
                    </select>
                </div>

                <div class="form-group">
                    <label>状态:</label>
                    <select name="status">
                        <option value="active" {% if license.status == 'active' %}selected{% endif %}>激活</option>
                        <option value="suspended" {% if license.status == 'suspended' %}selected{% endif %}>暂停</option>
                        <option value="expired" {% if license.status == 'expired' %}selected{% endif %}>过期</option>
                        <option value="revoked" {% if license.status == 'revoked' %}selected{% endif %}>撤销</option>
                    </select>
                </div>

                <div class="form-group">
                    <label>最大激活数:</label>
                    <input type="number" name="max_activations" value="{{ license.max_activations }}" min="1" max="100">
                </div>

                <div class="form-group">
                    <label>有效期（天数，留空表示永久）:</label>
                    <input type="number" name="expires_days" min="1" max="3650"
                           placeholder="当前: {% if license.expires_at %}{{ license.expires_at.strftime('%Y-%m-%d %H:%M') }}{% else %}永久{% endif %}">
                    <small style="color: #666;">输入天数将从当前时间开始计算新的过期时间</small>
                </div>

                <div class="form-group">
                    <label>用户姓名:</label>
                    <input type="text" name="user_name" value="{{ license.user_name or '' }}">
                </div>

                <div class="form-group">
                    <label>用户邮箱:</label>
                    <input type="email" name="user_email" value="{{ license.user_email or '' }}">
                </div>

                <div class="form-group">
                    <label>公司名称:</label>
                    <input type="text" name="company_name" value="{{ license.company_name or '' }}">
                </div>

                <div class="form-group">
                    <label>功能权限:</label>
                    <div class="checkbox-group">
                        <label><input type="checkbox" name="feature_email_management" {{ 'checked' if license_features and 'email_management' in license_features else '' }}> 邮件管理</label>
                        <label><input type="checkbox" name="feature_batch_import" {{ 'checked' if license_features and 'batch_import' in license_features else '' }}> 批量导入</label>
                        <label><input type="checkbox" name="feature_advanced_search" {{ 'checked' if license_features and 'advanced_search' in license_features else '' }}> 高级搜索</label>
                    </div>
                </div>

                <div class="form-group">
                    <label>备注:</label>
                    <textarea name="notes">{{ license.notes or '' }}</textarea>
                </div>

                <button type="submit">保存修改</button>
                <a href="{{ url_for('admin.view_license', license_id=license.id) }}" class="btn-secondary" style="display: inline-block; padding: 12px 30px; text-decoration: none; border-radius: 3px;">取消</a>

                {% if error %}
                    <div class="error">{{ error }}</div>
                {% endif %}
            </form>
        </div>
    </div>
</body>
</html>
'''

DELETE_LICENSE_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>删除许可证</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 0; background: #f5f5f5; }
        .header { background: #007cba; color: white; padding: 15px 20px; }
        .header h1 { margin: 0; display: inline-block; }
        .header .logout { float: right; color: white; text-decoration: none; }
        .container { padding: 20px; }
        .nav { margin-bottom: 20px; }
        .nav a { display: inline-block; padding: 10px 20px; background: #007cba; color: white; text-decoration: none; margin-right: 10px; border-radius: 3px; }
        .nav a:hover { background: #005a87; }
        .form { background: white; padding: 30px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); max-width: 600px; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .license-info { background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .license-key { font-family: monospace; background: #e9ecef; padding: 5px; border-radius: 3px; }
        button { padding: 12px 30px; border: none; border-radius: 3px; cursor: pointer; margin-right: 10px; font-size: 16px; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-danger:hover { background: #c82333; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn-secondary:hover { background: #545b62; }
        .error { color: red; margin-top: 10px; }
        .info-row { display: flex; margin-bottom: 10px; }
        .info-label { font-weight: bold; width: 120px; }
        .info-value { flex: 1; }
    </style>
</head>
<body>
    <div class="header">
        <h1>删除许可证</h1>
        <a href="{{ url_for('admin.logout') }}" class="logout">登出</a>
    </div>
    <div class="container">
        <div class="nav">
            <a href="{{ url_for('admin.dashboard') }}">首页</a>
            <a href="{{ url_for('admin.list_licenses') }}">许可证管理</a>
            <a href="{{ url_for('admin.batch_create_licenses') }}">批量创建</a>
            <a href="{{ url_for('admin.view_license', license_id=license.id) }}">返回详情</a>
        </div>

        <div class="form">
            <h2>⚠️ 删除许可证确认</h2>

            <div class="warning">
                <strong>警告：</strong>此操作将永久删除许可证及其所有相关数据，包括：
                <ul>
                    <li>许可证基本信息</li>
                    <li>所有激活记录</li>
                    <li>所有验证日志</li>
                </ul>
                <strong>此操作不可撤销！</strong>
            </div>

            <div class="license-info">
                <h3>即将删除的许可证信息：</h3>
                <div class="info-row">
                    <div class="info-label">许可证密钥:</div>
                    <div class="info-value"><span class="license-key">{{ license.license_key }}</span></div>
                </div>
                <div class="info-row">
                    <div class="info-label">类型:</div>
                    <div class="info-value">{{ license.license_type }}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">状态:</div>
                    <div class="info-value">{{ license.status }}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">用户:</div>
                    <div class="info-value">{{ license.user_name or '未设置' }}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">公司:</div>
                    <div class="info-value">{{ license.company_name or '未设置' }}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">激活数:</div>
                    <div class="info-value">{{ license.current_activations }}/{{ license.max_activations }}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">创建时间:</div>
                    <div class="info-value">{{ license.created_at.strftime('%Y-%m-%d %H:%M:%S') if license.created_at else '' }}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">过期时间:</div>
                    <div class="info-value">{{ license.expires_at.strftime('%Y-%m-%d %H:%M:%S') if license.expires_at else '永久' }}</div>
                </div>
            </div>

            <form method="post">
                <p><strong>请确认您要删除此许可证：</strong></p>
                <button type="submit" class="btn-danger" onclick="return confirm('最后确认：真的要删除这个许可证吗？')">确认删除</button>
                <a href="{{ url_for('admin.view_license', license_id=license.id) }}" class="btn-secondary" style="display: inline-block; padding: 12px 30px; text-decoration: none;">取消</a>

                {% if error %}
                    <div class="error">删除失败：{{ error }}</div>
                {% endif %}
            </form>
        </div>
    </div>
</body>
</html>
'''

BATCH_CREATE_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>批量创建许可证</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 0; background: #f5f5f5; }
        .header { background: #007cba; color: white; padding: 15px 20px; }
        .header h1 { margin: 0; display: inline-block; }
        .header .logout { float: right; color: white; text-decoration: none; }
        .container { padding: 20px; }
        .nav { margin-bottom: 20px; }
        .nav a { display: inline-block; padding: 10px 20px; background: #007cba; color: white; text-decoration: none; margin-right: 10px; border-radius: 3px; }
        .nav a:hover { background: #005a87; }
        .form { background: white; padding: 30px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); max-width: 800px; }
        .form-group { margin-bottom: 20px; }
        .form-row { display: flex; gap: 20px; }
        .form-row .form-group { flex: 1; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select, textarea { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 3px; box-sizing: border-box; }
        textarea { height: 80px; resize: vertical; }
        button { padding: 12px 30px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        .error { color: red; margin-top: 10px; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .checkbox-group { display: flex; flex-wrap: wrap; gap: 15px; }
        .checkbox-group label { display: flex; align-items: center; font-weight: normal; }
        .checkbox-group input { width: auto; margin-right: 5px; }
        .preview { background: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 15px; }
        .count-display { font-size: 1.2em; font-weight: bold; color: #007cba; }
    </style>
    <script>
        function updatePreview() {
            const count = document.getElementById('count').value || 1;
            const prefix = document.getElementById('user_name_prefix').value || '';
            const company = document.getElementById('company_name').value || '';
            const type = document.getElementById('license_type').value;

            document.getElementById('count-display').textContent = count;
            document.getElementById('preview-text').innerHTML =
                `将创建 <strong>${count}</strong> 个 <strong>${type}</strong> 类型的许可证<br>` +
                (prefix ? `用户名格式: ${prefix}1, ${prefix}2, ..., ${prefix}${count}<br>` : '') +
                (company ? `公司名称: ${company}<br>` : '');
        }

        window.onload = function() {
            updatePreview();
            document.getElementById('count').addEventListener('input', updatePreview);
            document.getElementById('user_name_prefix').addEventListener('input', updatePreview);
            document.getElementById('company_name').addEventListener('input', updatePreview);
            document.getElementById('license_type').addEventListener('change', updatePreview);
        }
    </script>
</head>
<body>
    <div class="header">
        <h1>批量创建许可证</h1>
        <a href="{{ url_for('admin.logout') }}" class="logout">登出</a>
    </div>
    <div class="container">
        <div class="nav">
            <a href="{{ url_for('admin.dashboard') }}">首页</a>
            <a href="{{ url_for('admin.list_licenses') }}">许可证管理</a>
            <a href="{{ url_for('admin.create_license') }}">创建许可证</a>
            <a href="{{ url_for('admin.batch_create_licenses') }}">批量创建</a>
        </div>

        <div class="form">
            <h2>🚀 批量创建许可证</h2>

            <div class="info">
                <strong>批量创建说明：</strong>
                <ul>
                    <li>一次最多可创建100个许可证</li>
                    <li>所有许可证将使用相同的配置参数</li>
                    <li>用户名将自动添加序号后缀（如：用户1, 用户2...）</li>
                    <li>每个许可证都有唯一的密钥</li>
                </ul>
            </div>

            <form method="post">
                <div class="form-row">
                    <div class="form-group">
                        <label>创建数量 (1-100):</label>
                        <input type="number" id="count" name="count" value="10" min="1" max="100" required>
                    </div>

                    <div class="form-group">
                        <label>许可证类型:</label>
                        <select id="license_type" name="license_type">
                            <option value="standard">标准版</option>
                            <option value="premium">高级版</option>
                            <option value="enterprise">企业版</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>最大激活数:</label>
                        <input type="number" name="max_activations" value="1" min="1" max="100">
                    </div>

                    <div class="form-group">
                        <label>有效期（天数，留空表示永久）:</label>
                        <input type="number" name="expires_days" min="1" max="3650" placeholder="例如：365">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>用户名前缀（可选）:</label>
                        <input type="text" id="user_name_prefix" name="user_name_prefix" placeholder="例如：测试用户">
                        <small style="color: #666;">将自动添加序号，如：测试用户1, 测试用户2...</small>
                    </div>

                    <div class="form-group">
                        <label>公司名称（可选）:</label>
                        <input type="text" id="company_name" name="company_name" placeholder="例如：测试公司">
                    </div>
                </div>

                <div class="form-group">
                    <label>功能权限:</label>
                    <div class="checkbox-group">
                        <label><input type="checkbox" name="feature_email_management" checked> 邮件管理</label>
                        <label><input type="checkbox" name="feature_batch_import"> 批量导入</label>
                        <label><input type="checkbox" name="feature_advanced_search"> 高级搜索</label>
                    </div>
                </div>

                <div class="form-group">
                    <label>备注前缀（可选）:</label>
                    <input type="text" name="notes_prefix" placeholder="例如：批量测试">
                    <small style="color: #666;">将自动添加序号，如：批量测试 #1, 批量测试 #2...</small>
                </div>

                <div class="preview">
                    <h3>创建预览</h3>
                    <div class="count-display" id="count-display">10</div>
                    <div id="preview-text"></div>
                </div>

                <button type="submit" onclick="return confirm('确定要创建这些许可证吗？')">开始批量创建</button>

                {% if error %}
                    <div class="error">{{ error }}</div>
                {% endif %}
            </form>
        </div>
    </div>
</body>
</html>
'''

BATCH_CREATE_SUCCESS_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>批量创建成功</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 0; background: #f5f5f5; }
        .header { background: #007cba; color: white; padding: 15px 20px; }
        .header h1 { margin: 0; display: inline-block; }
        .header .logout { float: right; color: white; text-decoration: none; }
        .container { padding: 20px; }
        .nav { margin-bottom: 20px; }
        .nav a { display: inline-block; padding: 10px 20px; background: #007cba; color: white; text-decoration: none; margin-right: 10px; border-radius: 3px; }
        .nav a:hover { background: #005a87; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .stats { display: flex; gap: 20px; margin-bottom: 20px; }
        .stat-card { background: white; padding: 20px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); flex: 1; text-align: center; }
        .stat-number { font-size: 2em; font-weight: bold; color: #28a745; }
        .stat-label { color: #666; margin-top: 5px; }
        table { width: 100%; background: white; border-collapse: collapse; margin-bottom: 20px; }
        th, td { padding: 8px; text-align: left; border: 1px solid #ddd; font-size: 14px; }
        th { background: #f0f0f0; }
        .license-key { font-family: monospace; background: #f8f9fa; padding: 2px 4px; border-radius: 3px; font-size: 12px; }
        .actions { margin-top: 20px; }
        .btn { display: inline-block; padding: 10px 20px; text-decoration: none; border-radius: 3px; margin-right: 10px; }
        .btn-primary { background: #007cba; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .export-section { background: white; padding: 20px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); margin-bottom: 20px; }
    </style>
    <script>
        function exportToCSV() {
            const table = document.getElementById('licenses-table');
            let csv = [];

            // 添加表头
            const headers = [];
            table.querySelectorAll('thead th').forEach(th => {
                headers.push(th.textContent.trim());
            });
            csv.push(headers.join(','));

            // 添加数据行
            table.querySelectorAll('tbody tr').forEach(tr => {
                const row = [];
                tr.querySelectorAll('td').forEach(td => {
                    let text = td.textContent.trim();
                    // 处理包含逗号的字段
                    if (text.includes(',')) {
                        text = '"' + text + '"';
                    }
                    row.push(text);
                });
                csv.push(row.join(','));
            });

            // 下载CSV文件
            const csvContent = csv.join('\\n');
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', 'batch_licenses_' + new Date().toISOString().slice(0,10) + '.csv');
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        function copyAllKeys() {
            const keys = [];
            document.querySelectorAll('.license-key').forEach(el => {
                keys.push(el.textContent);
            });

            navigator.clipboard.writeText(keys.join('\\n')).then(() => {
                alert('所有许可证密钥已复制到剪贴板！');
            }).catch(() => {
                // 备用方法
                const textarea = document.createElement('textarea');
                textarea.value = keys.join('\\n');
                document.body.appendChild(textarea);
                textarea.select();
                document.execCommand('copy');
                document.body.removeChild(textarea);
                alert('所有许可证密钥已复制到剪贴板！');
            });
        }
    </script>
</head>
<body>
    <div class="header">
        <h1>批量创建成功</h1>
        <a href="{{ url_for('admin.logout') }}" class="logout">登出</a>
    </div>
    <div class="container">
        <div class="nav">
            <a href="{{ url_for('admin.dashboard') }}">首页</a>
            <a href="{{ url_for('admin.list_licenses') }}">许可证管理</a>
            <a href="{{ url_for('admin.create_license') }}">创建许可证</a>
            <a href="{{ url_for('admin.batch_create_licenses') }}">批量创建</a>
        </div>

        <div class="success">
            <h2>🎉 批量创建成功！</h2>
            <p>已成功创建 <strong>{{ count }}</strong> 个许可证，所有许可证都已激活并可以使用。</p>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">{{ count }}</div>
                <div class="stat-label">新创建许可证</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ licenses|length }}</div>
                <div class="stat-label">成功生成</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">0</div>
                <div class="stat-label">创建失败</div>
            </div>
        </div>

        <div class="export-section">
            <h3>📋 导出和复制</h3>
            <p>您可以导出许可证列表或复制所有密钥：</p>
            <button onclick="exportToCSV()" class="btn btn-success">📄 导出为CSV</button>
            <button onclick="copyAllKeys()" class="btn btn-secondary">📋 复制所有密钥</button>
        </div>

        <h3>📋 新创建的许可证列表</h3>
        <table id="licenses-table">
            <thead>
                <tr>
                    <th>序号</th>
                    <th>许可证密钥</th>
                    <th>类型</th>
                    <th>用户名</th>
                    <th>最大激活数</th>
                    <th>过期时间</th>
                    <th>备注</th>
                </tr>
            </thead>
            <tbody>
                {% for license in licenses %}
                <tr>
                    <td>{{ loop.index }}</td>
                    <td><span class="license-key">{{ license.license_key }}</span></td>
                    <td>{{ license.license_type }}</td>
                    <td>{{ license.user_name or '未设置' }}</td>
                    <td>{{ license.max_activations }}</td>
                    <td>{{ license.expires_at.strftime('%Y-%m-%d %H:%M') if license.expires_at else '永久' }}</td>
                    <td>{{ license.notes or '' }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <div class="actions">
            <a href="{{ url_for('admin.list_licenses') }}" class="btn btn-primary">📋 查看所有许可证</a>
            <a href="{{ url_for('admin.batch_create_licenses') }}" class="btn btn-secondary">🔄 再次批量创建</a>
            <a href="{{ url_for('admin.dashboard') }}" class="btn btn-secondary">🏠 返回首页</a>
        </div>
    </div>
</body>
</html>
'''
