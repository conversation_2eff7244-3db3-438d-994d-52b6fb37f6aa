#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
企业邮件管理系统启动器
检查依赖并启动PySide6版本的邮件管理系统
"""

import sys
import os
import logging
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("错误: 需要Python 3.8或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    return True

def check_dependencies():
    """检查依赖模块"""
    required_modules = {
        'PySide6': 'PySide6>=6.6.0',
        'requests': 'requests>=2.25.0',
        'PIL': 'Pillow>=10.0.0',  # Pillow包在Python中用PIL导入
        'psutil': 'psutil>=5.8.0'
    }

    missing = []
    for module, package in required_modules.items():
        try:
            __import__(module)
            print(f"✓ {package.split('>=')[0]} - 已安装")
        except ImportError:
            missing.append(package)
            print(f"✗ {package.split('>=')[0]} - 未安装")

    if missing:
        print("\n缺少以下依赖模块:")
        for package in missing:
            print(f"  - {package}")
        print("\n请运行以下命令安装:")
        print(f"pip install {' '.join(missing)}")
        return False

    return True

def check_core_modules():
    """检查核心业务模块"""
    core_modules = [
        ('core.production_optimized_v2', 'core/production_optimized_v2.py'),
        ('core.multi_account_manager', 'core/multi_account_manager.py'),
        ('core.email_database', 'core/email_database.py'),
        ('utils.email_html_converter', 'utils/email_html_converter.py')
    ]

    missing = []
    for module_name, file_path in core_modules:
        try:
            __import__(module_name)
            print(f"✓ {module_name} - 已找到")
        except ImportError:
            missing.append(file_path)
            print(f"✗ {module_name} - 未找到")

    if missing:
        print("\n缺少以下核心模块:")
        for file_path in missing:
            print(f"  - {file_path}")
        print("\n请确保所有核心模块文件都在正确的目录中")
        return False

    return True

def setup_environment():
    """设置运行环境"""
    try:
        # 确保输出目录存在
        output_dir = Path("email_html_output")
        output_dir.mkdir(exist_ok=True)
        print(f"✓ 输出目录: {output_dir}")
        
        # 确保日志目录存在
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        print(f"✓ 日志目录: {log_dir}")
        
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('logs/startup.log', encoding='utf-8')
            ]
        )
        print("✓ 日志系统已配置")
        
        return True
        
    except Exception as e:
        print(f"✗ 环境设置失败: {e}")
        return False

def show_welcome():
    """显示欢迎信息"""
    print("=" * 70)
    print("🏢 企业邮件管理系统 - Professional Edition v2.0")
    print("=" * 70)
    print()
    print("🎯 核心特性:")
    print("  ✓ 基于PySide6的现代化企业级界面")
    print("  ✓ 多账户批量邮件获取和管理")
    print("  ✓ 专业的三栏式布局设计")
    print("  ✓ Outlook风格HTML邮件查看")
    print("  ✓ 高性能SQLite数据库存储")
    print("  ✓ 企业级安全和隐私保护")
    print()
    print("🔧 技术架构:")
    print("  • GUI框架: PySide6 (Qt6)")
    print("  • 数据库: SQLite")
    print("  • 邮件协议: IMAP + OAuth2")
    print("  • 认证方式: Microsoft Graph API")
    print()
    print("=" * 70)
    print()

def main():
    """主启动函数"""
    show_welcome()
    
    print("🔍 正在检查系统环境...")
    print()
    
    # 检查Python版本
    if not check_python_version():
        input("按Enter键退出...")
        return 1
    
    print()
    print("📦 正在检查依赖模块...")
    
    # 检查依赖
    if not check_dependencies():
        input("按Enter键退出...")
        return 1
    
    print()
    print("🔧 正在检查核心模块...")
    
    # 检查核心模块
    if not check_core_modules():
        input("按Enter键退出...")
        return 1
    
    print()
    print("⚙️ 正在设置运行环境...")
    
    # 设置环境
    if not setup_environment():
        input("按Enter键退出...")
        return 1
    
    print()
    print("🚀 正在启动企业邮件管理系统...")
    print()
    
    try:
        # 导入并启动主应用程序
        from enterprise_email_manager import main as run_app
        
        print("✓ 主应用程序已加载")
        print("✓ 正在启动GUI界面...")
        print()
        print("=" * 70)
        print("📧 企业邮件管理系统已启动！")
        print("=" * 70)
        
        # 运行应用程序
        return run_app()
        
    except ImportError as e:
        print(f"✗ 导入主应用程序失败: {e}")
        print("请确保 enterprise_email_manager.py 文件存在且正确")
        input("按Enter键退出...")
        return 1
        
    except Exception as e:
        print(f"✗ 启动失败: {e}")
        logging.error(f"应用启动失败: {e}")
        input("按Enter键退出...")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
