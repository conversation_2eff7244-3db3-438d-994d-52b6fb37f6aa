# 简化版编辑功能补丁
# 如果主要的admin.py有问题，可以用这个简单版本

@admin_bp.route('/licenses/<int:license_id>/edit_simple', methods=['GET', 'POST'])
@login_required
def edit_license_simple(license_id):
    """简化版编辑许可证"""
    license_obj = License.query.get_or_404(license_id)
    
    if request.method == 'POST':
        try:
            data = request.form
            
            # 只更新基本字段
            if 'status' in data:
                license_obj.status = data['status']
            if 'max_activations' in data:
                license_obj.max_activations = int(data['max_activations'])
            if 'user_name' in data:
                license_obj.user_name = data['user_name']
            if 'notes' in data:
                license_obj.notes = data['notes']
            
            db.session.commit()
            return redirect(url_for('admin.view_license', license_id=license_id))
            
        except Exception as e:
            db.session.rollback()
            return f"Error: {str(e)}"
    
    # 简单的编辑表单
    form_html = f'''
    <form method="post">
        <p>许可证: {license_obj.license_key}</p>
        <p>状态: 
            <select name="status">
                <option value="active" {"selected" if license_obj.status == "active" else ""}>激活</option>
                <option value="suspended" {"selected" if license_obj.status == "suspended" else ""}>暂停</option>
            </select>
        </p>
        <p>最大激活数: <input type="number" name="max_activations" value="{license_obj.max_activations}"></p>
        <p>用户名: <input type="text" name="user_name" value="{license_obj.user_name or ''}"></p>
        <p>备注: <textarea name="notes">{license_obj.notes or ''}</textarea></p>
        <button type="submit">保存</button>
        <a href="/admin/licenses/{license_id}">取消</a>
    </form>
    '''
    
    return form_html
