#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量真实账户导入器
支持导入特定格式的账户信息：email----password----client_id----refresh_token
"""

import sys
import re
import logging
from pathlib import Path
from typing import List, Dict, Any, Tuple
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTextEdit, QPushButton, 
    QLabel, QProgressBar, QMessageBox, QGroupBox, QCheckBox,
    QTableWidget, QTableWidgetItem, QHeaderView, QApplication,
    QFileDialog, QTabWidget, QWidget, QFormLayout, QLineEdit
)
from PySide6.QtCore import Qt, QThread, Signal
from PySide6.QtGui import QFont, QColor

class AccountParseThread(QThread):
    """账户解析线程"""
    
    progress_updated = Signal(int, str)  # 进度和消息
    account_parsed = Signal(dict)  # 解析的账户
    parsing_completed = Signal(int, int)  # 成功数量，失败数量
    
    def __init__(self, account_text: str):
        super().__init__()
        self.account_text = account_text
        self.logger = logging.getLogger(__name__)
    
    def run(self):
        """运行账户解析"""
        try:
            lines = self.account_text.strip().split('\n')
            total_lines = len(lines)
            success_count = 0
            failed_count = 0
            
            for i, line in enumerate(lines):
                line = line.strip()
                if not line:
                    continue
                
                self.progress_updated.emit(
                    int((i + 1) / total_lines * 100),
                    f"正在解析第 {i + 1} 行..."
                )
                
                # 解析账户信息
                account_info = self.parse_account_line(line)
                if account_info:
                    self.account_parsed.emit(account_info)
                    success_count += 1
                else:
                    failed_count += 1
                    self.logger.warning(f"解析失败: {line[:50]}...")
            
            self.parsing_completed.emit(success_count, failed_count)
            
        except Exception as e:
            self.logger.error(f"账户解析异常: {e}")
            self.parsing_completed.emit(0, 1)
    
    def parse_account_line(self, line: str) -> Dict[str, Any]:
        """解析单行账户信息"""
        try:
            # 支持的格式：email----password----client_id----refresh_token
            parts = line.split('----')
            
            if len(parts) >= 4:
                email = parts[0].strip()
                password = parts[1].strip()
                client_id = parts[2].strip()
                refresh_token = parts[3].strip()
                
                # 验证邮箱格式
                if '@' not in email or '.' not in email.split('@')[1]:
                    return None
                
                # 生成账户ID
                account_id = email.replace('@', '_').replace('.', '_')
                
                return {
                    'account_id': account_id,
                    'email': email,
                    'password': password,
                    'display_name': email.split('@')[0],
                    'imap_server': 'outlook.office365.com',
                    'imap_port': 993,
                    'smtp_server': 'smtp-mail.outlook.com',
                    'smtp_port': 587,
                    'use_ssl': True,
                    'client_id': client_id,
                    'refresh_token': refresh_token,
                    'enabled': True,
                    'priority': 1,
                    'max_retries': 3,
                    'timeout': 30,
                    'keep_alive': True,
                    'sync_interval': 5,
                    'max_emails': 1000,
                    'download_attachments': False
                }
            
            return None
            
        except Exception as e:
            self.logger.error(f"解析账户行失败: {e}")
            return None

class BatchRealAccountImporter(QDialog):
    """批量真实账户导入器"""

    # 添加信号
    import_completed = Signal(int, int)  # 成功数量，失败数量

    def __init__(self, parent=None, real_account_manager=None):
        super().__init__(parent)
        self.real_account_manager = real_account_manager
        self.logger = logging.getLogger(__name__)
        self.parsed_accounts = []
        
        self.setWindowTitle("批量导入真实邮件账户")
        self.setFixedSize(800, 600)
        self.setModal(True)
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        
        # 文本导入标签页
        self.setup_text_import_tab()
        
        # 文件导入标签页
        self.setup_file_import_tab()
        
        # 预览标签页
        self.setup_preview_tab()
        
        layout.addWidget(self.tab_widget)
        
        # 按钮区域
        self.setup_buttons(layout)
    
    def setup_text_import_tab(self):
        """设置文本导入标签页"""
        text_tab = QWidget()
        layout = QVBoxLayout(text_tab)
        
        # 说明信息
        info_group = QGroupBox("导入格式说明")
        info_layout = QVBoxLayout(info_group)
        
        info_text = """
<b>支持的账户格式：</b><br>
<code>邮箱地址----密码----客户端ID----刷新令牌</code><br><br>

<b>示例：</b><br>
<code><EMAIL>----password123----9e5f94bc-e8a4-4e73-b8be-63364c29d753----M.C520_SN1.0.U.-...</code><br><br>

<b>注意事项：</b><br>
• 每行一个账户<br>
• 使用四个连字符（----）分隔字段<br>
• 支持Outlook、Hotmail等Microsoft邮箱<br>
• 刷新令牌必须有效
        """
        
        info_label = QLabel(info_text)
        info_label.setWordWrap(True)
        info_label.setStyleSheet("QLabel { color: #333; font-size: 12px; }")
        info_layout.addWidget(info_label)
        
        layout.addWidget(info_group)
        
        # 输入区域
        input_group = QGroupBox("账户信息输入")
        input_layout = QVBoxLayout(input_group)
        
        self.account_text_edit = QTextEdit()
        self.account_text_edit.setPlaceholderText(
            "请粘贴账户信息，每行一个账户...\n"
            "格式：邮箱----密码----客户端ID----刷新令牌"
        )
        self.account_text_edit.setFont(QFont("Consolas", 10))
        input_layout.addWidget(self.account_text_edit)
        
        # 解析按钮
        parse_btn = QPushButton("🔍 解析账户信息")
        parse_btn.clicked.connect(self.parse_accounts)
        input_layout.addWidget(parse_btn)
        
        layout.addWidget(input_group)
        
        # 进度区域
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        self.status_label = QLabel("")
        layout.addWidget(self.status_label)
        
        self.tab_widget.addTab(text_tab, "文本导入")
    
    def setup_file_import_tab(self):
        """设置文件导入标签页"""
        file_tab = QWidget()
        layout = QVBoxLayout(file_tab)
        
        # 文件选择
        file_group = QGroupBox("文件导入")
        file_layout = QFormLayout(file_group)
        
        self.file_path_edit = QLineEdit()
        self.file_path_edit.setReadOnly(True)
        file_layout.addRow("文件路径:", self.file_path_edit)
        
        select_file_btn = QPushButton("📁 选择文件")
        select_file_btn.clicked.connect(self.select_file)
        file_layout.addRow("", select_file_btn)
        
        import_file_btn = QPushButton("📥 导入文件")
        import_file_btn.clicked.connect(self.import_from_file)
        file_layout.addRow("", import_file_btn)
        
        layout.addWidget(file_group)
        
        # 支持的文件格式说明
        format_group = QGroupBox("支持的文件格式")
        format_layout = QVBoxLayout(format_group)
        
        format_text = """
<b>支持的文件类型：</b><br>
• .txt - 纯文本文件<br>
• .csv - 逗号分隔值文件<br>
• .json - JSON格式文件<br><br>

<b>文件内容格式：</b><br>
• TXT: 每行一个账户，使用----分隔<br>
• CSV: email,password,client_id,refresh_token<br>
• JSON: 账户对象数组
        """
        
        format_label = QLabel(format_text)
        format_label.setWordWrap(True)
        format_label.setStyleSheet("QLabel { color: #333; font-size: 12px; }")
        format_layout.addWidget(format_label)
        
        layout.addWidget(format_group)
        
        self.tab_widget.addTab(file_tab, "文件导入")
    
    def setup_preview_tab(self):
        """设置预览标签页"""
        preview_tab = QWidget()
        layout = QVBoxLayout(preview_tab)
        
        # 预览表格
        preview_group = QGroupBox("解析结果预览")
        preview_layout = QVBoxLayout(preview_group)
        
        self.preview_table = QTableWidget()
        self.preview_table.setColumnCount(4)
        self.preview_table.setHorizontalHeaderLabels([
            "邮箱地址", "显示名称", "服务器", "状态"
        ])
        
        # 设置表格样式
        header = self.preview_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        
        preview_layout.addWidget(self.preview_table)
        
        # 统计信息
        self.stats_label = QLabel("等待解析账户信息...")
        preview_layout.addWidget(self.stats_label)
        
        layout.addWidget(preview_group)
        
        # 导入选项
        options_group = QGroupBox("导入选项")
        options_layout = QVBoxLayout(options_group)
        
        self.auto_enable_check = QCheckBox("自动启用导入的账户")
        self.auto_enable_check.setChecked(True)
        options_layout.addWidget(self.auto_enable_check)
        
        self.test_connection_check = QCheckBox("导入前测试连接")
        self.test_connection_check.setChecked(False)
        options_layout.addWidget(self.test_connection_check)
        
        layout.addWidget(options_group)
        
        self.tab_widget.addTab(preview_tab, "预览导入")
    
    def setup_buttons(self, layout):
        """设置按钮"""
        button_layout = QHBoxLayout()
        
        self.import_btn = QPushButton("📥 导入账户")
        self.import_btn.clicked.connect(self.import_accounts)
        self.import_btn.setEnabled(False)
        button_layout.addWidget(self.import_btn)
        
        self.clear_btn = QPushButton("🗑️ 清空")
        self.clear_btn.clicked.connect(self.clear_all)
        button_layout.addWidget(self.clear_btn)
        
        button_layout.addStretch()
        
        self.close_btn = QPushButton("❌ 关闭")
        self.close_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)
    
    def parse_accounts(self):
        """解析账户信息"""
        try:
            account_text = self.account_text_edit.toPlainText().strip()
            if not account_text:
                QMessageBox.warning(self, "警告", "请输入账户信息")
                return
            
            # 清空之前的结果
            self.parsed_accounts.clear()
            self.preview_table.setRowCount(0)
            
            # 显示进度条
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            self.status_label.setText("正在解析账户信息...")
            
            # 创建解析线程
            self.parse_thread = AccountParseThread(account_text)
            self.parse_thread.progress_updated.connect(self.on_parse_progress)
            self.parse_thread.account_parsed.connect(self.on_account_parsed)
            self.parse_thread.parsing_completed.connect(self.on_parsing_completed)
            self.parse_thread.start()
            
        except Exception as e:
            self.logger.error(f"解析账户失败: {e}")
            QMessageBox.critical(self, "错误", f"解析账户失败：\n{str(e)}")
    
    def on_parse_progress(self, progress, message):
        """解析进度更新"""
        self.progress_bar.setValue(progress)
        self.status_label.setText(message)
    
    def on_account_parsed(self, account_info):
        """账户解析完成"""
        self.parsed_accounts.append(account_info)
        self.add_account_to_preview(account_info)
    
    def on_parsing_completed(self, success_count, failed_count):
        """解析完成"""
        self.progress_bar.setVisible(False)
        
        total_count = success_count + failed_count
        self.status_label.setText(
            f"解析完成：成功 {success_count} 个，失败 {failed_count} 个"
        )
        
        self.stats_label.setText(
            f"共解析 {len(self.parsed_accounts)} 个有效账户"
        )
        
        # 启用导入按钮
        self.import_btn.setEnabled(len(self.parsed_accounts) > 0)
        
        # 切换到预览标签页
        self.tab_widget.setCurrentIndex(2)
    
    def add_account_to_preview(self, account_info):
        """添加账户到预览表格"""
        row = self.preview_table.rowCount()
        self.preview_table.insertRow(row)
        
        # 邮箱地址
        email_item = QTableWidgetItem(account_info['email'])
        self.preview_table.setItem(row, 0, email_item)
        
        # 显示名称
        display_name_item = QTableWidgetItem(account_info['display_name'])
        self.preview_table.setItem(row, 1, display_name_item)
        
        # 服务器
        server_item = QTableWidgetItem(f"{account_info['imap_server']}:{account_info['imap_port']}")
        self.preview_table.setItem(row, 2, server_item)
        
        # 状态
        status_item = QTableWidgetItem("✅ 已解析")
        status_item.setForeground(QColor("#008000"))
        self.preview_table.setItem(row, 3, status_item)
    
    def select_file(self):
        """选择导入文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择账户文件",
            "",
            "文本文件 (*.txt);;CSV文件 (*.csv);;JSON文件 (*.json);;所有文件 (*.*)"
        )
        
        if file_path:
            self.file_path_edit.setText(file_path)
    
    def import_from_file(self):
        """从文件导入"""
        file_path = self.file_path_edit.text().strip()
        if not file_path:
            QMessageBox.warning(self, "警告", "请先选择文件")
            return
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            self.account_text_edit.setPlainText(content)
            self.parse_accounts()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"读取文件失败：\n{str(e)}")
    
    def start_import(self):
        """开始导入（兼容enhanced_batch_import_dialog调用）"""
        self.import_accounts()

    def import_accounts(self):
        """导入账户"""
        if not self.parsed_accounts:
            # 发送信号表示没有账户导入
            self.import_completed.emit(0, 0)
            QMessageBox.warning(self, "警告", "没有可导入的账户")
            return

        if not self.real_account_manager:
            # 发送信号表示导入失败
            self.import_completed.emit(0, len(self.parsed_accounts))
            QMessageBox.critical(self, "错误", "真实账户管理器未初始化")
            return
        
        try:
            success_count = 0
            failed_count = 0
            
            for account_info in self.parsed_accounts:
                try:
                    # 设置启用状态
                    account_info['enabled'] = self.auto_enable_check.isChecked()
                    
                    # 添加账户
                    success = self.real_account_manager.add_account(account_info)
                    if success:
                        success_count += 1
                    else:
                        failed_count += 1
                        
                except Exception as e:
                    self.logger.error(f"导入账户 {account_info['email']} 失败: {e}")
                    failed_count += 1
            
            # 发送导入完成信号
            self.logger.info(f"发送导入完成信号: 成功={success_count}, 失败={failed_count}")
            self.import_completed.emit(success_count, failed_count)

            # 显示结果
            QMessageBox.information(
                self,
                "导入完成",
                f"账户导入完成！\n"
                f"成功：{success_count} 个\n"
                f"失败：{failed_count} 个"
            )

            if success_count > 0:
                self.accept()  # 关闭对话框
            
        except Exception as e:
            self.logger.error(f"导入账户失败: {e}")
            # 发送信号表示导入失败
            self.import_completed.emit(0, len(self.parsed_accounts) if self.parsed_accounts else 1)
            QMessageBox.critical(self, "错误", f"导入账户失败：\n{str(e)}")
    
    def clear_all(self):
        """清空所有内容"""
        self.account_text_edit.clear()
        self.parsed_accounts.clear()
        self.preview_table.setRowCount(0)
        self.status_label.setText("")
        self.stats_label.setText("等待解析账户信息...")
        self.import_btn.setEnabled(False)
        self.progress_bar.setVisible(False)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    dialog = BatchRealAccountImporter()
    dialog.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
