#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的连接测试脚本
"""

import requests
import json

def test_direct_connection():
    """直接测试连接"""
    print("🔗 直接测试HTTPS连接...")
    
    try:
        # 测试ping端点
        url = "https://ka.915277.xyz/api/v1/ping"
        print(f"请求URL: {url}")
        
        response = requests.get(url, verify=False, timeout=10)
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ ping测试成功")
            return True
        else:
            print("❌ ping测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 连接异常: {e}")
        return False

def test_license_validation():
    """测试许可证验证"""
    print("\n🔍 测试许可证验证...")
    
    try:
        url = "https://ka.915277.xyz/api/v1/validate"
        data = {"license_key": "GEAY-AMUY-8QBQ-D1Q5"}
        
        print(f"请求URL: {url}")
        print(f"请求数据: {data}")
        
        response = requests.post(
            url, 
            json=data, 
            verify=False, 
            timeout=10,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ 许可证验证成功")
                return True
            else:
                print("❌ 许可证验证失败")
                return False
        else:
            print("❌ 请求失败")
            return False
            
    except Exception as e:
        print(f"❌ 验证异常: {e}")
        return False

if __name__ == "__main__":
    print("🧪 简单连接测试")
    print("=" * 40)
    
    # 禁用SSL警告
    import urllib3
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    
    ping_ok = test_direct_connection()
    validate_ok = test_license_validation()
    
    print("\n" + "=" * 40)
    print("📊 测试结果:")
    print(f"  Ping测试: {'✅ 成功' if ping_ok else '❌ 失败'}")
    print(f"  验证测试: {'✅ 成功' if validate_ok else '❌ 失败'}")
    
    if ping_ok and validate_ok:
        print("🎉 所有测试通过！")
    else:
        print("⚠️  部分测试失败")
