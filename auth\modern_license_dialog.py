#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现代化许可证授权对话框
基于HTML设计风格的专业级授权验证面板
"""

import sys
import re
from typing import Optional, Dict, Any
from datetime import datetime

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton,
    QTextEdit, QProgressBar, QFrame, QGraphicsDropShadowEffect,
    QApplication, QWidget, QSpacerItem, QSizePolicy
)
from PySide6.QtCore import Qt, QThread, Signal, QTimer, QPropertyAnimation, QEasingCurve, QRect
from PySide6.QtGui import QFont, QPalette, QColor, QPainter, QLinearGradient, QBrush

from .license_manager import LicenseManager
from .license_success_dialog import LicenseSuccessDialog


class ModernLicenseDialog(QDialog):
    """现代化许可证授权对话框"""
    
    def __init__(self, parent=None, license_manager: LicenseManager = None):
        super().__init__(parent)
        self.license_manager = license_manager or LicenseManager()
        self.validation_thread = None
        self.drag_position = None
        
        # 设置窗口属性
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.Dialog)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setFixedSize(480, 520)  # 减小高度从640到520
        self.setModal(True)
        
        # 初始化UI
        self._setup_ui()
        self._setup_connections()
        self._setup_animations()
        
        # 自动测试连接
        QTimer.singleShot(1000, self._auto_test_connection)
    
    def _setup_ui(self):
        """设置用户界面"""
        # 主容器
        self.main_container = QFrame(self)
        self.main_container.setGeometry(20, 20, 440, 480)  # 减小高度从600到480
        self.main_container.setObjectName("mainContainer")
        
        # 设置现代化样式
        self.setStyleSheet(self._get_modern_stylesheet())
        
        # 创建布局
        layout = QVBoxLayout(self.main_container)
        layout.setContentsMargins(25, 25, 25, 25)  # 进一步减小边距
        layout.setSpacing(16)  # 进一步减小间距
        
        # 创建各个组件
        self._create_header(layout)
        self._create_license_input(layout)
        self._create_status_area(layout)
        self._create_buttons(layout)
        
        # 添加阴影效果
        self._add_shadow_effect()
    
    def _get_modern_stylesheet(self):
        """获取现代化样式表"""
        return """
            QDialog {
                background: transparent;
            }
            
            #mainContainer {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(102, 126, 234, 0.9),
                    stop:0.5 rgba(118, 75, 162, 0.9),
                    stop:1 rgba(240, 147, 251, 0.9));
                border-radius: 24px;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }
            
            #titleLabel {
                color: white;
                font-size: 24px;
                font-weight: 700;
                font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
                margin-bottom: 4px;
            }

            #subtitleLabel {
                color: rgba(255, 255, 255, 0.8);
                font-size: 13px;
                font-weight: 400;
                margin-bottom: 12px;
            }
            
            #licenseInput {
                background: rgba(255, 255, 255, 0.15);
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-radius: 14px;
                padding: 12px 16px;
                font-size: 14px;
                font-weight: 500;
                color: white;
                font-family: 'Consolas', 'Monaco', monospace;
                letter-spacing: 1.5px;
                min-height: 20px;
                max-height: 44px;
            }
            
            #licenseInput:focus {
                border: 2px solid rgba(255, 255, 255, 0.6);
                background: rgba(255, 255, 255, 0.2);
                outline: none;
            }
            
            #licenseInput::placeholder {
                color: rgba(255, 255, 255, 0.5);
                font-style: italic;
            }
            
            #statusContainer {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 16px;
                border: 1px solid rgba(255, 255, 255, 0.15);
                padding: 16px;
                min-height: 100px;
            }
            
            #connectionStatus {
                color: rgba(255, 255, 255, 0.9);
                font-size: 13px;
                font-weight: 500;
                padding: 8px 16px;
                border-radius: 20px;
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
            }
            
            #connectionStatus[status="success"] {
                background: rgba(40, 167, 69, 0.3);
                border-color: rgba(40, 167, 69, 0.5);
                color: #d4edda;
            }
            
            #connectionStatus[status="error"] {
                background: rgba(220, 53, 69, 0.3);
                border-color: rgba(220, 53, 69, 0.5);
                color: #f8d7da;
            }
            
            #connectionStatus[status="loading"] {
                background: rgba(0, 123, 255, 0.3);
                border-color: rgba(0, 123, 255, 0.5);
                color: #cce7ff;
            }
            
            #resultArea {
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 12px;
                padding: 12px;
                color: rgba(255, 255, 255, 0.8);
                font-size: 11px;
                font-family: 'Consolas', 'Monaco', monospace;
                min-height: 60px;
                max-height: 80px;
            }
            
            QPushButton {
                border: none;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 11px;
                font-weight: 600;
                font-family: 'Microsoft YaHei', sans-serif;
                min-width: 70px;
                max-width: 100px;
                max-height: 32px;
            }
            
            #validateBtn {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4facfe, stop:1 #00f2fe);
                color: white;
                max-width: 90px;
            }
            
            #validateBtn:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3d8bfe, stop:1 #00d4fe);
                transform: translateY(-2px);
            }
            
            #validateBtn:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2c6fce, stop:1 #00b8ce);
            }
            
            #validateBtn:disabled {
                background: rgba(255, 255, 255, 0.2);
                color: rgba(255, 255, 255, 0.5);
            }
            
            #testBtn {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #a8edea, stop:1 #fed6e3);
                color: #2c3e50;
                max-width: 90px;
            }
            
            #testBtn:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #96e6e2, stop:1 #fcc9d9);
            }
            
            #cancelBtn {
                background: rgba(255, 255, 255, 0.15);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.3);
                max-width: 70px;
            }
            
            #cancelBtn:hover {
                background: rgba(255, 255, 255, 0.25);
                border-color: rgba(255, 255, 255, 0.5);
            }
            
            #closeBtn {
                background: rgba(255, 255, 255, 0.1);
                color: rgba(255, 255, 255, 0.7);
                border: none;
                border-radius: 10px;
                width: 20px;
                height: 20px;
                font-size: 16px;
                font-weight: bold;
            }
            
            #closeBtn:hover {
                background: rgba(255, 255, 255, 0.2);
                color: white;
            }
            
            QProgressBar {
                border: none;
                border-radius: 8px;
                background: rgba(255, 255, 255, 0.1);
                height: 6px;
            }
            
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4facfe, stop:1 #00f2fe);
                border-radius: 8px;
            }
        """
    
    def _create_header(self, layout):
        """创建标题区域"""
        # 标题栏容器
        header_container = QFrame()
        header_layout = QHBoxLayout(header_container)
        header_layout.setContentsMargins(0, 0, 0, 0)

        # 标题区域
        title_area = QFrame()
        title_layout = QVBoxLayout(title_area)
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(2)  # 减小间距从4到2
        
        # 主标题
        self.title_label = QLabel("🔐 微软Ou工具验证")
        self.title_label.setObjectName("titleLabel")
        self.title_label.setAlignment(Qt.AlignLeft)

        # 副标题
        self.subtitle_label = QLabel("微软邮件管理系统")
        self.subtitle_label.setObjectName("subtitleLabel")
        self.subtitle_label.setAlignment(Qt.AlignLeft)
        
        title_layout.addWidget(self.title_label)
        title_layout.addWidget(self.subtitle_label)
        
        # 关闭按钮
        self.close_btn = QPushButton("×")
        self.close_btn.setObjectName("closeBtn")
        self.close_btn.setFixedSize(20, 20)  # 减小关闭按钮框的尺寸
        
        header_layout.addWidget(title_area)
        header_layout.addStretch()
        header_layout.addWidget(self.close_btn, 0, Qt.AlignTop | Qt.AlignRight)
        
        layout.addWidget(header_container)
    
    def _create_license_input(self, layout):
        """创建许可证输入区域"""
        # 许可证输入框
        self.license_input = QLineEdit()
        self.license_input.setObjectName("licenseInput")
        self.license_input.setPlaceholderText("请输入微软Ou工具密钥 (XXXX-XXXX-XXXX-XXXX)")
        self.license_input.setMaxLength(19)  # 包含连字符
        
        layout.addWidget(self.license_input)
    
    def _create_status_area(self, layout):
        """创建状态显示区域"""
        # 状态容器
        status_container = QFrame()
        status_container.setObjectName("statusContainer")
        status_layout = QVBoxLayout(status_container)
        status_layout.setSpacing(12)  # 减小间距从16到12
        status_layout.setContentsMargins(0, 0, 0, 0)
        
        # 连接状态
        self.connection_status = QLabel("🔄 正在检查连接状态...")
        self.connection_status.setObjectName("connectionStatus")
        self.connection_status.setAlignment(Qt.AlignCenter)
        
        # 结果显示区域
        self.result_area = QTextEdit()
        self.result_area.setObjectName("resultArea")
        self.result_area.setReadOnly(True)
        self.result_area.setPlainText("等待验证...")
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setRange(0, 100)
        
        status_layout.addWidget(self.connection_status)
        status_layout.addWidget(self.result_area)
        status_layout.addWidget(self.progress_bar)
        
        layout.addWidget(status_container)
    
    def _create_buttons(self, layout):
        """创建按钮区域"""
        button_layout = QHBoxLayout()
        button_layout.setSpacing(8)  # 进一步减小按钮间距防止重叠
        button_layout.setContentsMargins(0, 0, 0, 0)
        
        # 测试连接按钮
        self.test_btn = QPushButton("测试连接")
        self.test_btn.setObjectName("testBtn")

        # 验证按钮
        self.validate_btn = QPushButton("验证密钥")
        self.validate_btn.setObjectName("validateBtn")
        self.validate_btn.setEnabled(False)

        # 取消按钮
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.setObjectName("cancelBtn")
        
        # 添加弹性空间确保按钮居中分布
        button_layout.addStretch(1)
        button_layout.addWidget(self.test_btn)
        button_layout.addSpacing(6)  # 减小间距
        button_layout.addWidget(self.validate_btn)
        button_layout.addSpacing(6)  # 减小间距
        button_layout.addWidget(self.cancel_btn)
        button_layout.addStretch(1)
        
        layout.addLayout(button_layout)

    def _add_shadow_effect(self):
        """添加阴影效果"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(30)
        shadow.setColor(QColor(0, 0, 0, 80))
        shadow.setOffset(0, 10)
        self.main_container.setGraphicsEffect(shadow)

    def _setup_connections(self):
        """设置信号连接"""
        self.close_btn.clicked.connect(self.reject)
        self.cancel_btn.clicked.connect(self.reject)
        self.test_btn.clicked.connect(self._test_connection)
        self.validate_btn.clicked.connect(self._validate_license)
        self.license_input.textChanged.connect(self._on_license_text_changed)
        self.license_input.returnPressed.connect(self._validate_license)

    def _setup_animations(self):
        """设置动画效果"""
        # 窗口出现动画
        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(300)
        self.fade_animation.setStartValue(0.0)
        self.fade_animation.setEndValue(1.0)
        self.fade_animation.setEasingCurve(QEasingCurve.OutCubic)

        # 容器缩放动画
        self.scale_animation = QPropertyAnimation(self.main_container, b"geometry")
        self.scale_animation.setDuration(300)
        self.scale_animation.setEasingCurve(QEasingCurve.OutBack)

        # 启动动画
        QTimer.singleShot(50, self._start_entrance_animation)

    def _start_entrance_animation(self):
        """启动入场动画"""
        # 设置初始状态
        start_rect = QRect(40, 40, 400, 440)  # 调整高度
        end_rect = QRect(20, 20, 440, 480)    # 调整高度

        self.main_container.setGeometry(start_rect)
        self.setWindowOpacity(0.0)

        # 启动动画
        self.scale_animation.setStartValue(start_rect)
        self.scale_animation.setEndValue(end_rect)

        self.fade_animation.start()
        self.scale_animation.start()

    def _on_license_text_changed(self, text):
        """许可证文本改变处理"""
        # 格式化输入（自动添加连字符）
        formatted_text = self._format_license_key(text)
        if formatted_text != text:
            cursor_pos = self.license_input.cursorPosition()
            self.license_input.setText(formatted_text)
            # 调整光标位置
            new_pos = min(cursor_pos + (len(formatted_text) - len(text)), len(formatted_text))
            self.license_input.setCursorPosition(new_pos)

        # 启用/禁用验证按钮
        clean_text = text.replace('-', '').strip()
        self.validate_btn.setEnabled(len(clean_text) >= 16)

    def _format_license_key(self, text):
        """格式化许可证密钥"""
        # 移除所有非字母数字字符
        clean_text = re.sub(r'[^A-Za-z0-9]', '', text.upper())

        # 限制长度
        clean_text = clean_text[:16]

        # 添加连字符
        formatted = ''
        for i, char in enumerate(clean_text):
            if i > 0 and i % 4 == 0:
                formatted += '-'
            formatted += char

        return formatted

    def _auto_test_connection(self):
        """自动测试连接"""
        self._update_connection_status("loading", "🔄 正在检查服务器连接...")

        success, message = self.license_manager.test_connection()

        if success:
            self._update_connection_status("success", "✅ 服务器连接正常")
        else:
            self._update_connection_status("error", f"❌ 连接失败: {message}")

    def _test_connection(self):
        """手动测试连接"""
        self._update_connection_status("loading", "🔄 正在测试连接...")

        success, message = self.license_manager.test_connection()

        if success:
            self._update_connection_status("success", "✅ 连接测试成功")
            self.result_area.setPlainText(f"连接测试成功\n服务器响应正常\n时间: {datetime.now().strftime('%H:%M:%S')}")
        else:
            self._update_connection_status("error", f"❌ 连接测试失败")
            self.result_area.setPlainText(f"连接测试失败\n错误信息: {message}\n时间: {datetime.now().strftime('%H:%M:%S')}")

    def _update_connection_status(self, status, text):
        """更新连接状态"""
        self.connection_status.setText(text)
        self.connection_status.setProperty("status", status)
        self.connection_status.style().unpolish(self.connection_status)
        self.connection_status.style().polish(self.connection_status)

    def _validate_license(self):
        """验证许可证"""
        license_key = self.license_input.text().strip()
        if not license_key:
            return

        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # 禁用按钮
        self.validate_btn.setEnabled(False)
        self.test_btn.setEnabled(False)

        # 更新状态
        self.result_area.setPlainText("正在验证密钥，请稍候...")

        # 启动验证线程
        self.validation_thread = LicenseValidationThread(
            self.license_manager, license_key
        )
        self.validation_thread.validation_completed.connect(self._on_validation_completed)
        self.validation_thread.progress_updated.connect(self._on_progress_updated)
        self.validation_thread.start()

    def _on_progress_updated(self, value, message):
        """进度更新处理"""
        self.progress_bar.setValue(value)
        if message:
            self.result_area.setPlainText(message)

    def _on_validation_completed(self, success, message, data):
        """验证完成处理"""
        # 隐藏进度条
        self.progress_bar.setVisible(False)

        # 启用按钮
        self.validate_btn.setEnabled(True)
        self.test_btn.setEnabled(True)

        if success:
            # 验证成功
            result_text = f"✅ 密钥验证成功\n\n"
            result_text += f"状态: {data.get('status', 'N/A')}\n"
            result_text += f"到期时间: {data.get('expiry_time', 'N/A')}\n"
            result_text += f"验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

            self.result_area.setPlainText(result_text)
            self._update_connection_status("success", "✅ 密钥验证成功")

            # 显示成功界面
            QTimer.singleShot(1500, lambda: self._show_success_dialog(data))
        else:
            # 验证失败
            result_text = f"❌ 密钥验证失败\n\n"
            result_text += f"错误信息: {message}\n"
            result_text += f"验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
            result_text += "请检查:\n"
            result_text += "• 密钥是否正确\n"
            result_text += "• 网络连接是否正常\n"
            result_text += "• 密钥是否已过期"

            self.result_area.setPlainText(result_text)
            self._update_connection_status("error", "❌ 密钥验证失败")

    def _show_success_dialog(self, license_data):
        """显示授权成功对话框"""
        try:
            # 隐藏当前对话框
            self.hide()

            # 创建并显示成功对话框
            success_dialog = LicenseSuccessDialog(license_data, self.parent())
            success_dialog.start_application.connect(self._on_start_application)

            result = success_dialog.exec()

            # 如果用户没有选择开始使用，则关闭当前对话框
            if result == QDialog.Accepted:
                self.accept()
            else:
                # 如果用户取消了成功对话框，重新显示当前对话框
                self.show()

        except Exception as e:
            print(f"显示成功对话框时出错: {e}")
            # 出错时直接关闭当前对话框
            self.accept()

    def _on_start_application(self):
        """开始使用应用程序"""
        # 这里可以添加启动主应用程序的逻辑
        print("用户选择开始使用应用程序")
        self.accept()

    # 窗口拖动功能
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            event.accept()

    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if event.buttons() == Qt.LeftButton and self.drag_position:
            self.move(event.globalPosition().toPoint() - self.drag_position)
            event.accept()

    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        self.drag_position = None


class LicenseValidationThread(QThread):
    """许可证验证线程"""

    validation_completed = Signal(bool, str, dict)
    progress_updated = Signal(int, str)

    def __init__(self, license_manager: LicenseManager, license_key: str):
        super().__init__()
        self.license_manager = license_manager
        self.license_key = license_key

    def run(self):
        """执行验证"""
        try:
            self.progress_updated.emit(20, "正在连接验证服务器...")

            # 测试连接
            connection_ok, connection_msg = self.license_manager.test_connection()
            if not connection_ok:
                self.validation_completed.emit(False, f"连接失败: {connection_msg}", {})
                return

            self.progress_updated.emit(50, "正在验证密钥...")

            # 验证许可证
            success, message, data = self.license_manager.validate_license_key(
                self.license_key, force_online=True
            )

            self.progress_updated.emit(100, "验证完成")
            self.validation_completed.emit(success, message, data or {})

        except Exception as e:
            self.validation_completed.emit(False, f"验证过程中发生错误: {str(e)}", {})
