#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Outlook风格邮件查看器
基于成功的ENVELOPE解析，创建完美复制Outlook界面的邮件显示系统
"""

import os
import json
import logging
import webbrowser
import tempfile
from datetime import datetime
from typing import List, Dict, Optional
from dataclasses import dataclass
from pathlib import Path

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.production_optimized_v2 import ProductionOptimizedClientV2, EmailContent, EmailHeader
from utils.html_email_processor import HTMLEmailProcessor

@dataclass
class OutlookDisplayConfig:
    """Outlook风格显示配置"""
    theme: str = "light"  # light, dark
    font_family: str = "Segoe UI, Tahoma, Arial, sans-serif"
    show_conversation_view: bool = True
    auto_load_images: bool = False
    compact_mode: bool = False

class OutlookStyleViewer:
    """Outlook风格邮件查看器"""
    
    def __init__(self, config: OutlookDisplayConfig = None):
        self.config = config or OutlookDisplayConfig()
        self.html_processor = HTMLEmailProcessor()
        self.logger = logging.getLogger(__name__)
        
    def create_email_display(self, email: EmailContent) -> str:
        """创建完整的邮件显示页面"""
        try:
            # 生成Outlook风格的HTML
            email_html = self._generate_outlook_html(email)
            
            # 包装在完整页面中
            full_page = self._wrap_in_outlook_template(email_html, email.header.subject)
            
            return full_page
            
        except Exception as e:
            self.logger.error(f"创建邮件显示失败: {e}")
            return self._create_error_page(str(e))
    
    def _generate_outlook_html(self, email: EmailContent) -> str:
        """生成Outlook风格的邮件HTML"""
        try:
            # 邮件头部区域
            header_section = self._create_header_section(email.header)
            
            # 邮件工具栏
            toolbar_section = self._create_toolbar_section(email.header)
            
            # 邮件正文区域
            body_section = self._create_body_section(email)
            
            # 附件区域
            attachments_section = self._create_attachments_section(email.attachments)
            
            return f"""
            <div class="outlook-email-container">
                {toolbar_section}
                {header_section}
                {body_section}
                {attachments_section}
            </div>
            """
            
        except Exception as e:
            self.logger.error(f"生成Outlook HTML失败: {e}")
            return f"<div class='error'>邮件生成失败: {e}</div>"
    
    def _create_header_section(self, header: EmailHeader) -> str:
        """创建邮件头部区域"""
        try:
            # 格式化日期为Outlook风格
            formatted_date = self._format_outlook_date(header.date)
            
            # 处理发件人信息
            sender_display = self._format_sender_outlook(header.sender)
            
            # 处理收件人信息
            recipients_display = self._format_recipients_outlook(header.recipients)
            
            # 邮件状态指示器
            status_badges = self._create_status_badges(header.flags)
            
            return f"""
            <div class="email-header-section">
                <div class="subject-line">
                    <h1 class="email-subject">{self._escape_html(header.subject)}</h1>
                    <div class="status-badges">{status_badges}</div>
                </div>
                
                <div class="sender-info-line">
                    <div class="sender-avatar">
                        <div class="avatar-circle">{self._get_sender_initials(header.sender)}</div>
                    </div>
                    <div class="sender-details">
                        <div class="sender-name-line">
                            {sender_display}
                            <span class="email-date">{formatted_date}</span>
                        </div>
                        <div class="recipients-line">
                            <span class="to-label">收件人:</span> {recipients_display}
                        </div>
                    </div>
                </div>
            </div>
            """
            
        except Exception as e:
            self.logger.error(f"创建头部区域失败: {e}")
            return f"<div class='error'>头部创建失败: {e}</div>"
    
    def _create_toolbar_section(self, header: EmailHeader) -> str:
        """创建邮件工具栏"""
        return f"""
        <div class="email-toolbar">
            <div class="toolbar-left">
                <button class="btn-reply" title="回复">
                    <span class="icon">↩️</span> 回复
                </button>
                <button class="btn-reply-all" title="全部回复">
                    <span class="icon">↩️</span> 全部回复
                </button>
                <button class="btn-forward" title="转发">
                    <span class="icon">➡️</span> 转发
                </button>
            </div>
            <div class="toolbar-right">
                <button class="btn-flag" title="标记">
                    <span class="icon">🚩</span>
                </button>
                <button class="btn-delete" title="删除">
                    <span class="icon">🗑️</span>
                </button>
                <button class="btn-more" title="更多操作">
                    <span class="icon">⋯</span>
                </button>
            </div>
        </div>
        """
    
    def _create_body_section(self, email: EmailContent) -> str:
        """创建邮件正文区域"""
        try:
            self.logger.debug(f"创建正文区域 - HTML长度: {len(email.html_body) if email.html_body else 0}, 文本长度: {len(email.text_body) if email.text_body else 0}")

            if email.html_body and email.html_body.strip():
                # 处理HTML邮件
                self.logger.debug("处理HTML邮件内容")

                try:
                    # 使用HTML处理器处理内容
                    html_result = self.html_processor.process_html_email(
                        email.html_body,
                        email.attachments or []
                    )

                    # 生成安全的HTML内容
                    if hasattr(html_result, 'cleaned_html') and html_result.cleaned_html:
                        safe_content = self._make_outlook_compatible_html(html_result.cleaned_html)
                        self.logger.debug(f"HTML处理完成，安全内容长度: {len(safe_content)}")
                    else:
                        # 如果HTML处理器失败，直接使用原始HTML
                        self.logger.warning("HTML处理器返回空内容，使用原始HTML")
                        safe_content = self._make_outlook_compatible_html(email.html_body)

                except Exception as html_error:
                    self.logger.warning(f"HTML处理器失败: {html_error}，使用原始HTML")
                    safe_content = self._make_outlook_compatible_html(email.html_body)

                return f"""
                <div class="email-body-section">
                    <div class="body-content html-content">
                        {safe_content}
                    </div>
                </div>
                """

            elif email.text_body and email.text_body.strip():
                # 处理纯文本邮件
                self.logger.debug("处理纯文本邮件内容")
                formatted_text = self._format_text_for_outlook(email.text_body)

                return f"""
                <div class="email-body-section">
                    <div class="body-content text-content">
                        <div class="text-body-wrapper">
                            {formatted_text}
                        </div>
                    </div>
                </div>
                """
            else:
                self.logger.warning("邮件没有正文内容")
                return """
                <div class="email-body-section">
                    <div class="body-content empty-content">
                        <div class="empty-message">
                            <span class="icon">📭</span>
                            <p>此邮件没有内容</p>
                        </div>
                    </div>
                </div>
                """

        except Exception as e:
            self.logger.error(f"创建正文区域失败: {e}")
            import traceback
            traceback.print_exc()
            return f"<div class='error'>正文创建失败: {e}</div>"
    
    def _create_attachments_section(self, attachments: List[Dict]) -> str:
        """创建附件区域"""
        try:
            if not attachments:
                return ""
            
            # 过滤掉内嵌图片
            regular_attachments = [
                att for att in attachments 
                if not att.get('is_inline', False)
            ]
            
            if not regular_attachments:
                return ""
            
            attachment_items = []
            for i, attachment in enumerate(regular_attachments):
                filename = attachment.get('filename', f'附件{i+1}')
                size = attachment.get('size', 0)
                content_type = attachment.get('content_type', 'unknown')
                
                # 获取文件图标和类型
                file_icon, file_type = self._get_file_info(content_type, filename)
                
                attachment_html = f"""
                <div class="attachment-item" data-index="{i}">
                    <div class="attachment-icon">
                        <span class="file-icon">{file_icon}</span>
                    </div>
                    <div class="attachment-details">
                        <div class="attachment-name">{self._escape_html(filename)}</div>
                        <div class="attachment-meta">
                            {file_type} • {self._format_file_size(size)}
                        </div>
                    </div>
                    <div class="attachment-actions">
                        <button class="btn-download" onclick="downloadAttachment('{filename}', {i})">
                            <span class="icon">⬇️</span> 下载
                        </button>
                    </div>
                </div>
                """
                attachment_items.append(attachment_html)
            
            return f"""
            <div class="email-attachments-section">
                <div class="attachments-header">
                    <span class="icon">📎</span>
                    <span class="attachments-count">{len(regular_attachments)} 个附件</span>
                </div>
                <div class="attachments-grid">
                    {''.join(attachment_items)}
                </div>
            </div>
            """
            
        except Exception as e:
            self.logger.error(f"创建附件区域失败: {e}")
            return f"<div class='error'>附件创建失败: {e}</div>"
    
    def _format_outlook_date(self, date_obj) -> str:
        """格式化为Outlook风格的日期"""
        try:
            if hasattr(date_obj, 'strftime'):
                now = datetime.now(date_obj.tzinfo if date_obj.tzinfo else None)
                
                # 如果是今天
                if date_obj.date() == now.date():
                    return date_obj.strftime('%H:%M')
                # 如果是昨天
                elif (now.date() - date_obj.date()).days == 1:
                    return f"昨天 {date_obj.strftime('%H:%M')}"
                # 如果是本周
                elif (now.date() - date_obj.date()).days < 7:
                    weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
                    weekday = weekdays[date_obj.weekday()]
                    return f"{weekday} {date_obj.strftime('%H:%M')}"
                # 如果是今年
                elif date_obj.year == now.year:
                    return date_obj.strftime('%m月%d日')
                # 其他情况
                else:
                    return date_obj.strftime('%Y年%m月%d日')
            else:
                return str(date_obj)
        except:
            return "未知时间"
    
    def _format_sender_outlook(self, sender: str) -> str:
        """格式化发件人为Outlook风格"""
        try:
            if '<' in sender and '>' in sender:
                name_part = sender.split('<')[0].strip()
                email_part = sender.split('<')[1].split('>')[0].strip()
                
                if name_part:
                    return f"""
                    <span class="sender-name">{self._escape_html(name_part)}</span>
                    <span class="sender-email">&lt;{self._escape_html(email_part)}&gt;</span>
                    """
                else:
                    return f'<span class="sender-email">{self._escape_html(email_part)}</span>'
            else:
                return f'<span class="sender-email">{self._escape_html(sender)}</span>'
        except:
            return f'<span class="sender-email">{self._escape_html(sender)}</span>'
    
    def _format_recipients_outlook(self, recipients: List[str]) -> str:
        """格式化收件人为Outlook风格"""
        try:
            if not recipients:
                return '<span class="no-recipients">未知收件人</span>'
            
            if len(recipients) == 1:
                return f'<span class="recipient">{self._escape_html(recipients[0])}</span>'
            else:
                first = self._escape_html(recipients[0])
                others = len(recipients) - 1
                return f'<span class="recipient">{first}</span> <span class="others-count">和其他{others}人</span>'
        except:
            return '<span class="no-recipients">收件人解析失败</span>'
    
    def _get_sender_initials(self, sender: str) -> str:
        """获取发件人姓名首字母"""
        try:
            if '<' in sender:
                name = sender.split('<')[0].strip()
            else:
                name = sender.split('@')[0] if '@' in sender else sender
            
            if name:
                # 处理中文姓名
                if any('\u4e00' <= char <= '\u9fff' for char in name):
                    return name[0] if name else '?'
                # 处理英文姓名
                else:
                    words = name.split()
                    if len(words) >= 2:
                        return (words[0][0] + words[1][0]).upper()
                    elif len(words) == 1:
                        return words[0][0].upper()
            
            return '?'
        except:
            return '?'
    
    def _create_status_badges(self, flags: List[str]) -> str:
        """创建状态徽章"""
        try:
            badges = []
            
            if '\\Flagged' in flags:
                badges.append('<span class="badge flag-badge" title="已标记">🚩</span>')
            
            if '\\Important' in flags:
                badges.append('<span class="badge important-badge" title="重要">❗</span>')
            
            if not flags or '\\Seen' not in flags:
                badges.append('<span class="badge unread-badge" title="未读">●</span>')
            
            return ' '.join(badges)
        except:
            return ""
    
    def _get_file_info(self, content_type: str, filename: str) -> tuple:
        """获取文件图标和类型信息"""
        try:
            # 根据文件扩展名和MIME类型确定图标
            ext = Path(filename).suffix.lower() if filename else ""
            
            if content_type.startswith('image/') or ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp']:
                return '🖼️', '图片'
            elif content_type.startswith('video/') or ext in ['.mp4', '.avi', '.mov', '.wmv']:
                return '🎥', '视频'
            elif content_type.startswith('audio/') or ext in ['.mp3', '.wav', '.flac']:
                return '🎵', '音频'
            elif 'pdf' in content_type or ext == '.pdf':
                return '📄', 'PDF'
            elif 'word' in content_type or ext in ['.doc', '.docx']:
                return '📝', 'Word文档'
            elif 'excel' in content_type or ext in ['.xls', '.xlsx']:
                return '📊', 'Excel表格'
            elif 'powerpoint' in content_type or ext in ['.ppt', '.pptx']:
                return '📽️', 'PowerPoint'
            elif ext in ['.zip', '.rar', '.7z']:
                return '🗜️', '压缩文件'
            elif ext in ['.txt', '.log']:
                return '📄', '文本文件'
            else:
                return '📎', '文件'
        except:
            return '📎', '文件'
    
    def _format_file_size(self, size: int) -> str:
        """格式化文件大小"""
        try:
            if size < 1024:
                return f"{size} B"
            elif size < 1024 * 1024:
                return f"{size / 1024:.1f} KB"
            else:
                return f"{size / (1024 * 1024):.1f} MB"
        except:
            return "未知大小"
    
    def _make_outlook_compatible_html(self, html_content: str) -> str:
        """使HTML内容兼容Outlook显示"""
        try:
            if not html_content or not html_content.strip():
                self.logger.warning("HTML内容为空")
                return "<p>邮件内容为空</p>"

            self.logger.debug(f"处理HTML内容，长度: {len(html_content)}")

            # 清理和优化HTML内容
            cleaned_html = self._clean_html_content(html_content)

            # 添加Outlook兼容的样式和结构
            compatible_html = f"""
            <div class="outlook-html-content">
                <div class="email-content-wrapper">
                    {cleaned_html}
                </div>
            </div>
            """

            self.logger.debug(f"HTML处理完成，最终长度: {len(compatible_html)}")
            return compatible_html

        except Exception as e:
            self.logger.error(f"HTML兼容性处理失败: {e}")
            # 返回原始内容作为备用
            return f"""
            <div class="outlook-html-content">
                <div class="email-content-wrapper">
                    {self._escape_html(str(html_content))}
                </div>
            </div>
            """

    def _clean_html_content(self, html_content: str) -> str:
        """清理HTML内容"""
        try:
            import re

            # 移除可能有害的标签和属性
            # 但保留基本的格式标签
            cleaned = html_content

            # 移除script标签
            cleaned = re.sub(r'<script[^>]*>.*?</script>', '', cleaned, flags=re.DOTALL | re.IGNORECASE)

            # 移除style标签中的可能有害内容，但保留基本样式
            cleaned = re.sub(r'<style[^>]*>.*?</style>', '', cleaned, flags=re.DOTALL | re.IGNORECASE)

            # 移除事件处理器
            cleaned = re.sub(r'\s*on\w+\s*=\s*["\'][^"\']*["\']', '', cleaned, flags=re.IGNORECASE)

            # 确保图片标签有正确的属性
            cleaned = re.sub(r'<img([^>]*?)>', r'<img\1 style="max-width: 100%; height: auto;">', cleaned, flags=re.IGNORECASE)

            # 确保链接在新窗口打开
            cleaned = re.sub(r'<a\s+([^>]*?)>', r'<a \1 target="_blank" rel="noopener noreferrer">', cleaned, flags=re.IGNORECASE)

            return cleaned

        except Exception as e:
            self.logger.error(f"HTML清理失败: {e}")
            return html_content
    
    def _format_text_for_outlook(self, text: str) -> str:
        """格式化纯文本为Outlook风格"""
        try:
            import html
            import re
            
            # 转义HTML
            escaped = html.escape(text)
            
            # 转换URL为链接
            url_pattern = r'(https?://[^\s]+)'
            escaped = re.sub(url_pattern, r'<a href="\1" target="_blank" class="email-link">\1</a>', escaped)
            
            # 转换换行
            escaped = escaped.replace('\n', '<br>')
            
            return f'<div class="text-content-formatted">{escaped}</div>'
        except:
            return f'<pre>{text}</pre>'
    
    def _escape_html(self, text: str) -> str:
        """转义HTML特殊字符"""
        try:
            import html
            return html.escape(str(text))
        except:
            return str(text)
    
    def _wrap_in_outlook_template(self, email_html: str, title: str) -> str:
        """包装在Outlook风格的页面模板中"""
        return f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{self._escape_html(title)} - 邮件查看器</title>
    <link rel="stylesheet" href="outlook_styles.css">
</head>
<body class="outlook-theme-{self.config.theme}">
    <div class="outlook-container">
        <div class="outlook-header">
            <div class="app-title">📧 邮件查看器</div>
            <div class="header-actions">
                <button class="btn-close" onclick="window.close()">✕</button>
            </div>
        </div>
        <div class="outlook-content">
            {email_html}
        </div>
    </div>
    <script src="outlook_viewer.js"></script>
</body>
</html>"""
    
    def _create_error_page(self, error_message: str) -> str:
        """创建错误页面"""
        return f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>邮件显示错误</title>
    <style>
        body {{ font-family: Segoe UI, Arial, sans-serif; margin: 50px; }}
        .error-box {{ 
            border: 1px solid #d32f2f; 
            background: #ffebee; 
            padding: 20px; 
            border-radius: 4px; 
            color: #d32f2f;
        }}
    </style>
</head>
<body>
    <div class="error-box">
        <h2>❌ 邮件显示错误</h2>
        <p>{self._escape_html(error_message)}</p>
    </div>
</body>
</html>"""

def main():
    """测试Outlook风格查看器"""
    print("📧 Outlook风格邮件查看器测试")
    print("=" * 50)
    
    # 这里可以添加测试代码
    viewer = OutlookStyleViewer()
    print("✅ Outlook风格查看器创建成功")

if __name__ == "__main__":
    main()
