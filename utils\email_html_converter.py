#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件HTML转换器
将获取的邮件转换为Outlook风格的HTML页面
"""

import os
import json
import logging
import tempfile
import webbrowser
from datetime import datetime
from typing import List, Dict, Optional, Tuple
from pathlib import Path

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.production_optimized_v2 import ProductionOptimizedClientV2, EmailContent, EmailHeader
from ui.outlook_style_viewer import OutlookStyleViewer, OutlookDisplayConfig

class EmailHTMLConverter:
    """邮件HTML转换器"""
    
    def __init__(self, output_dir: str = "email_html_output"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 创建Outlook风格查看器
        self.outlook_viewer = OutlookStyleViewer()
        
        self.logger = logging.getLogger(__name__)
        
        # 确保CSS和JS文件存在
        self._ensure_assets()
    
    def _ensure_assets(self):
        """确保CSS和JS文件存在于输出目录"""
        try:
            # 复制CSS文件
            css_source = Path("outlook_styles.css")
            css_target = self.output_dir / "outlook_styles.css"
            
            if css_source.exists():
                import shutil
                shutil.copy2(css_source, css_target)
                self.logger.debug("CSS文件已复制到输出目录")
            else:
                self.logger.warning("CSS文件不存在，将使用内联样式")
            
            # 复制JS文件
            js_source = Path("outlook_viewer.js")
            js_target = self.output_dir / "outlook_viewer.js"
            
            if js_source.exists():
                import shutil
                shutil.copy2(js_source, js_target)
                self.logger.debug("JS文件已复制到输出目录")
            else:
                # 创建基本的JS文件
                self._create_basic_js(js_target)
                
        except Exception as e:
            self.logger.error(f"复制资源文件失败: {e}")
    
    def _create_basic_js(self, js_path: Path):
        """创建基本的JavaScript文件"""
        js_content = """
// Outlook邮件查看器JavaScript

// 下载附件功能
function downloadAttachment(filename, index) {
    alert('附件下载功能: ' + filename);
    console.log('下载附件:', filename, '索引:', index);
}

// 工具栏按钮功能
document.addEventListener('DOMContentLoaded', function() {
    // 回复按钮
    const replyBtn = document.querySelector('.btn-reply');
    if (replyBtn) {
        replyBtn.addEventListener('click', function() {
            alert('回复功能暂未实现');
        });
    }
    
    // 全部回复按钮
    const replyAllBtn = document.querySelector('.btn-reply-all');
    if (replyAllBtn) {
        replyAllBtn.addEventListener('click', function() {
            alert('全部回复功能暂未实现');
        });
    }
    
    // 转发按钮
    const forwardBtn = document.querySelector('.btn-forward');
    if (forwardBtn) {
        forwardBtn.addEventListener('click', function() {
            alert('转发功能暂未实现');
        });
    }
    
    // 标记按钮
    const flagBtn = document.querySelector('.btn-flag');
    if (flagBtn) {
        flagBtn.addEventListener('click', function() {
            alert('标记功能暂未实现');
        });
    }
    
    // 删除按钮
    const deleteBtn = document.querySelector('.btn-delete');
    if (deleteBtn) {
        deleteBtn.addEventListener('click', function() {
            if (confirm('确定要删除这封邮件吗？')) {
                alert('删除功能暂未实现');
            }
        });
    }
    
    // 更多操作按钮
    const moreBtn = document.querySelector('.btn-more');
    if (moreBtn) {
        moreBtn.addEventListener('click', function() {
            alert('更多操作功能暂未实现');
        });
    }
});

// 图片加载错误处理
document.addEventListener('DOMContentLoaded', function() {
    const images = document.querySelectorAll('img');
    images.forEach(function(img) {
        img.addEventListener('error', function() {
            this.style.display = 'none';
        });
    });
});
"""
        try:
            with open(js_path, 'w', encoding='utf-8') as f:
                f.write(js_content)
            self.logger.debug("基本JS文件已创建")
        except Exception as e:
            self.logger.error(f"创建JS文件失败: {e}")
    
    def convert_email_to_html(self, email: EmailContent, 
                             filename: str = None,
                             open_in_browser: bool = True) -> str:
        """
        将邮件转换为HTML文件
        
        Args:
            email: 邮件内容
            filename: 输出文件名，如果不指定则自动生成
            open_in_browser: 是否在浏览器中打开
            
        Returns:
            HTML文件路径
        """
        try:
            # 生成文件名
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                safe_subject = self._make_safe_filename(email.header.subject)
                filename = f"email_{timestamp}_{safe_subject}.html"
            
            # 生成HTML内容
            html_content = self.outlook_viewer.create_email_display(email)
            
            # 保存文件
            file_path = self.output_dir / filename
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            self.logger.info(f"邮件HTML已保存到: {file_path}")
            
            # 在浏览器中打开
            if open_in_browser:
                webbrowser.open(f'file://{file_path.absolute()}')
                self.logger.info("已在浏览器中打开邮件")
            
            return str(file_path)
            
        except Exception as e:
            self.logger.error(f"转换邮件为HTML失败: {e}")
            raise
    
    def convert_emails_to_html_list(self, emails: List[EmailContent],
                                   list_filename: str = "email_list.html",
                                   open_in_browser: bool = True) -> str:
        """
        将多封邮件转换为HTML列表页面
        
        Args:
            emails: 邮件列表
            list_filename: 列表页面文件名
            open_in_browser: 是否在浏览器中打开
            
        Returns:
            HTML列表文件路径
        """
        try:
            # 为每封邮件生成单独的HTML文件
            email_files = []
            for i, email in enumerate(emails):
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                safe_subject = self._make_safe_filename(email.header.subject)
                filename = f"email_{i+1:03d}_{timestamp}_{safe_subject}.html"
                
                file_path = self.convert_email_to_html(
                    email, 
                    filename=filename, 
                    open_in_browser=False
                )
                email_files.append((email, Path(file_path).name))
            
            # 生成邮件列表页面
            list_html = self._create_email_list_html(email_files)
            
            # 保存列表文件
            list_path = self.output_dir / list_filename
            with open(list_path, 'w', encoding='utf-8') as f:
                f.write(list_html)
            
            self.logger.info(f"邮件列表HTML已保存到: {list_path}")
            
            # 在浏览器中打开
            if open_in_browser:
                webbrowser.open(f'file://{list_path.absolute()}')
                self.logger.info("已在浏览器中打开邮件列表")
            
            return str(list_path)
            
        except Exception as e:
            self.logger.error(f"转换邮件列表为HTML失败: {e}")
            raise
    
    def _create_email_list_html(self, email_files: List[Tuple[EmailContent, str]]) -> str:
        """创建邮件列表HTML页面"""
        try:
            # 生成邮件列表项
            email_items = []
            for i, (email, filename) in enumerate(email_files, 1):
                # 格式化日期
                date_str = self._format_date_for_list(email.header.date)
                
                # 获取发件人显示名
                sender_display = self._get_sender_display_name(email.header.sender)
                
                # 截取主题
                subject_preview = email.header.subject[:60] + "..." if len(email.header.subject) > 60 else email.header.subject
                
                # 获取正文预览
                body_preview = self._get_body_preview(email)
                
                # 邮件大小
                size_display = self._format_size(email.header.size)
                
                # 未读状态
                unread_class = "unread" if "\\Seen" not in email.header.flags else ""
                
                item_html = f"""
                <div class="email-item {unread_class}" onclick="openEmail('{filename}')">
                    <div class="email-item-header">
                        <div class="sender-name">{self._escape_html(sender_display)}</div>
                        <div class="email-date">{date_str}</div>
                    </div>
                    <div class="email-subject">{self._escape_html(subject_preview)}</div>
                    <div class="email-preview">{self._escape_html(body_preview)}</div>
                    <div class="email-meta">
                        <span class="email-size">{size_display}</span>
                        {self._get_status_indicators(email.header.flags)}
                    </div>
                </div>
                """
                email_items.append(item_html)
            
            # 生成完整页面
            return f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮件列表 - 邮件查看器</title>
    <link rel="stylesheet" href="outlook_styles.css">
    <style>
        .email-list-container {{
            max-width: 1000px;
            margin: 0 auto;
            background: white;
        }}
        .list-header {{
            background: #0078d4;
            color: white;
            padding: 20px;
            text-align: center;
        }}
        .list-stats {{
            background: #f3f2f1;
            padding: 16px 20px;
            border-bottom: 1px solid #edebe9;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }}
        .email-item {{
            border-bottom: 1px solid #edebe9;
            padding: 16px 20px;
            cursor: pointer;
            transition: background-color 0.2s;
        }}
        .email-item:hover {{
            background: #f8f8f8;
        }}
        .email-item.unread {{
            border-left: 4px solid #0078d4;
            background: #f9f9f9;
        }}
        .email-item-header {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }}
        .sender-name {{
            font-weight: 600;
            color: #323130;
        }}
        .email-date {{
            color: #605e5c;
            font-size: 13px;
        }}
        .email-subject {{
            font-weight: 500;
            color: #323130;
            margin-bottom: 6px;
            font-size: 15px;
        }}
        .email-preview {{
            color: #605e5c;
            font-size: 13px;
            line-height: 1.4;
            margin-bottom: 8px;
        }}
        .email-meta {{
            display: flex;
            justify-content: space-between;
            align-items: center;
        }}
        .email-size {{
            color: #605e5c;
            font-size: 12px;
        }}
        .status-indicators {{
            display: flex;
            gap: 6px;
        }}
        .status-badge {{
            font-size: 12px;
        }}
    </style>
</head>
<body>
    <div class="email-list-container">
        <div class="list-header">
            <h1>📧 邮件列表</h1>
            <p>共 {len(email_files)} 封邮件</p>
        </div>
        
        <div class="list-stats">
            <div class="stats-left">
                <strong>邮件总数:</strong> {len(email_files)}
            </div>
            <div class="stats-right">
                <span>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</span>
            </div>
        </div>
        
        <div class="email-list">
            {''.join(email_items)}
        </div>
    </div>
    
    <script>
        function openEmail(filename) {{
            window.open(filename, '_blank');
        }}
    </script>
</body>
</html>"""
            
        except Exception as e:
            self.logger.error(f"创建邮件列表HTML失败: {e}")
            return f"<html><body><h1>错误</h1><p>{e}</p></body></html>"
    
    def _make_safe_filename(self, text: str, max_length: int = 50) -> str:
        """创建安全的文件名"""
        try:
            import re
            # 移除或替换不安全的字符
            safe = re.sub(r'[<>:"/\\|?*]', '_', text)
            safe = safe.strip()
            
            # 限制长度
            if len(safe) > max_length:
                safe = safe[:max_length]
            
            # 确保不为空
            if not safe:
                safe = "untitled"
            
            return safe
        except:
            return "untitled"
    
    def _format_date_for_list(self, date_obj) -> str:
        """格式化日期用于列表显示"""
        try:
            if hasattr(date_obj, 'strftime'):
                now = datetime.now(date_obj.tzinfo if date_obj.tzinfo else None)
                
                # 如果是今天
                if date_obj.date() == now.date():
                    return date_obj.strftime('%H:%M')
                # 如果是昨天
                elif (now.date() - date_obj.date()).days == 1:
                    return f"昨天"
                # 如果是本周
                elif (now.date() - date_obj.date()).days < 7:
                    weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
                    return weekdays[date_obj.weekday()]
                # 如果是今年
                elif date_obj.year == now.year:
                    return date_obj.strftime('%m/%d')
                else:
                    return date_obj.strftime('%Y/%m/%d')
            else:
                return str(date_obj)
        except:
            return "未知"
    
    def _get_sender_display_name(self, sender: str) -> str:
        """获取发件人显示名"""
        try:
            if '<' in sender and '>' in sender:
                name_part = sender.split('<')[0].strip()
                if name_part:
                    return name_part
                else:
                    return sender.split('<')[1].split('>')[0].strip()
            else:
                return sender
        except:
            return sender
    
    def _get_body_preview(self, email: EmailContent) -> str:
        """获取邮件正文预览"""
        try:
            # 优先使用文本正文
            if email.text_body:
                text = email.text_body.strip()
                # 移除多余的换行符
                text = ' '.join(text.split())
                return text[:100] + "..." if len(text) > 100 else text
            
            # 如果没有文本正文，尝试从HTML中提取
            elif email.html_body:
                import re
                # 简单的HTML标签移除
                text = re.sub(r'<[^>]+>', '', email.html_body)
                text = text.strip()
                text = ' '.join(text.split())
                return text[:100] + "..." if len(text) > 100 else text
            
            else:
                return "（无正文内容）"
        except:
            return "（正文解析失败）"
    
    def _format_size(self, size: int) -> str:
        """格式化文件大小"""
        try:
            if size < 1024:
                return f"{size} B"
            elif size < 1024 * 1024:
                return f"{size / 1024:.1f} KB"
            else:
                return f"{size / (1024 * 1024):.1f} MB"
        except:
            return "未知"
    
    def _get_status_indicators(self, flags: List[str]) -> str:
        """获取状态指示器"""
        try:
            indicators = []
            
            if '\\Flagged' in flags:
                indicators.append('<span class="status-badge flag">🚩</span>')
            
            if '\\Important' in flags:
                indicators.append('<span class="status-badge important">❗</span>')
            
            if not flags or '\\Seen' not in flags:
                indicators.append('<span class="status-badge unread">●</span>')
            
            return f'<div class="status-indicators">{"".join(indicators)}</div>'
        except:
            return ""
    
    def _escape_html(self, text: str) -> str:
        """转义HTML特殊字符"""
        try:
            import html
            return html.escape(str(text))
        except:
            return str(text)

def main():
    """测试HTML转换器"""
    print("🔄 邮件HTML转换器测试")
    print("=" * 50)
    
    converter = EmailHTMLConverter()
    print("✅ HTML转换器创建成功")
    print(f"输出目录: {converter.output_dir.absolute()}")

if __name__ == "__main__":
    main()