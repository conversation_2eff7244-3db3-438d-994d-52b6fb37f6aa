#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
许可证服务器数据模型
"""

import uuid
import hashlib
import secrets
import json
from datetime import datetime, timedelta
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from sqlalchemy import func

db = SQLAlchemy()


class License(db.Model):
    """许可证模型"""
    __tablename__ = 'licenses'
    
    id = db.Column(db.Integer, primary_key=True)
    license_key = db.Column(db.String(64), unique=True, nullable=False, index=True)
    license_hash = db.Column(db.String(128), nullable=False, index=True)
    
    # 许可证信息
    product_name = db.Column(db.String(100), nullable=False, default='EnterpriseEmailManager')
    version = db.Column(db.String(20), nullable=False, default='2.1.0')
    license_type = db.Column(db.String(20), nullable=False, default='standard')  # standard, premium, enterprise
    
    # 状态信息
    status = db.Column(db.String(20), nullable=False, default='active')  # active, suspended, expired, revoked
    max_activations = db.Column(db.Integer, nullable=False, default=1)
    current_activations = db.Column(db.Integer, nullable=False, default=0)
    
    # 时间信息
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    activated_at = db.Column(db.DateTime)
    expires_at = db.Column(db.DateTime)
    last_validated_at = db.Column(db.DateTime)
    
    # 用户信息
    user_name = db.Column(db.String(100))
    user_email = db.Column(db.String(120))
    company_name = db.Column(db.String(100))
    
    # 功能权限（JSON格式存储）
    features = db.Column(db.Text, default='["email_management"]')  # JSON string
    
    # 备注信息
    notes = db.Column(db.Text)
    
    # 关联的激活记录
    activations = db.relationship('LicenseActivation', backref='license', lazy='dynamic', cascade='all, delete-orphan')
    validation_logs = db.relationship('ValidationLog', backref='license', lazy='dynamic', cascade='all, delete-orphan')
    
    def __init__(self, **kwargs):
        super(License, self).__init__(**kwargs)
        if not self.license_key:
            self.license_key = self.generate_license_key()
        self.license_hash = self.generate_license_hash(self.license_key)
    
    @staticmethod
    def generate_license_key():
        """生成许可证密钥"""
        # 格式: XXXX-XXXX-XXXX-XXXX
        segments = []
        for _ in range(4):
            segment = ''.join(secrets.choice('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789') for _ in range(4))
            segments.append(segment)
        return '-'.join(segments)
    
    @staticmethod
    def generate_license_hash(license_key):
        """生成许可证哈希"""
        return hashlib.sha256(license_key.encode('utf-8')).hexdigest()
    
    def is_valid(self):
        """检查许可证是否有效"""
        if self.status != 'active':
            return False
        
        if self.expires_at and datetime.utcnow() > self.expires_at:
            return False
        
        if self.current_activations >= self.max_activations:
            return False
        
        return True
    
    def can_activate(self):
        """检查是否可以激活"""
        return self.is_valid() and self.current_activations < self.max_activations
    
    def activate(self, machine_fingerprint, user_info=None):
        """激活许可证"""
        if not self.can_activate():
            return False
        
        # 检查是否已在此机器上激活
        existing = LicenseActivation.query.filter_by(
            license_id=self.id,
            machine_fingerprint=machine_fingerprint,
            status='active'
        ).first()
        
        if existing:
            return True  # 已激活
        
        # 创建新的激活记录
        activation = LicenseActivation(
            license_id=self.id,
            machine_fingerprint=machine_fingerprint,
            user_info=user_info or {},
            activated_at=datetime.utcnow()
        )
        
        db.session.add(activation)
        self.current_activations += 1
        if not self.activated_at:
            self.activated_at = datetime.utcnow()
        
        return True
    
    def deactivate(self, machine_fingerprint):
        """停用许可证"""
        activation = LicenseActivation.query.filter_by(
            license_id=self.id,
            machine_fingerprint=machine_fingerprint,
            status='active'
        ).first()
        
        if activation:
            activation.status = 'deactivated'
            activation.deactivated_at = datetime.utcnow()
            self.current_activations = max(0, self.current_activations - 1)
            return True
        
        return False
    
    def log_validation(self, machine_fingerprint, result, details=None):
        """记录验证日志"""
        # 确保details是字符串格式
        if isinstance(details, dict):
            details_str = json.dumps(details)
        elif isinstance(details, str):
            details_str = details
        else:
            details_str = json.dumps({})

        log = ValidationLog(
            license_id=self.id,
            machine_fingerprint=machine_fingerprint,
            validation_result=result,
            validation_details=details_str,
            validated_at=datetime.utcnow()
        )
        db.session.add(log)
        self.last_validated_at = datetime.utcnow()
    
    def to_dict(self, include_sensitive=False):
        """转换为字典"""
        data = {
            'id': self.id,
            'license_key': self.license_key if include_sensitive else self.license_key[:8] + '****',
            'product_name': self.product_name,
            'version': self.version,
            'license_type': self.license_type,
            'status': self.status,
            'max_activations': self.max_activations,
            'current_activations': self.current_activations,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'activated_at': self.activated_at.isoformat() if self.activated_at else None,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'last_validated_at': self.last_validated_at.isoformat() if self.last_validated_at else None,
            'user_name': self.user_name,
            'user_email': self.user_email,
            'company_name': self.company_name,
            'features': self.features,
            'is_valid': self.is_valid()
        }
        return data


class LicenseActivation(db.Model):
    """许可证激活记录"""
    __tablename__ = 'license_activations'
    
    id = db.Column(db.Integer, primary_key=True)
    license_id = db.Column(db.Integer, db.ForeignKey('licenses.id'), nullable=False)
    
    machine_fingerprint = db.Column(db.String(64), nullable=False, index=True)
    status = db.Column(db.String(20), nullable=False, default='active')  # active, deactivated
    
    activated_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    deactivated_at = db.Column(db.DateTime)
    last_seen_at = db.Column(db.DateTime)
    
    # 用户信息（JSON格式）
    user_info = db.Column(db.Text)
    
    # 系统信息
    ip_address = db.Column(db.String(45))
    user_agent = db.Column(db.String(500))
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'machine_fingerprint': self.machine_fingerprint,
            'status': self.status,
            'activated_at': self.activated_at.isoformat() if self.activated_at else None,
            'deactivated_at': self.deactivated_at.isoformat() if self.deactivated_at else None,
            'last_seen_at': self.last_seen_at.isoformat() if self.last_seen_at else None,
            'ip_address': self.ip_address,
            'user_info': self.user_info
        }


class ValidationLog(db.Model):
    """验证日志"""
    __tablename__ = 'validation_logs'
    
    id = db.Column(db.Integer, primary_key=True)
    license_id = db.Column(db.Integer, db.ForeignKey('licenses.id'), nullable=False)
    
    machine_fingerprint = db.Column(db.String(64), nullable=False, index=True)
    validation_result = db.Column(db.String(20), nullable=False)  # success, failed, error
    validation_details = db.Column(db.Text)  # JSON格式的详细信息
    
    validated_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow, index=True)
    
    # 请求信息
    ip_address = db.Column(db.String(45))
    user_agent = db.Column(db.String(500))
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'license_id': self.license_id,
            'machine_fingerprint': self.machine_fingerprint,
            'validation_result': self.validation_result,
            'validation_details': self.validation_details,
            'validated_at': self.validated_at.isoformat() if self.validated_at else None,
            'ip_address': self.ip_address
        }


class Admin(db.Model):
    """管理员用户"""
    __tablename__ = 'admins'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(128), nullable=False)
    
    is_active = db.Column(db.Boolean, nullable=False, default=True)
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    last_login_at = db.Column(db.DateTime)
    
    def set_password(self, password):
        """设置密码"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """检查密码"""
        return check_password_hash(self.password_hash, password)
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login_at': self.last_login_at.isoformat() if self.last_login_at else None
        }
