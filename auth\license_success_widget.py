#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
授权成功显示组件
显示许可证到期时间和相关信息的美观UI
"""

import sys
from datetime import datetime, timedelta
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *


class LicenseSuccessWidget(QWidget):
    """授权成功显示组件"""
    
    def __init__(self, license_data=None, parent=None):
        super().__init__(parent)
        self.license_data = license_data or {}
        self.timer = QTimer()
        self.timer.timeout.connect(self._update_countdown)
        
        self._setup_ui()
        self._setup_styles()
        self._update_display()
        
        # 启动倒计时更新
        self.timer.start(1000)  # 每秒更新一次
    
    def _setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # 成功图标和标题
        self._create_header(layout)
        
        # 许可证信息卡片
        self._create_license_info_card(layout)
        
        # 到期时间倒计时
        self._create_countdown_card(layout)
        
        # 功能权限信息
        self._create_features_card(layout)
        
        # 操作按钮
        self._create_action_buttons(layout)
    
    def _create_header(self, layout):
        """创建标题区域"""
        header_frame = QFrame()
        header_frame.setObjectName("headerFrame")
        header_layout = QVBoxLayout(header_frame)
        header_layout.setSpacing(10)
        
        # 成功图标
        icon_label = QLabel("✅")
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("""
            QLabel {
                font-size: 48px;
                color: #28a745;
                margin: 10px;
            }
        """)
        
        # 成功标题
        title_label = QLabel("授权验证成功！")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setObjectName("successTitle")
        
        # 副标题
        subtitle_label = QLabel("您的微软Ou工具已成功激活")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setObjectName("successSubtitle")
        
        header_layout.addWidget(icon_label)
        header_layout.addWidget(title_label)
        header_layout.addWidget(subtitle_label)
        
        layout.addWidget(header_frame)
    
    def _create_license_info_card(self, layout):
        """创建许可证信息卡片"""
        card = QFrame()
        card.setObjectName("infoCard")
        card_layout = QVBoxLayout(card)
        
        # 卡片标题
        title = QLabel("📋 许可证信息")
        title.setObjectName("cardTitle")
        card_layout.addWidget(title)
        
        # 信息网格
        info_layout = QGridLayout()
        info_layout.setSpacing(15)
        
        # 许可证密钥
        self._add_info_row(info_layout, 0, "🔑 许可证密钥:", self._mask_license_key())
        
        # 许可证类型
        license_type = self.license_data.get('license_type', 'standard')
        type_display = {'standard': '标准版', 'premium': '高级版', 'enterprise': '企业版'}.get(license_type, license_type)
        self._add_info_row(info_layout, 1, "📦 许可证类型:", type_display)
        
        # 状态
        status = self.license_data.get('status', 'active')
        status_display = {'active': '✅ 已激活', 'suspended': '⏸️ 已暂停', 'expired': '❌ 已过期'}.get(status, status)
        self._add_info_row(info_layout, 2, "📊 当前状态:", status_display)
        
        card_layout.addLayout(info_layout)
        layout.addWidget(card)
    
    def _create_countdown_card(self, layout):
        """创建倒计时卡片"""
        self.countdown_card = QFrame()
        self.countdown_card.setObjectName("countdownCard")
        card_layout = QVBoxLayout(self.countdown_card)
        
        # 卡片标题
        title = QLabel("⏰ 许可证有效期")
        title.setObjectName("cardTitle")
        card_layout.addWidget(title)
        
        # 倒计时显示区域
        countdown_frame = QFrame()
        countdown_layout = QVBoxLayout(countdown_frame)
        countdown_layout.setSpacing(10)
        
        # 到期时间
        self.expiry_label = QLabel()
        self.expiry_label.setObjectName("expiryLabel")
        self.expiry_label.setAlignment(Qt.AlignCenter)
        
        # 剩余时间显示
        self.countdown_label = QLabel()
        self.countdown_label.setObjectName("countdownLabel")
        self.countdown_label.setAlignment(Qt.AlignCenter)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setObjectName("expiryProgress")
        self.progress_bar.setTextVisible(False)
        self.progress_bar.setFixedHeight(8)
        
        countdown_layout.addWidget(self.expiry_label)
        countdown_layout.addWidget(self.countdown_label)
        countdown_layout.addWidget(self.progress_bar)
        
        card_layout.addWidget(countdown_frame)
        layout.addWidget(self.countdown_card)
    
    def _create_features_card(self, layout):
        """创建功能权限卡片"""
        card = QFrame()
        card.setObjectName("featuresCard")
        card_layout = QVBoxLayout(card)
        
        # 卡片标题
        title = QLabel("🚀 可用功能")
        title.setObjectName("cardTitle")
        card_layout.addWidget(title)
        
        # 功能列表
        features = self.license_data.get('features', ['email_management'])
        features_layout = QVBoxLayout()
        
        feature_names = {
            'email_management': '📧 邮件管理',
            'batch_import': '📥 批量导入',
            'advanced_search': '🔍 高级搜索'
        }
        
        for feature in features:
            feature_label = QLabel(feature_names.get(feature, f'• {feature}'))
            feature_label.setObjectName("featureItem")
            features_layout.addWidget(feature_label)
        
        card_layout.addLayout(features_layout)
        layout.addWidget(card)
    
    def _create_action_buttons(self, layout):
        """创建操作按钮"""
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)
        
        # 开始使用按钮
        start_button = QPushButton("🚀 开始使用")
        start_button.setObjectName("primaryButton")
        start_button.clicked.connect(self._start_using)
        
        # 查看详情按钮
        details_button = QPushButton("📋 查看详情")
        details_button.setObjectName("secondaryButton")
        details_button.clicked.connect(self._show_details)
        
        button_layout.addWidget(start_button)
        button_layout.addWidget(details_button)
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
    
    def _add_info_row(self, layout, row, label_text, value_text):
        """添加信息行"""
        label = QLabel(label_text)
        label.setObjectName("infoLabel")
        
        value = QLabel(str(value_text))
        value.setObjectName("infoValue")
        
        layout.addWidget(label, row, 0)
        layout.addWidget(value, row, 1)
    
    def _mask_license_key(self):
        """遮蔽许可证密钥"""
        license_key = self.license_data.get('license_key', '')
        if len(license_key) > 8:
            return license_key[:4] + '-****-****-' + license_key[-4:]
        return license_key
    
    def _update_display(self):
        """更新显示内容"""
        expiry_time = self.license_data.get('expiry_time')
        
        if not expiry_time or expiry_time == '永久':
            # 永久许可证
            self.expiry_label.setText("🎉 永久许可证")
            self.countdown_label.setText("此许可证永不过期")
            self.progress_bar.setVisible(False)
            self.countdown_card.setStyleSheet("""
                QFrame#countdownCard {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #e8f5e8, stop:1 #d4edda);
                    border: 2px solid #28a745;
                    border-radius: 12px;
                    padding: 15px;
                }
            """)
        else:
            # 有期限许可证
            self._update_countdown()
    
    def _update_countdown(self):
        """更新倒计时"""
        expiry_time = self.license_data.get('expiry_time')
        if not expiry_time or expiry_time == '永久':
            return
        
        try:
            # 解析到期时间
            if isinstance(expiry_time, str):
                expiry_dt = datetime.fromisoformat(expiry_time.replace('Z', '+00:00'))
            else:
                expiry_dt = expiry_time
            
            now = datetime.now(expiry_dt.tzinfo) if expiry_dt.tzinfo else datetime.now()
            
            if now >= expiry_dt:
                # 已过期
                self.expiry_label.setText("❌ 许可证已过期")
                self.countdown_label.setText("请联系管理员续期")
                self.progress_bar.setValue(0)
                self._set_expired_style()
            else:
                # 未过期，计算剩余时间
                remaining = expiry_dt - now
                days = remaining.days
                hours, remainder = divmod(remaining.seconds, 3600)
                minutes, seconds = divmod(remainder, 60)
                
                # 显示到期时间
                self.expiry_label.setText(f"📅 到期时间: {expiry_dt.strftime('%Y年%m月%d日 %H:%M')}")
                
                # 显示剩余时间
                if days > 0:
                    self.countdown_label.setText(f"⏳ 剩余 {days} 天 {hours} 小时 {minutes} 分钟")
                elif hours > 0:
                    self.countdown_label.setText(f"⏳ 剩余 {hours} 小时 {minutes} 分钟 {seconds} 秒")
                else:
                    self.countdown_label.setText(f"⏳ 剩余 {minutes} 分钟 {seconds} 秒")
                
                # 更新进度条（假设许可证有效期为1年）
                total_seconds = 365 * 24 * 3600  # 1年的秒数
                remaining_seconds = remaining.total_seconds()
                progress = max(0, min(100, int((remaining_seconds / total_seconds) * 100)))
                self.progress_bar.setValue(progress)
                
                # 根据剩余时间设置样式
                if days <= 7:
                    self._set_warning_style()
                elif days <= 30:
                    self._set_caution_style()
                else:
                    self._set_normal_style()
                    
        except Exception as e:
            self.expiry_label.setText("❓ 无法解析到期时间")
            self.countdown_label.setText(f"原始数据: {expiry_time}")
    
    def _set_normal_style(self):
        """设置正常样式（绿色）"""
        self.countdown_card.setStyleSheet("""
            QFrame#countdownCard {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #e8f5e8, stop:1 #d4edda);
                border: 2px solid #28a745;
                border-radius: 12px;
                padding: 15px;
            }
        """)
        self.progress_bar.setStyleSheet("QProgressBar::chunk { background-color: #28a745; }")
    
    def _set_caution_style(self):
        """设置警告样式（黄色）"""
        self.countdown_card.setStyleSheet("""
            QFrame#countdownCard {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #fff3cd, stop:1 #ffeaa7);
                border: 2px solid #ffc107;
                border-radius: 12px;
                padding: 15px;
            }
        """)
        self.progress_bar.setStyleSheet("QProgressBar::chunk { background-color: #ffc107; }")
    
    def _set_warning_style(self):
        """设置危险样式（红色）"""
        self.countdown_card.setStyleSheet("""
            QFrame#countdownCard {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8d7da, stop:1 #f5c6cb);
                border: 2px solid #dc3545;
                border-radius: 12px;
                padding: 15px;
            }
        """)
        self.progress_bar.setStyleSheet("QProgressBar::chunk { background-color: #dc3545; }")
    
    def _set_expired_style(self):
        """设置过期样式（深红色）"""
        self.countdown_card.setStyleSheet("""
            QFrame#countdownCard {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8d7da, stop:1 #f1b0b7);
                border: 2px solid #721c24;
                border-radius: 12px;
                padding: 15px;
            }
        """)
        self.progress_bar.setStyleSheet("QProgressBar::chunk { background-color: #721c24; }")

    def _setup_styles(self):
        """设置样式"""
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                font-family: 'Microsoft YaHei', 'Segoe UI', Arial, sans-serif;
            }

            QFrame#headerFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #e3f2fd, stop:1 #bbdefb);
                border-radius: 15px;
                padding: 20px;
                margin-bottom: 10px;
            }

            QLabel#successTitle {
                font-size: 24px;
                font-weight: bold;
                color: #1976d2;
                margin: 5px 0;
            }

            QLabel#successSubtitle {
                font-size: 14px;
                color: #666;
                margin-bottom: 10px;
            }

            QFrame#infoCard, QFrame#featuresCard {
                background: white;
                border: 1px solid #e0e0e0;
                border-radius: 12px;
                padding: 20px;
                margin: 5px 0;
            }

            QLabel#cardTitle {
                font-size: 16px;
                font-weight: bold;
                color: #333;
                margin-bottom: 15px;
                padding-bottom: 8px;
                border-bottom: 2px solid #e0e0e0;
            }

            QLabel#infoLabel {
                font-weight: bold;
                color: #555;
                font-size: 13px;
                min-width: 120px;
            }

            QLabel#infoValue {
                color: #333;
                font-size: 13px;
                font-weight: 500;
            }

            QLabel#expiryLabel {
                font-size: 16px;
                font-weight: bold;
                color: #333;
                margin: 10px 0;
            }

            QLabel#countdownLabel {
                font-size: 18px;
                font-weight: bold;
                color: #1976d2;
                margin: 10px 0;
            }

            QProgressBar#expiryProgress {
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: #f0f0f0;
                margin: 10px 0;
            }

            QProgressBar#expiryProgress::chunk {
                background-color: #28a745;
                border-radius: 3px;
            }

            QLabel#featureItem {
                font-size: 14px;
                color: #333;
                padding: 5px 0;
                margin: 2px 0;
            }

            QPushButton#primaryButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4CAF50, stop:1 #45a049);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
                min-width: 120px;
            }

            QPushButton#primaryButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #45a049, stop:1 #3d8b40);
            }

            QPushButton#primaryButton:pressed {
                background: #3d8b40;
            }

            QPushButton#secondaryButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #6c757d, stop:1 #5a6268);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
                min-width: 120px;
            }

            QPushButton#secondaryButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5a6268, stop:1 #495057);
            }

            QPushButton#secondaryButton:pressed {
                background: #495057;
            }
        """)

    def _start_using(self):
        """开始使用按钮点击事件"""
        # 关闭当前窗口，启动主程序
        if self.parent():
            self.parent().accept()
        else:
            self.close()

    def _show_details(self):
        """显示详情按钮点击事件"""
        # 显示详细的许可证信息对话框
        details_dialog = LicenseDetailsDialog(self.license_data, self)
        details_dialog.exec_()

    def update_license_data(self, license_data):
        """更新许可证数据"""
        self.license_data = license_data
        self._update_display()


class LicenseDetailsDialog(QDialog):
    """许可证详情对话框"""

    def __init__(self, license_data, parent=None):
        super().__init__(parent)
        self.license_data = license_data
        self.setWindowTitle("许可证详细信息")
        self.setFixedSize(500, 400)
        self.setModal(True)

        self._setup_ui()
        self._setup_styles()

    def _setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 标题
        title = QLabel("📋 许可证详细信息")
        title.setObjectName("dialogTitle")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # 详细信息
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)

        # 添加所有信息
        info_data = [
            ("🔑 许可证密钥", self.license_data.get('license_key', 'N/A')),
            ("📦 许可证类型", self.license_data.get('license_type', 'N/A')),
            ("📊 当前状态", self.license_data.get('status', 'N/A')),
            ("👤 用户名称", self.license_data.get('user_name', '未设置')),
            ("🏢 公司名称", self.license_data.get('company_name', '未设置')),
            ("📧 邮箱地址", self.license_data.get('user_email', '未设置')),
            ("🔢 最大激活数", str(self.license_data.get('max_activations', 'N/A'))),
            ("📅 创建时间", self.license_data.get('created_at', 'N/A')),
            ("⏰ 到期时间", self.license_data.get('expiry_time', '永久')),
            ("🚀 可用功能", ', '.join(self.license_data.get('features', ['标准功能']))),
        ]

        for label_text, value_text in info_data:
            self._add_detail_row(scroll_layout, label_text, value_text)

        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)

        # 关闭按钮
        close_button = QPushButton("关闭")
        close_button.setObjectName("closeButton")
        close_button.clicked.connect(self.accept)

        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(close_button)
        layout.addLayout(button_layout)

    def _add_detail_row(self, layout, label_text, value_text):
        """添加详情行"""
        row_frame = QFrame()
        row_frame.setObjectName("detailRow")
        row_layout = QHBoxLayout(row_frame)
        row_layout.setContentsMargins(10, 8, 10, 8)

        label = QLabel(label_text)
        label.setObjectName("detailLabel")
        label.setMinimumWidth(100)

        value = QLabel(str(value_text))
        value.setObjectName("detailValue")
        value.setWordWrap(True)

        row_layout.addWidget(label)
        row_layout.addWidget(value, 1)

        layout.addWidget(row_frame)

    def _setup_styles(self):
        """设置样式"""
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
                font-family: 'Microsoft YaHei', 'Segoe UI', Arial, sans-serif;
            }

            QLabel#dialogTitle {
                font-size: 18px;
                font-weight: bold;
                color: #333;
                margin: 10px 0;
            }

            QFrame#detailRow {
                background: white;
                border: 1px solid #e0e0e0;
                border-radius: 6px;
                margin: 2px 0;
            }

            QLabel#detailLabel {
                font-weight: bold;
                color: #555;
                font-size: 12px;
            }

            QLabel#detailValue {
                color: #333;
                font-size: 12px;
            }

            QPushButton#closeButton {
                background: #6c757d;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 20px;
                font-size: 13px;
                font-weight: bold;
            }

            QPushButton#closeButton:hover {
                background: #5a6268;
            }
        """)
