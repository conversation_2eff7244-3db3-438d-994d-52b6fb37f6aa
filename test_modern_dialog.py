#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现代化许可证对话框测试脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from PySide6.QtCore import Qt
from auth.modern_license_dialog import ModernLicenseDialog
from auth.license_manager import LicenseManager


class TestMainWindow(QMainWindow):
    """测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("现代化许可证对话框测试")
        self.setGeometry(100, 100, 400, 200)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 创建测试按钮
        test_button = QPushButton("🚀 打开现代化许可证对话框")
        test_button.setMinimumHeight(50)
        test_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:1 #764ba2);
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #5a6fd8, stop:1 #6a4190);
            }
        """)
        test_button.clicked.connect(self.show_license_dialog)
        
        layout.addWidget(test_button)
    
    def show_license_dialog(self):
        """显示许可证对话框"""
        license_manager = LicenseManager()
        dialog = ModernLicenseDialog(self, license_manager)
        
        result = dialog.exec()
        
        if result == dialog.Accepted:
            print("✅ 许可证验证成功！")
        else:
            print("❌ 许可证验证取消或失败")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    app.setStyle('Fusion')
    
    # 创建主窗口
    window = TestMainWindow()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
