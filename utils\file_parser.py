#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件解析工具
支持TXT、Excel等格式的账户信息文件解析
"""

import logging
import pandas as pd
from pathlib import Path
from typing import List, Dict, Tuple, Optional
import re


class FileParser:
    """文件解析器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def parse_file(self, file_path: str) -> <PERSON>ple[List[Dict], List[str]]:
        """
        解析文件并返回账户信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            Tuple[List[Dict], List[str]]: (成功解析的账户列表, 错误信息列表)
        """
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                return [], [f"文件不存在: {file_path}"]
                
            # 根据文件扩展名选择解析方法
            extension = file_path.suffix.lower()
            
            if extension == '.txt':
                return self._parse_txt_file(file_path)
            elif extension in ['.xlsx', '.xls']:
                return self._parse_excel_file(file_path)
            elif extension == '.csv':
                return self._parse_csv_file(file_path)
            else:
                return [], [f"不支持的文件格式: {extension}"]
                
        except Exception as e:
            self.logger.error(f"解析文件失败: {e}")
            return [], [f"解析文件失败: {str(e)}"]
            
    def _parse_txt_file(self, file_path: Path) -> Tuple[List[Dict], List[str]]:
        """解析TXT文件"""
        accounts = []
        errors = []
        
        try:
            # 尝试不同的编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
            content = None
            
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read()
                    break
                except UnicodeDecodeError:
                    continue
                    
            if content is None:
                return [], ["无法读取文件，请检查文件编码"]
                
            lines = content.strip().split('\n')
            
            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                if not line or line.startswith('#'):  # 跳过空行和注释
                    continue
                    
                account, error = self._parse_account_line(line, line_num)
                if account:
                    accounts.append(account)
                if error:
                    errors.append(error)
                    
        except Exception as e:
            errors.append(f"读取TXT文件失败: {str(e)}")
            
        return accounts, errors
        
    def _parse_excel_file(self, file_path: Path) -> Tuple[List[Dict], List[str]]:
        """解析Excel文件"""
        accounts = []
        errors = []
        
        try:
            # 读取Excel文件
            df = pd.read_excel(file_path, sheet_name=0)  # 读取第一个工作表
            
            # 检查列名
            expected_columns = ['email', 'password', 'client_id', 'refresh_token']
            alternative_columns = {
                'email': ['邮箱', '邮箱地址', 'Email', 'EMAIL', '账号'],
                'password': ['密码', 'Password', 'PASSWORD', 'pwd'],
                'client_id': ['客户端ID', 'ClientID', 'Client_ID', 'client_id'],
                'refresh_token': ['刷新令牌', 'RefreshToken', 'Refresh_Token', 'refresh_token', 'token']
            }
            
            # 标准化列名
            column_mapping = {}
            for expected_col in expected_columns:
                found = False
                for col in df.columns:
                    if col.lower() == expected_col.lower():
                        column_mapping[col] = expected_col
                        found = True
                        break
                    elif col in alternative_columns[expected_col]:
                        column_mapping[col] = expected_col
                        found = True
                        break
                        
                if not found:
                    errors.append(f"未找到必需的列: {expected_col}")
                    
            if len(column_mapping) < 4:
                # 尝试按位置解析
                if len(df.columns) >= 4:
                    column_mapping = {
                        df.columns[0]: 'email',
                        df.columns[1]: 'password', 
                        df.columns[2]: 'client_id',
                        df.columns[3]: 'refresh_token'
                    }
                    errors.append("按列位置解析Excel文件 (第1列=邮箱, 第2列=密码, 第3列=客户端ID, 第4列=刷新令牌)")
                else:
                    return [], ["Excel文件格式不正确，需要至少4列数据"]
                    
            # 重命名列
            df = df.rename(columns=column_mapping)
            
            # 解析每一行
            for index, row in df.iterrows():
                try:
                    # 检查必需字段
                    email = str(row.get('email', '')).strip()
                    password = str(row.get('password', '')).strip()
                    client_id = str(row.get('client_id', '')).strip()
                    refresh_token = str(row.get('refresh_token', '')).strip()
                    
                    if not all([email, password, client_id, refresh_token]):
                        errors.append(f"第{index + 2}行数据不完整")
                        continue
                        
                    # 验证邮箱格式
                    if not self._is_valid_email(email):
                        errors.append(f"第{index + 2}行邮箱格式无效: {email}")
                        continue
                        
                    account = {
                        'email': email,
                        'password': password,
                        'client_id': client_id,
                        'refresh_token': refresh_token,
                        'display_name': email.split('@')[0]  # 使用邮箱前缀作为显示名
                    }
                    
                    accounts.append(account)
                    
                except Exception as e:
                    errors.append(f"第{index + 2}行解析失败: {str(e)}")
                    
        except Exception as e:
            errors.append(f"读取Excel文件失败: {str(e)}")
            
        return accounts, errors
        
    def _parse_csv_file(self, file_path: Path) -> Tuple[List[Dict], List[str]]:
        """解析CSV文件"""
        accounts = []
        errors = []
        
        try:
            # 尝试不同的编码和分隔符
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
            separators = [',', ';', '\t', '|']
            
            df = None
            for encoding in encodings:
                for sep in separators:
                    try:
                        df = pd.read_csv(file_path, encoding=encoding, sep=sep)
                        if len(df.columns) >= 4:  # 至少需要4列
                            break
                    except:
                        continue
                if df is not None and len(df.columns) >= 4:
                    break
                    
            if df is None or len(df.columns) < 4:
                return [], ["无法解析CSV文件，请检查文件格式和编码"]
                
            # 使用Excel解析逻辑处理DataFrame
            return self._parse_dataframe(df)
            
        except Exception as e:
            errors.append(f"读取CSV文件失败: {str(e)}")
            
        return accounts, errors
        
    def _parse_dataframe(self, df: pd.DataFrame) -> Tuple[List[Dict], List[str]]:
        """解析DataFrame数据"""
        accounts = []
        errors = []
        
        # 标准化列名
        expected_columns = ['email', 'password', 'client_id', 'refresh_token']
        
        if len(df.columns) >= 4:
            # 按位置映射列名
            column_mapping = {
                df.columns[0]: 'email',
                df.columns[1]: 'password',
                df.columns[2]: 'client_id', 
                df.columns[3]: 'refresh_token'
            }
            df = df.rename(columns=column_mapping)
            
            # 解析每一行
            for index, row in df.iterrows():
                try:
                    email = str(row.get('email', '')).strip()
                    password = str(row.get('password', '')).strip()
                    client_id = str(row.get('client_id', '')).strip()
                    refresh_token = str(row.get('refresh_token', '')).strip()
                    
                    if not all([email, password, client_id, refresh_token]):
                        errors.append(f"第{index + 2}行数据不完整")
                        continue
                        
                    if not self._is_valid_email(email):
                        errors.append(f"第{index + 2}行邮箱格式无效: {email}")
                        continue
                        
                    account = {
                        'email': email,
                        'password': password,
                        'client_id': client_id,
                        'refresh_token': refresh_token,
                        'display_name': email.split('@')[0]
                    }
                    
                    accounts.append(account)
                    
                except Exception as e:
                    errors.append(f"第{index + 2}行解析失败: {str(e)}")
                    
        return accounts, errors
        
    def _parse_account_line(self, line: str, line_num: int) -> Tuple[Optional[Dict], Optional[str]]:
        """解析单行账户信息"""
        try:
            # 支持多种分隔符
            separators = ['----', '|||', ':::', ';;;', '\t\t', '  ']
            parts = None
            
            for sep in separators:
                if sep in line:
                    parts = [part.strip() for part in line.split(sep)]
                    break
                    
            if parts is None:
                # 尝试正则表达式匹配
                pattern = r'(\S+@\S+\.\S+)\s+(\S+)\s+(\S+)\s+(\S+)'
                match = re.match(pattern, line)
                if match:
                    parts = list(match.groups())
                    
            if parts is None or len(parts) < 4:
                return None, f"第{line_num}行格式错误: {line[:50]}..."
                
            email, password, client_id, refresh_token = parts[:4]
            
            # 验证邮箱格式
            if not self._is_valid_email(email):
                return None, f"第{line_num}行邮箱格式无效: {email}"
                
            account = {
                'email': email,
                'password': password,
                'client_id': client_id,
                'refresh_token': refresh_token,
                'display_name': email.split('@')[0]
            }
            
            return account, None
            
        except Exception as e:
            return None, f"第{line_num}行解析失败: {str(e)}"
            
    def _is_valid_email(self, email: str) -> bool:
        """验证邮箱格式"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
        
    def get_supported_formats(self) -> List[str]:
        """获取支持的文件格式"""
        return [
            "文本文件 (*.txt)",
            "Excel文件 (*.xlsx *.xls)", 
            "CSV文件 (*.csv)",
            "所有支持的文件 (*.txt *.xlsx *.xls *.csv)"
        ]
        
    def create_sample_files(self, output_dir: str = "."):
        """创建示例文件"""
        try:
            output_path = Path(output_dir)
            output_path.mkdir(exist_ok=True)
            
            # 创建TXT示例文件
            txt_content = """# 账户信息示例文件 (TXT格式)
# 格式: email----password----client_id----refresh_token
<EMAIL>----password123----client_id_1----refresh_token_1
<EMAIL>----password456----client_id_2----refresh_token_2
<EMAIL>----password789----client_id_3----refresh_token_3"""
            
            with open(output_path / "账户示例.txt", 'w', encoding='utf-8') as f:
                f.write(txt_content)
                
            # 创建Excel示例文件
            excel_data = {
                'email': ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
                'password': ['password123', 'password456', 'password789'],
                'client_id': ['client_id_1', 'client_id_2', 'client_id_3'],
                'refresh_token': ['refresh_token_1', 'refresh_token_2', 'refresh_token_3']
            }
            
            df = pd.DataFrame(excel_data)
            df.to_excel(output_path / "账户示例.xlsx", index=False)
            
            # 创建CSV示例文件
            df.to_csv(output_path / "账户示例.csv", index=False, encoding='utf-8-sig')
            
            return True, "示例文件创建成功"
            
        except Exception as e:
            return False, f"创建示例文件失败: {str(e)}"
