#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
账户管理对话框
提供完整的邮箱账户管理功能：查看、编辑、删除、测试等
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional

# 兼容不同版本的PySide6的sip导入
try:
    import sip
except ImportError:
    try:
        from PySide6 import sip
    except ImportError:
        # 如果都无法导入，创建一个简单的替代函数
        class MockSip:
            @staticmethod
            def isdeleted(obj):
                try:
                    # 尝试访问对象的一个基本属性来检查是否有效
                    _ = obj.objectName()
                    return False
                except (RuntimeError, AttributeError):
                    return True
        sip = MockSip()

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTableWidget, QTableWidgetItem, QHeaderView, QGroupBox,
    QMessageBox, QMenu, QInputDialog, QProgressBar, QTextEdit,
    QSplitter, QFrame, QCheckBox, QSpinBox, QFormLayout, QLineEdit,
    QWidget, QFileDialog, QApplication, QProgressDialog
)
from PySide6.QtCore import Qt, QTimer, Signal, QThread
from PySide6.QtGui import QFont, QColor, QAction, QMouseEvent

from core.real_account_manager import RealAccountManager, RealAccountConfig
from ui.real_account_config_dialog import RealAccountConfigDialog
from ui.loading_widget import LoadingManager, ProgressStepManager


class PersistentSelectionTableWidget(QTableWidget):
    """
    自定义表格组件，保持选择状态不被点击空白区域清除
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self._preserve_selection = True

    def mousePressEvent(self, event: QMouseEvent):
        """重写鼠标按下事件，防止点击空白区域清除选择"""
        # 获取点击位置的项目
        item = self.itemAt(event.pos())

        if item is None and self._preserve_selection:
            # 点击了空白区域，且启用了选择保持功能
            # 直接忽略这次点击，不调用父类方法
            return
        else:
            # 点击了有效项目，或者禁用了选择保持功能，正常处理
            super().mousePressEvent(event)

    def set_preserve_selection(self, preserve: bool):
        """设置是否保持选择状态"""
        self._preserve_selection = preserve

    def get_preserve_selection(self) -> bool:
        """获取是否保持选择状态"""
        return self._preserve_selection


class AccountTestThread(QThread):
    """账户测试线程"""
    
    progress_updated = Signal(str, str)  # 账户ID和状态消息
    test_completed = Signal(str, bool, str)  # 账户ID、结果、消息
    
    def __init__(self, account_manager: RealAccountManager, account_ids: List[str]):
        super().__init__()
        self.account_manager = account_manager
        self.account_ids = account_ids
        self.logger = logging.getLogger(__name__)
        
    def run(self):
        """运行账户测试"""
        for account_id in self.account_ids:
            try:
                self.progress_updated.emit(account_id, "测试中...")
                
                # 获取客户端并测试连接
                client = self.account_manager.get_client(account_id)
                if client:
                    success = client.connect_and_authenticate_fast()
                    if success:
                        self.test_completed.emit(account_id, True, "连接成功")
                    else:
                        self.test_completed.emit(account_id, False, "连接失败")
                else:
                    self.test_completed.emit(account_id, False, "客户端创建失败")
                    
            except Exception as e:
                self.test_completed.emit(account_id, False, f"测试异常: {str(e)}")


class AccountManagementDialog(QDialog):
    """账户管理对话框"""
    
    accounts_changed = Signal()  # 账户变更信号
    
    def __init__(self, parent=None, real_account_manager: RealAccountManager = None):
        super().__init__(parent)
        self.real_account_manager = real_account_manager
        self.logger = logging.getLogger(__name__)
        
        # 测试线程
        self.test_thread = None

        # 批量选择状态
        self.selected_accounts = set()  # 存储选中的账户ID
        self.is_updating_selection = False  # 防止递归更新

        # 删除操作状态
        self.is_deleting = False
        self.delete_progress_dialog = None

        # 加载管理器
        self.loading_manager = None
        self.progress_manager = None

        # 设置对话框
        self.setWindowTitle("邮箱账户管理 - 微软邮箱批量管理1.0")
        self.setModal(True)
        self.resize(1000, 700)

        # 创建UI
        self.setup_ui()

        # 初始化加载管理器
        self.loading_manager = LoadingManager(self)
        self.progress_manager = ProgressStepManager(self.loading_manager)

        # 加载账户数据
        self.load_accounts()

        # 初始化UI状态
        self.update_selection_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        
        # 标题区域
        self.create_header_section(layout)
        
        # 主要内容区域
        splitter = QSplitter(Qt.Vertical)
        layout.addWidget(splitter)
        
        # 账户列表区域
        self.create_accounts_section(splitter)
        
        # 操作日志区域
        self.create_log_section(splitter)
        
        # 按钮区域
        self.create_button_section(layout)
        
        # 设置分割器比例
        splitter.setSizes([500, 150])
        
    def create_header_section(self, layout):
        """创建标题区域"""
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.StyledPanel)
        header_layout = QVBoxLayout(header_frame)
        
        # 主标题
        title_label = QLabel("🔐 邮箱账户管理")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label)
        
        # 说明文字
        desc_label = QLabel("管理所有邮箱账户：查看、编辑、删除、测试连接等")
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setStyleSheet("color: #666; font-size: 12px;")
        header_layout.addWidget(desc_label)
        
        layout.addWidget(header_frame)
        
    def create_accounts_section(self, splitter):
        """创建账户列表区域"""
        accounts_group = QGroupBox("📋 账户列表")
        accounts_layout = QVBoxLayout(accounts_group)
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        
        # 添加账户按钮
        self.add_account_btn = QPushButton("➕ 添加账户")
        self.add_account_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        self.add_account_btn.clicked.connect(self.add_account)
        toolbar_layout.addWidget(self.add_account_btn)
        
        # 编辑账户按钮
        self.edit_account_btn = QPushButton("✏️ 编辑账户")
        self.edit_account_btn.setEnabled(False)
        self.edit_account_btn.clicked.connect(self.edit_account)
        toolbar_layout.addWidget(self.edit_account_btn)
        
        # 删除账户按钮
        self.delete_account_btn = QPushButton("🗑️ 删除账户")
        self.delete_account_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
        """)
        self.delete_account_btn.setEnabled(False)
        self.delete_account_btn.clicked.connect(self.delete_account)
        toolbar_layout.addWidget(self.delete_account_btn)

        # 添加分隔符（使用空白空间）
        toolbar_layout.addSpacing(20)

        # 测试连接按钮
        self.test_account_btn = QPushButton("🔍 测试连接")
        self.test_account_btn.setEnabled(False)
        self.test_account_btn.clicked.connect(self.test_account)
        toolbar_layout.addWidget(self.test_account_btn)
        
        # 批量测试按钮
        self.batch_test_btn = QPushButton("🔍 批量测试")
        self.batch_test_btn.clicked.connect(self.batch_test_accounts)
        toolbar_layout.addWidget(self.batch_test_btn)
        
        toolbar_layout.addStretch()

        # 选择保持功能切换按钮
        self.preserve_selection_btn = QPushButton("🔒 保持选择")
        self.preserve_selection_btn.setCheckable(True)
        self.preserve_selection_btn.setChecked(True)  # 默认启用
        self.preserve_selection_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:checked {
                background-color: #28a745;
            }
            QPushButton:!checked {
                background-color: #6c757d;
            }
        """)
        self.preserve_selection_btn.clicked.connect(self.toggle_preserve_selection)
        toolbar_layout.addWidget(self.preserve_selection_btn)

        # 刷新按钮
        self.refresh_btn = QPushButton("🔄 刷新")
        self.refresh_btn.clicked.connect(self.load_accounts)
        toolbar_layout.addWidget(self.refresh_btn)
        
        accounts_layout.addLayout(toolbar_layout)
        
        # 账户表格（使用自定义表格类保持选择状态）
        self.accounts_table = PersistentSelectionTableWidget()
        self.accounts_table.setColumnCount(8)
        self.accounts_table.setHorizontalHeaderLabels([
            "选择", "邮箱地址", "显示名称", "状态", "最后同步", "邮件数量", "连接状态", "操作"
        ])
        
        # 设置表格属性
        header = self.accounts_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # 选择列
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # 邮箱地址列自适应
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # 显示名称列
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # 状态列
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # 最后同步列
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # 邮件数量列
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # 连接状态列
        header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # 操作列
        
        self.accounts_table.setAlternatingRowColors(True)
        self.accounts_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.accounts_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.accounts_table.customContextMenuRequested.connect(self.show_context_menu)

        # 添加选择变更事件处理
        self.accounts_table.itemSelectionChanged.connect(self.on_table_selection_changed)
        self.accounts_table.itemSelectionChanged.connect(self.on_selection_changed)

        # 添加全选复选框到表头
        self.select_all_checkbox = QCheckBox()
        self.select_all_checkbox.stateChanged.connect(self.on_select_all_changed)

        # 设置全选复选框的现代化圆形样式
        self.select_all_checkbox.setStyleSheet("""
            QCheckBox {
                spacing: 5px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 9px;
                border: 2px solid #ddd;
                background-color: white;
            }
            QCheckBox::indicator:hover {
                border-color: #007bff;
                background-color: #f8f9fa;
            }
            QCheckBox::indicator:checked {
                background-color: #007bff;
                border-color: #007bff;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xMC42IDEuNEw0LjIgNy44TDEuNCA1IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
            }
            QCheckBox::indicator:checked:hover {
                background-color: #0056b3;
                border-color: #0056b3;
            }
            QCheckBox::indicator:indeterminate {
                background-color: #6c757d;
                border-color: #6c757d;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAiIGhlaWdodD0iMiIgdmlld0JveD0iMCAwIDEwIDIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMCIgaGVpZ2h0PSIyIiBmaWxsPSJ3aGl0ZSIgcng9IjEiLz4KPC9zdmc+);
            }
        """)

        # 创建自定义表头小部件
        header_widget = QWidget()
        header_layout = QHBoxLayout(header_widget)
        header_layout.setContentsMargins(0, 0, 0, 0)
        header_layout.addWidget(self.select_all_checkbox)
        header_layout.setAlignment(Qt.AlignCenter)

        # 将全选复选框设置到表头
        self.accounts_table.setHorizontalHeaderItem(0, QTableWidgetItem(""))
        header.setSectionResizeMode(0, QHeaderView.Fixed)
        header.resizeSection(0, 50)  # 设置选择列宽度

        accounts_layout.addWidget(self.accounts_table)

        # 批量操作工具栏
        self.create_batch_operations_toolbar(accounts_layout)
        
        splitter.addWidget(accounts_group)

    def create_batch_operations_toolbar(self, layout):
        """创建批量操作工具栏"""
        batch_frame = QFrame()
        batch_frame.setFrameStyle(QFrame.StyledPanel)
        batch_frame.setStyleSheet("QFrame { background-color: #f8f9fa; border: 1px solid #dee2e6; }")
        batch_layout = QHBoxLayout(batch_frame)

        # 选择统计和提示信息
        selection_info_widget = QWidget()
        selection_info_layout = QVBoxLayout(selection_info_widget)
        selection_info_layout.setContentsMargins(0, 0, 0, 0)
        selection_info_layout.setSpacing(2)

        self.selection_stats_label = QLabel("已选择: 0 个账户")
        self.selection_stats_label.setStyleSheet("font-weight: bold; color: #495057;")
        selection_info_layout.addWidget(self.selection_stats_label)

        # 添加选择保持提示
        selection_hint_label = QLabel("💡 提示: 点击空白区域不会清除选择，使用'清除选择'按钮来取消选择")
        selection_hint_label.setStyleSheet("font-size: 11px; color: #6c757d; font-style: italic;")
        selection_hint_label.setWordWrap(True)
        selection_info_layout.addWidget(selection_hint_label)

        batch_layout.addWidget(selection_info_widget)

        batch_layout.addStretch()

        # 选择操作按钮组
        selection_group = QWidget()
        selection_layout = QHBoxLayout(selection_group)
        selection_layout.setContentsMargins(0, 0, 0, 0)

        # 全选按钮
        self.select_all_btn = QPushButton("全选")
        self.select_all_btn.clicked.connect(self.select_all_accounts)
        selection_layout.addWidget(self.select_all_btn)

        # 反选按钮
        self.invert_selection_btn = QPushButton("反选")
        self.invert_selection_btn.clicked.connect(self.invert_selection)
        selection_layout.addWidget(self.invert_selection_btn)

        # 清除选择按钮
        self.clear_selection_btn = QPushButton("清除选择")
        self.clear_selection_btn.clicked.connect(self.clear_selection)
        selection_layout.addWidget(self.clear_selection_btn)

        batch_layout.addWidget(selection_group)

        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.VLine)
        separator.setFrameShadow(QFrame.Sunken)
        batch_layout.addWidget(separator)

        # 批量操作按钮组
        operations_group = QWidget()
        operations_layout = QHBoxLayout(operations_group)
        operations_layout.setContentsMargins(0, 0, 0, 0)

        # 批量启用按钮
        self.batch_enable_btn = QPushButton("✅ 批量启用")
        self.batch_enable_btn.setEnabled(False)
        self.batch_enable_btn.clicked.connect(lambda: self.batch_toggle_status(True))
        operations_layout.addWidget(self.batch_enable_btn)

        # 批量禁用按钮
        self.batch_disable_btn = QPushButton("❌ 批量禁用")
        self.batch_disable_btn.setEnabled(False)
        self.batch_disable_btn.clicked.connect(lambda: self.batch_toggle_status(False))
        operations_layout.addWidget(self.batch_disable_btn)

        # 批量删除按钮
        self.batch_delete_btn = QPushButton("🗑️ 批量删除")
        self.batch_delete_btn.setEnabled(False)
        self.batch_delete_btn.setStyleSheet("background-color: #dc3545; color: white;")
        self.batch_delete_btn.clicked.connect(self.batch_delete_accounts)
        operations_layout.addWidget(self.batch_delete_btn)

        # 批量导出按钮
        self.batch_export_btn = QPushButton("📤 批量导出")
        self.batch_export_btn.setEnabled(False)
        self.batch_export_btn.clicked.connect(self.batch_export_accounts)
        operations_layout.addWidget(self.batch_export_btn)

        batch_layout.addWidget(operations_group)

        layout.addWidget(batch_frame)
        
    def create_log_section(self, splitter):
        """创建日志区域"""
        log_group = QGroupBox("📝 操作日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(120)
        self.log_text.setReadOnly(True)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 10px;
            }
        """)
        
        log_layout.addWidget(self.log_text)
        splitter.addWidget(log_group)
        
    def create_button_section(self, layout):
        """创建按钮区域"""
        button_layout = QHBoxLayout()
        
        # 统计信息
        self.stats_label = QLabel("总计: 0 个账户")
        self.stats_label.setStyleSheet("font-weight: bold; color: #495057;")
        button_layout.addWidget(self.stats_label)
        
        button_layout.addStretch()
        
        # 关闭按钮
        self.close_btn = QPushButton("关闭")
        self.close_btn.clicked.connect(self.close)
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)

    def get_account_id_from_row(self, row: int) -> str:
        """从表格行获取账户ID"""
        try:
            email_item = self.accounts_table.item(row, 1)  # 第1列是邮箱地址列
            if email_item is None:
                return None
            return email_item.data(Qt.UserRole)
        except Exception as e:
            self.logger.error(f"获取账户ID失败: {e}")
            return None

    def get_selected_account_ids(self) -> List[str]:
        """获取所有选中的账户ID（统一使用复选框选择状态）"""
        # 统一使用复选框选择状态作为唯一的选择机制
        return list(self.selected_accounts)

    def load_accounts(self):
        """加载账户数据"""
        try:
            accounts = self.real_account_manager.get_all_accounts()
            
            self.accounts_table.setRowCount(len(accounts))
            
            for row, (account_id, account_config) in enumerate(accounts.items()):
                # 选择复选框
                checkbox_widget = QWidget()
                checkbox_layout = QHBoxLayout(checkbox_widget)
                checkbox_layout.setContentsMargins(0, 0, 0, 0)
                checkbox = QCheckBox()
                checkbox.setProperty("account_id", account_id)
                checkbox.setChecked(False)  # 确保初始状态为未选中
                checkbox.stateChanged.connect(self.on_account_checkbox_changed)

                # 设置现代化圆形样式
                checkbox.setStyleSheet("""
                    QCheckBox {
                        spacing: 5px;
                    }
                    QCheckBox::indicator {
                        width: 18px;
                        height: 18px;
                        border-radius: 9px;
                        border: 2px solid #ddd;
                        background-color: white;
                    }
                    QCheckBox::indicator:hover {
                        border-color: #007bff;
                        background-color: #f8f9fa;
                    }
                    QCheckBox::indicator:checked {
                        background-color: #007bff;
                        border-color: #007bff;
                        image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xMC42IDEuNEw0LjIgNy44TDEuNCA1IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
                    }
                    QCheckBox::indicator:checked:hover {
                        background-color: #0056b3;
                        border-color: #0056b3;
                    }
                    QCheckBox::indicator:indeterminate {
                        background-color: #6c757d;
                        border-color: #6c757d;
                        image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAiIGhlaWdodD0iMiIgdmlld0JveD0iMCAwIDEwIDIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMCIgaGVpZ2h0PSIyIiBmaWxsPSJ3aGl0ZSIgcng9IjEiLz4KPC9zdmc+);
                    }
                """)

                # 恢复选择状态（在连接信号之后设置，确保状态同步）
                if account_id in self.selected_accounts:
                    checkbox.blockSignals(True)  # 临时阻止信号
                    checkbox.setChecked(True)
                    checkbox.blockSignals(False)  # 恢复信号

                checkbox_layout.addWidget(checkbox)
                checkbox_layout.setAlignment(Qt.AlignCenter)
                self.accounts_table.setCellWidget(row, 0, checkbox_widget)

                # 邮箱地址
                email_item = QTableWidgetItem(account_config.email)
                email_item.setData(Qt.UserRole, account_id)
                self.accounts_table.setItem(row, 1, email_item)
                
                # 显示名称
                display_name_item = QTableWidgetItem(account_config.display_name)
                self.accounts_table.setItem(row, 2, display_name_item)

                # 状态
                status_text = "✅ 启用" if account_config.enabled else "❌ 禁用"
                status_item = QTableWidgetItem(status_text)
                if account_config.enabled:
                    status_item.setBackground(QColor(200, 255, 200))
                else:
                    status_item.setBackground(QColor(255, 200, 200))
                self.accounts_table.setItem(row, 3, status_item)

                # 最后同步
                last_sync = account_config.last_sync
                if last_sync:
                    sync_text = last_sync.strftime("%Y-%m-%d %H:%M")
                else:
                    sync_text = "从未同步"
                sync_item = QTableWidgetItem(sync_text)
                self.accounts_table.setItem(row, 4, sync_item)

                # 邮件数量
                try:
                    email_count = len(self.real_account_manager.get_account_emails(account_id, "INBOX"))
                except Exception as e:
                    self.logger.warning(f"获取账户 {account_id} 邮件数量失败: {e}")
                    email_count = 0
                count_item = QTableWidgetItem(str(email_count))
                self.accounts_table.setItem(row, 5, count_item)

                # 连接状态
                connection_item = QTableWidgetItem("未测试")
                connection_item.setBackground(QColor(255, 255, 200))
                self.accounts_table.setItem(row, 6, connection_item)
                
                # 操作按钮
                action_widget = QWidget()
                action_layout = QHBoxLayout(action_widget)
                action_layout.setContentsMargins(2, 2, 2, 2)

                # 快速编辑按钮
                quick_edit_btn = QPushButton("编辑")
                quick_edit_btn.setMaximumSize(50, 25)
                quick_edit_btn.clicked.connect(lambda checked, aid=account_id: self.edit_account_by_id(aid))
                action_layout.addWidget(quick_edit_btn)

                # 快速删除按钮
                quick_delete_btn = QPushButton("删除")
                quick_delete_btn.setMaximumSize(50, 25)
                quick_delete_btn.setStyleSheet("background-color: #dc3545; color: white;")
                quick_delete_btn.clicked.connect(lambda checked, aid=account_id: self.delete_account_by_id(aid, show_confirmation=True))
                action_layout.addWidget(quick_delete_btn)

                self.accounts_table.setCellWidget(row, 7, action_widget)
                
            # 更新统计信息
            enabled_count = sum(1 for config in accounts.values() if config.enabled)
            self.stats_label.setText(f"总计: {len(accounts)} 个账户 (启用: {enabled_count}, 禁用: {len(accounts) - enabled_count})")
            
            self.log_message(f"✅ 加载了 {len(accounts)} 个账户")

            # 重置选择状态
            self.selected_accounts.clear()

            # 安全地更新选择UI
            try:
                self.update_selection_ui()
            except (RuntimeError, AttributeError) as e:
                self.logger.debug(f"更新选择UI时出错，忽略: {e}")

        except Exception as e:
            self.logger.error(f"加载账户失败: {e}")
            self.log_message(f"❌ 加载账户失败: {e}")

    def on_account_checkbox_changed(self, state):
        """账户复选框状态变更"""
        if self.is_updating_selection:
            return

        try:
            self.is_updating_selection = True

            checkbox = self.sender()
            # 检查对象是否有效
            if checkbox is None or sip.isdeleted(checkbox):
                return

            account_id = checkbox.property("account_id")
            if not account_id:
                return

            # 更新选择集合（修复：使用 .value 获取枚举的整数值）
            if state == Qt.Checked.value:
                self.selected_accounts.add(account_id)
            elif state == Qt.Unchecked.value:
                self.selected_accounts.discard(account_id)

            # 同步表格行选择状态
            self.sync_table_selection_from_checkboxes()

            # 更新UI状态
            self.update_selection_ui()

        except (RuntimeError, AttributeError) as e:
            # 对象已被删除或无效，忽略此事件
            self.logger.debug(f"复选框对象无效，忽略状态变更: {e}")
        finally:
            self.is_updating_selection = False

    def on_table_selection_changed(self):
        """表格选择变更事件"""
        if self.is_updating_selection:
            return

        try:
            self.is_updating_selection = True

            # 获取当前选中的表格行
            selected_rows = set()
            for item in self.accounts_table.selectedItems():
                selected_rows.add(item.row())

            # 获取当前选中行对应的账户ID
            selected_account_ids = set()
            for row in selected_rows:
                account_id = self.get_account_id_from_row(row)
                if account_id:
                    selected_account_ids.add(account_id)

            # 同步复选框状态：取消所有复选框，然后勾选对应的复选框
            for row in range(self.accounts_table.rowCount()):
                try:
                    checkbox_widget = self.accounts_table.cellWidget(row, 0)
                    if checkbox_widget and not sip.isdeleted(checkbox_widget):
                        checkbox = checkbox_widget.findChild(QCheckBox)
                        if checkbox and not sip.isdeleted(checkbox):
                            account_id = checkbox.property("account_id")
                            if account_id:
                                # 根据表格行选择状态设置复选框
                                should_be_checked = account_id in selected_account_ids
                                if checkbox.isChecked() != should_be_checked:
                                    checkbox.blockSignals(True)
                                    checkbox.setChecked(should_be_checked)
                                    checkbox.blockSignals(False)

                                    # 更新选择集合
                                    if should_be_checked:
                                        self.selected_accounts.add(account_id)
                                    else:
                                        self.selected_accounts.discard(account_id)
                except (RuntimeError, AttributeError):
                    continue

            # 更新UI状态
            self.update_selection_ui()

        finally:
            self.is_updating_selection = False

    def on_select_all_changed(self, state):
        """全选复选框状态变更"""
        if self.is_updating_selection:
            return

        self.is_updating_selection = True

        try:
            if state == Qt.Checked:
                self.select_all_accounts()
            else:
                self.clear_selection()
        finally:
            self.is_updating_selection = False

    def select_all_accounts(self):
        """全选所有账户"""
        self.is_updating_selection = True

        try:
            # 获取所有账户ID并添加到选择集合
            for row in range(self.accounts_table.rowCount()):
                try:
                    checkbox_widget = self.accounts_table.cellWidget(row, 0)
                    if checkbox_widget and not sip.isdeleted(checkbox_widget):
                        checkbox = checkbox_widget.findChild(QCheckBox)
                        if checkbox and not sip.isdeleted(checkbox):
                            account_id = checkbox.property("account_id")
                            if account_id:
                                checkbox.setChecked(True)
                                self.selected_accounts.add(account_id)
                except (RuntimeError, AttributeError):
                    # 对象已被删除，跳过此行
                    continue

            # 同步表格选择状态
            self.sync_table_selection_from_checkboxes()

            if not sip.isdeleted(self.select_all_checkbox):
                self.select_all_checkbox.setChecked(True)
            self.update_selection_ui()

        finally:
            self.is_updating_selection = False

    def invert_selection(self):
        """反选"""
        self.is_updating_selection = True

        try:
            for row in range(self.accounts_table.rowCount()):
                try:
                    checkbox_widget = self.accounts_table.cellWidget(row, 0)
                    if checkbox_widget and not sip.isdeleted(checkbox_widget):
                        checkbox = checkbox_widget.findChild(QCheckBox)
                        if checkbox and not sip.isdeleted(checkbox):
                            account_id = checkbox.property("account_id")
                            if account_id:
                                new_state = not checkbox.isChecked()
                                checkbox.setChecked(new_state)

                                if new_state:
                                    self.selected_accounts.add(account_id)
                                else:
                                    self.selected_accounts.discard(account_id)
                except (RuntimeError, AttributeError):
                    # 对象已被删除，跳过此行
                    continue

            # 同步表格选择状态
            self.sync_table_selection_from_checkboxes()
            self.update_selection_ui()

        finally:
            self.is_updating_selection = False

    def clear_selection(self):
        """清除所有选择"""
        self.is_updating_selection = True

        try:
            # 清除复选框选择
            for row in range(self.accounts_table.rowCount()):
                try:
                    checkbox_widget = self.accounts_table.cellWidget(row, 0)
                    if checkbox_widget and not sip.isdeleted(checkbox_widget):
                        checkbox = checkbox_widget.findChild(QCheckBox)
                        if checkbox and not sip.isdeleted(checkbox):
                            checkbox.setChecked(False)
                except (RuntimeError, AttributeError):
                    # 对象已被删除，跳过此行
                    continue

            # 清除表格行选择
            self.accounts_table.clearSelection()

            # 清除选择集合和全选复选框
            self.selected_accounts.clear()
            if not sip.isdeleted(self.select_all_checkbox):
                self.select_all_checkbox.setChecked(False)
            self.update_selection_ui()

        finally:
            self.is_updating_selection = False

    def toggle_preserve_selection(self):
        """切换选择保持功能"""
        preserve = self.preserve_selection_btn.isChecked()
        self.accounts_table.set_preserve_selection(preserve)

        # 更新按钮文本和样式
        if preserve:
            self.preserve_selection_btn.setText("🔒 保持选择")
            self.preserve_selection_btn.setToolTip("点击空白区域不会清除选择")
        else:
            self.preserve_selection_btn.setText("🔓 允许清除")
            self.preserve_selection_btn.setToolTip("点击空白区域会清除选择（默认行为）")

        # 记录日志
        status = "启用" if preserve else "禁用"
        self.log_message(f"选择保持功能已{status}")

    def sync_table_selection_from_checkboxes(self):
        """根据复选框状态同步表格行选择"""
        try:
            # 清除当前表格选择
            self.accounts_table.clearSelection()

            # 根据复选框选择状态选中对应的表格行
            for row in range(self.accounts_table.rowCount()):
                try:
                    checkbox_widget = self.accounts_table.cellWidget(row, 0)
                    if checkbox_widget and not sip.isdeleted(checkbox_widget):
                        checkbox = checkbox_widget.findChild(QCheckBox)
                        if checkbox and not sip.isdeleted(checkbox):
                            account_id = checkbox.property("account_id")
                            if account_id and account_id in self.selected_accounts:
                                # 选中整行
                                for col in range(self.accounts_table.columnCount()):
                                    item = self.accounts_table.item(row, col)
                                    if item:
                                        item.setSelected(True)
                except (RuntimeError, AttributeError):
                    continue

        except Exception as e:
            self.logger.debug(f"同步表格选择状态失败: {e}")

    def show_delete_progress(self, title: str, message: str, max_value: int = 0):
        """显示删除进度对话框"""
        if self.delete_progress_dialog:
            self.delete_progress_dialog.close()
            self.delete_progress_dialog = None

        # 创建进度对话框
        self.delete_progress_dialog = QProgressDialog(self)
        self.delete_progress_dialog.setWindowTitle(title)
        self.delete_progress_dialog.setLabelText(message)
        self.delete_progress_dialog.setCancelButtonText("取消")
        self.delete_progress_dialog.setRange(0, max_value)
        self.delete_progress_dialog.setValue(0)

        # 设置对话框属性
        self.delete_progress_dialog.setModal(True)
        self.delete_progress_dialog.setMinimumDuration(0)  # 立即显示
        self.delete_progress_dialog.setAutoClose(False)    # 不自动关闭
        self.delete_progress_dialog.setAutoReset(False)    # 不自动重置

        # 设置最小尺寸确保内容可见
        self.delete_progress_dialog.setMinimumSize(400, 120)
        self.delete_progress_dialog.resize(450, 150)

        # 使用简化的样式，避免渲染问题
        self.delete_progress_dialog.setStyleSheet("""
            QProgressDialog {
                background-color: #ffffff;
                border: 2px solid #007bff;
                border-radius: 8px;
                padding: 10px;
            }
            QLabel {
                color: #333333;
                font-size: 12px;
                padding: 5px;
                background-color: transparent;
            }
            QProgressBar {
                border: 1px solid #cccccc;
                border-radius: 4px;
                text-align: center;
                background-color: #f8f9fa;
                height: 20px;
                color: #333333;
            }
            QProgressBar::chunk {
                background-color: #007bff;
                border-radius: 3px;
            }
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-size: 11px;
                min-width: 60px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
            QPushButton:pressed {
                background-color: #545b62;
            }
        """)

        # 强制显示并刷新
        self.delete_progress_dialog.show()
        self.delete_progress_dialog.raise_()
        self.delete_progress_dialog.activateWindow()
        QApplication.processEvents()

        return self.delete_progress_dialog

    def update_delete_progress(self, value: int, message: str = None):
        """更新删除进度"""
        if self.delete_progress_dialog and not sip.isdeleted(self.delete_progress_dialog):
            try:
                # 更新进度值
                self.delete_progress_dialog.setValue(value)

                # 更新消息文本
                if message:
                    self.delete_progress_dialog.setLabelText(message)

                # 强制刷新UI - 多次调用确保更新
                QApplication.processEvents()
                QApplication.processEvents()

                # 确保对话框保持在前台
                self.delete_progress_dialog.raise_()

                # 短暂延迟让UI有时间渲染
                QTimer.singleShot(10, lambda: QApplication.processEvents())

            except (RuntimeError, AttributeError) as e:
                # 对话框可能已被删除，忽略错误
                self.logger.debug(f"更新进度对话框时出错: {e}")
                self.delete_progress_dialog = None

    def hide_delete_progress(self):
        """隐藏删除进度对话框"""
        if self.delete_progress_dialog and not sip.isdeleted(self.delete_progress_dialog):
            try:
                # 设置完成状态
                self.delete_progress_dialog.setValue(self.delete_progress_dialog.maximum())
                QApplication.processEvents()

                # 短暂延迟后关闭，让用户看到完成状态
                QTimer.singleShot(500, self._close_progress_dialog)

            except (RuntimeError, AttributeError) as e:
                self.logger.debug(f"关闭进度对话框时出错: {e}")
                self.delete_progress_dialog = None

    def _close_progress_dialog(self):
        """延迟关闭进度对话框"""
        if self.delete_progress_dialog and not sip.isdeleted(self.delete_progress_dialog):
            try:
                self.delete_progress_dialog.close()
                self.delete_progress_dialog.deleteLater()
                self.delete_progress_dialog = None
                QApplication.processEvents()
            except (RuntimeError, AttributeError) as e:
                self.logger.debug(f"延迟关闭进度对话框时出错: {e}")
                self.delete_progress_dialog = None

    def update_selection_ui(self):
        """更新选择相关的UI状态"""
        try:
            # 获取选中的账户数量（统一使用复选框选择状态）
            selected_count = len(self.selected_accounts)

            # 更新选择统计标签
            if hasattr(self, 'selection_stats_label') and not sip.isdeleted(self.selection_stats_label):
                if selected_count > 0:
                    self.selection_stats_label.setText(f"已选择: {selected_count} 个账户")
                else:
                    self.selection_stats_label.setText("已选择: 0 个账户")

            # 更新批量操作按钮状态
            has_selection = selected_count > 0

            for btn_name in ['batch_enable_btn', 'batch_disable_btn', 'batch_delete_btn', 'batch_export_btn']:
                if hasattr(self, btn_name):
                    btn = getattr(self, btn_name)
                    if btn and not sip.isdeleted(btn):
                        btn.setEnabled(has_selection)

            # 更新单个操作按钮状态
            single_selection = selected_count == 1
            for btn_name in ['edit_account_btn', 'test_account_btn']:
                if hasattr(self, btn_name):
                    btn = getattr(self, btn_name)
                    if not sip.isdeleted(btn):
                        btn.setEnabled(single_selection)

            # 更新删除按钮状态
            if hasattr(self, 'delete_account_btn'):
                btn = getattr(self, 'delete_account_btn')
                if not sip.isdeleted(btn):
                    btn.setEnabled(has_selection)

            # 更新全选复选框状态
            if not self.is_updating_selection and hasattr(self, 'select_all_checkbox') and not sip.isdeleted(self.select_all_checkbox):
                total_count = self.accounts_table.rowCount()
                if selected_count == 0:
                    self.select_all_checkbox.setCheckState(Qt.Unchecked)
                elif selected_count == total_count:
                    self.select_all_checkbox.setCheckState(Qt.Checked)
                else:
                    self.select_all_checkbox.setCheckState(Qt.PartiallyChecked)

        except (RuntimeError, AttributeError) as e:
            # UI对象可能已被删除，忽略更新
            self.logger.debug(f"UI对象无效，跳过选择状态更新: {e}")
            return

    def batch_toggle_status(self, enabled: bool):
        """批量启用/禁用账户"""
        if not self.selected_accounts:
            return

        action_text = "启用" if enabled else "禁用"
        reply = QMessageBox.question(
            self,
            f"批量{action_text}确认",
            f"确定要{action_text} {len(self.selected_accounts)} 个选中的账户吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        success_count = 0
        failed_count = 0

        for account_id in self.selected_accounts:
            try:
                if self.real_account_manager.toggle_account_status(account_id, enabled):
                    success_count += 1
                    account_config = self.real_account_manager.get_account_config(account_id)
                    if account_config:
                        self.log_message(f"✅ {action_text}账户: {account_config.email}")
                else:
                    failed_count += 1

            except Exception as e:
                failed_count += 1
                self.logger.error(f"{action_text}账户 {account_id} 失败: {e}")

        # 刷新表格
        self.load_accounts()

        # 显示结果
        self.log_message(f"📊 批量{action_text}完成 - 成功: {success_count}, 失败: {failed_count}")
        QMessageBox.information(
            self,
            f"批量{action_text}完成",
            f"成功{action_text} {success_count} 个账户\n失败 {failed_count} 个账户"
        )

    def batch_delete_accounts(self):
        """批量删除账户"""
        # 防止重复删除操作
        if self.is_deleting:
            return

        # 使用统一的选择机制获取选中的账户
        selected_account_ids = self.get_selected_account_ids()

        if not selected_account_ids:
            QMessageBox.warning(self, "提示", "请选择要删除的账户（可以点击复选框或选择表格行）")
            return

        # 获取账户邮箱地址用于确认对话框
        account_emails = []
        for account_id in selected_account_ids:
            account_config = self.real_account_manager.get_account_config(account_id)
            if account_config:
                account_emails.append(account_config.email)
            else:
                account_emails.append(f"未知账户({account_id})")

        reply = QMessageBox.warning(
            self,
            "批量删除确认",
            f"确定要删除 {len(selected_account_ids)} 个选中的账户吗？\n\n账户列表：\n" + "\n".join(account_emails) + "\n\n此操作不可撤销！",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        # 设置删除状态
        self.is_deleting = True

        # 禁用相关按钮
        self.batch_delete_btn.setEnabled(False)
        self.batch_enable_btn.setEnabled(False)
        self.batch_disable_btn.setEnabled(False)

        try:
            # 显示进度对话框
            total_accounts = len(selected_account_ids)
            progress = self.show_delete_progress("批量删除账户", f"准备删除 {total_accounts} 个账户...", total_accounts)

            success_count = 0
            failed_count = 0

            # 初始延迟，确保进度对话框完全显示
            QTimer.singleShot(100, lambda: None)
            QApplication.processEvents()

            for i, account_id in enumerate(selected_account_ids):
                # 检查是否取消
                if progress and progress.wasCanceled():
                    self.log_message("⚠️ 用户取消了批量删除操作")
                    break

                try:
                    account_config = self.real_account_manager.get_account_config(account_id)
                    email = account_config.email if account_config else account_id

                    # 更新进度 - 开始删除
                    self.update_delete_progress(i, f"正在删除账户 {i+1}/{total_accounts}: {email}")

                    # 停止同步
                    self.real_account_manager.stop_account_sync(account_id)

                    # 短暂延迟，让用户看到进度更新
                    QTimer.singleShot(50, lambda: None)
                    QApplication.processEvents()

                    # 删除账户
                    if self.real_account_manager.remove_account(account_id):
                        success_count += 1
                        # 从选择集合中移除
                        self.selected_accounts.discard(account_id)
                        self.log_message(f"✅ 删除账户: {email}")

                        # 更新进度 - 删除成功
                        self.update_delete_progress(i + 1, f"✅ 已删除账户 {i+1}/{total_accounts}: {email}")
                    else:
                        failed_count += 1
                        self.log_message(f"❌ 删除账户失败: {email}")

                        # 更新进度 - 删除失败
                        self.update_delete_progress(i + 1, f"❌ 删除失败 {i+1}/{total_accounts}: {email}")

                    # 短暂延迟，让用户看到每个账户的处理结果
                    QTimer.singleShot(200, lambda: None)
                    QApplication.processEvents()

                except Exception as e:
                    failed_count += 1
                    self.logger.error(f"删除账户 {account_id} 失败: {e}")
                    self.log_message(f"❌ 删除账户失败: {e}")

                    # 更新进度 - 异常
                    self.update_delete_progress(i + 1, f"❌ 删除异常 {i+1}/{total_accounts}: {account_id}")
                    QApplication.processEvents()

            # 完成进度
            self.update_delete_progress(total_accounts, "正在更新界面...")
            QApplication.processEvents()

            # 清理选择状态
            self.selected_accounts.clear()

            # 刷新表格
            self.load_accounts()
            self.accounts_changed.emit()
            QApplication.processEvents()

            # 隐藏进度对话框
            self.hide_delete_progress()

            # 显示结果
            self.log_message(f"📊 批量删除完成 - 成功: {success_count}, 失败: {failed_count}")

            if not (progress and progress.wasCanceled()):
                # 延迟显示结果对话框，确保进度对话框已关闭
                QTimer.singleShot(600, lambda: QMessageBox.information(
                    self,
                    "批量删除完成",
                    f"成功删除 {success_count} 个账户\n失败 {failed_count} 个账户"
                ))

        except Exception as e:
            # 确保进度对话框被关闭
            if self.delete_progress_dialog:
                try:
                    self.delete_progress_dialog.close()
                    self.delete_progress_dialog = None
                except:
                    pass

            self.logger.error(f"批量删除过程中出错: {e}")
            QMessageBox.critical(self, "批量删除错误", f"批量删除过程中出错：\n{str(e)}")

        finally:
            # 确保资源清理
            if self.delete_progress_dialog:
                try:
                    self.delete_progress_dialog.close()
                    self.delete_progress_dialog = None
                except:
                    pass

            # 恢复删除状态和按钮
            self.is_deleting = False

            # 延迟更新UI，确保所有操作完成
            QTimer.singleShot(100, self.update_selection_ui)

    def batch_export_accounts(self):
        """批量导出账户配置"""
        if not self.selected_accounts:
            return

        import json

        # 选择保存文件
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "导出账户配置",
            f"accounts_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            "JSON文件 (*.json);;所有文件 (*)"
        )

        if not file_path:
            return

        try:
            export_data = {
                "export_time": datetime.now().isoformat(),
                "account_count": len(self.selected_accounts),
                "accounts": []
            }

            for account_id in self.selected_accounts:
                account_config = self.real_account_manager.get_account_config(account_id)
                if account_config:
                    # 导出账户配置（不包含敏感信息）
                    account_data = {
                        "account_id": account_config.account_id,
                        "email": account_config.email,
                        "display_name": account_config.display_name,
                        "enabled": account_config.enabled,
                        "priority": account_config.priority,
                        "max_retries": account_config.max_retries,
                        "timeout": account_config.timeout,
                        "sync_interval": account_config.sync_interval,
                        "max_emails": account_config.max_emails,
                        "last_sync": account_config.last_sync.isoformat() if account_config.last_sync else None
                    }
                    export_data["accounts"].append(account_data)

            # 保存文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)

            self.log_message(f"✅ 成功导出 {len(export_data['accounts'])} 个账户配置到: {file_path}")
            QMessageBox.information(
                self,
                "导出完成",
                f"成功导出 {len(export_data['accounts'])} 个账户配置\n保存位置: {file_path}"
            )

        except Exception as e:
            self.logger.error(f"导出账户配置失败: {e}")
            self.log_message(f"❌ 导出失败: {e}")
            QMessageBox.critical(self, "导出失败", f"导出账户配置时发生错误:\n{e}")
            
    def on_selection_changed(self):
        """选择变更事件"""
        selected_rows = set()
        for item in self.accounts_table.selectedItems():
            selected_rows.add(item.row())
            
        has_selection = len(selected_rows) > 0
        self.edit_account_btn.setEnabled(has_selection and len(selected_rows) == 1)
        self.delete_account_btn.setEnabled(has_selection)
        self.test_account_btn.setEnabled(has_selection)
        
    def show_context_menu(self, position):
        """显示右键菜单"""
        item = self.accounts_table.itemAt(position)
        if item is None:
            return

        row = item.row()
        email_item = self.accounts_table.item(row, 1)  # 第1列是邮箱地址列
        if email_item is None:
            return
        account_id = email_item.data(Qt.UserRole)
        
        menu = QMenu(self)

        # 📋 复制邮箱地址
        email_text = email_item.text()
        copy_action = QAction("📋 复制邮箱地址", self)
        copy_action.triggered.connect(lambda: self.copy_email_to_clipboard(email_text))
        menu.addAction(copy_action)

        menu.addSeparator()

        # 编辑账户
        edit_action = QAction("✏️ 编辑账户", self)
        edit_action.triggered.connect(lambda: self.edit_account_by_id(account_id))
        menu.addAction(edit_action)
        
        # 删除账户（保留邮件）
        delete_action = QAction("🗑️ 删除账户 (保留邮件)", self)
        delete_action.triggered.connect(lambda: self.delete_account_by_id(account_id))
        menu.addAction(delete_action)

        # 完全删除账户（包括邮件）
        delete_all_action = QAction("💥 完全删除 (含邮件)", self)
        delete_all_action.triggered.connect(lambda: self.delete_account_completely(account_id))
        menu.addAction(delete_all_action)

        menu.addSeparator()
        
        # 测试连接
        test_action = QAction("🔍 测试连接", self)
        test_action.triggered.connect(lambda: self.test_account_by_id(account_id))
        menu.addAction(test_action)
        
        # 启用/禁用账户
        account_config = self.real_account_manager.get_account_config(account_id)
        if account_config:
            if account_config.enabled:
                toggle_action = QAction("❌ 禁用账户", self)
                toggle_action.triggered.connect(lambda: self.toggle_account_status(account_id, False))
            else:
                toggle_action = QAction("✅ 启用账户", self)
                toggle_action.triggered.connect(lambda: self.toggle_account_status(account_id, True))
            menu.addAction(toggle_action)
        
        menu.exec(self.accounts_table.mapToGlobal(position))

    def copy_email_to_clipboard(self, email: str):
        """复制邮箱地址到剪贴板"""
        try:
            from PySide6.QtWidgets import QApplication

            clipboard = QApplication.clipboard()
            clipboard.setText(email)

            # 显示成功提示
            QMessageBox.information(self, "复制成功",
                f"邮箱地址已复制到剪贴板:\n{email}")

            self.logger.info(f"已复制邮箱地址到剪贴板: {email}")

        except Exception as e:
            self.logger.error(f"复制邮箱地址失败: {e}")
            QMessageBox.warning(self, "错误", f"复制邮箱地址失败: {e}")

    def add_account(self):
        """添加账户"""
        try:
            # 显示加载状态
            self.loading_manager.show_loading(
                "🔧 正在打开账户配置对话框...",
                [self.add_account_btn]
            )

            # 延迟显示对话框，让用户看到加载状态
            QTimer.singleShot(500, self._show_add_account_dialog)

        except Exception as e:
            self.loading_manager.show_error("添加账户失败")
            self.logger.error(f"添加账户失败: {e}")
            self.log_message(f"❌ 添加账户失败: {e}")

    def _show_add_account_dialog(self):
        """显示添加账户对话框"""
        try:
            self.loading_manager.hide_loading()

            dialog = RealAccountConfigDialog(self)

            # 显示对话框处理进度
            self.loading_manager.show_loading(
                "⏳ 等待用户配置账户...",
                [self.add_account_btn]
            )

            result = dialog.exec()
            self.loading_manager.hide_loading()

            if result == QDialog.Accepted:
                # 显示后处理进度
                steps = [
                    "🔄 刷新账户列表...",
                    "📡 发送变更通知...",
                    "✅ 添加完成"
                ]

                self.progress_manager.set_steps(steps)
                self.progress_manager.start_progress([self.add_account_btn])

                QTimer.singleShot(300, self._complete_add_account)
            else:
                self.log_message("ℹ️ 用户取消了账户添加")

        except Exception as e:
            self.loading_manager.show_error("显示配置对话框失败")
            self.logger.error(f"添加账户失败: {e}")
            self.log_message(f"❌ 添加账户失败: {e}")

    def _complete_add_account(self):
        """完成账户添加"""
        try:
            # 步骤1: 刷新账户列表
            self.load_accounts()
            self.progress_manager.next_step()

            QTimer.singleShot(500, self._complete_add_account_step2)

        except Exception as e:
            self.progress_manager.cancel_progress()
            self.logger.error(f"刷新账户列表失败: {e}")
            self.log_message(f"❌ 刷新账户列表失败: {e}")

    def _complete_add_account_step2(self):
        """完成账户添加步骤2"""
        try:
            # 步骤2: 发送变更通知
            self.accounts_changed.emit()
            self.progress_manager.next_step()

            QTimer.singleShot(300, self._complete_add_account_final)

        except Exception as e:
            self.progress_manager.cancel_progress()
            self.logger.error(f"发送变更通知失败: {e}")
            self.log_message(f"❌ 发送变更通知失败: {e}")

    def _complete_add_account_final(self):
        """完成账户添加最终步骤"""
        self.progress_manager.complete_progress("🎉 账户添加成功！")
        self.log_message("✅ 账户添加成功")
            
    def edit_account(self):
        """编辑选中的账户"""
        # 优先使用复选框选择的账户
        if len(self.selected_accounts) == 1:
            account_id = list(self.selected_accounts)[0]
            self.edit_account_by_id(account_id)
            return
        elif len(self.selected_accounts) > 1:
            QMessageBox.warning(self, "提示", "请只选择一个账户进行编辑")
            return

        # 如果没有复选框选择，尝试使用表格行选择
        selected_rows = set()
        for item in self.accounts_table.selectedItems():
            selected_rows.add(item.row())

        if len(selected_rows) != 1:
            QMessageBox.warning(self, "提示", "请选择一个账户进行编辑（可以点击复选框或选择表格行）")
            return

        row = list(selected_rows)[0]
        account_id = self.get_account_id_from_row(row)
        if account_id:
            self.edit_account_by_id(account_id)
        else:
            QMessageBox.warning(self, "错误", "无法获取账户信息")
        
    def edit_account_by_id(self, account_id: str):
        """根据ID编辑账户"""
        try:
            account_config = self.real_account_manager.get_account_config(account_id)
            if not account_config:
                QMessageBox.warning(self, "错误", "账户不存在")
                return
                
            dialog = RealAccountConfigDialog(self, account_config)
            if dialog.exec() == QDialog.Accepted:
                self.load_accounts()
                self.accounts_changed.emit()
                self.log_message(f"✅ 账户 {account_config.email} 编辑成功")
                
        except Exception as e:
            self.logger.error(f"编辑账户失败: {e}")
            self.log_message(f"❌ 编辑账户失败: {e}")
            
    def delete_account(self):
        """删除选中的账户"""
        account_ids = self.get_selected_account_ids()

        if len(account_ids) == 0:
            QMessageBox.warning(self, "提示", "请选择要删除的账户（可以点击复选框或选择表格行）")
            return

        # 获取账户邮箱地址用于确认对话框
        account_emails = []
        for account_id in account_ids:
            account_config = self.real_account_manager.get_account_config(account_id)
            if account_config:
                account_emails.append(account_config.email)
            else:
                account_emails.append(f"未知账户({account_id})")
            
        # 确认删除
        if len(account_ids) == 1:
            reply = QMessageBox.question(
                self, "确认删除",
                f"确定要删除账户 '{account_emails[0]}' 吗？\n\n"
                f"📧 邮件数据将被保留，重新添加账户时可快速恢复\n"
                f"⚙️ 仅删除账户配置信息\n\n"
                f"此操作不可撤销！",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
        else:
            reply = QMessageBox.question(
                self, "确认删除",
                f"确定要删除 {len(account_ids)} 个账户吗？\n\n"
                f"账户列表：\n" + "\n".join(account_emails) + "\n\n"
                f"📧 邮件数据将被保留，重新添加账户时可快速恢复\n"
                f"⚙️ 仅删除账户配置信息\n\n"
                f"此操作不可撤销！",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
        if reply == QMessageBox.Yes:
            # 单个删除使用带进度的删除方法
            if len(account_ids) == 1:
                self.delete_account_by_id(account_ids[0], show_confirmation=False, show_progress=True)
            else:
                # 多个删除使用批量删除的进度显示
                success_count = 0
                failed_count = 0

                # 显示进度对话框
                total_accounts = len(account_ids)
                progress = self.show_delete_progress("删除账户", f"正在删除 {total_accounts} 个账户...", total_accounts)

                try:
                    for i, account_id in enumerate(account_ids):
                        # 检查是否取消
                        if progress.wasCanceled():
                            self.log_message("⚠️ 用户取消了删除操作")
                            break

                        try:
                            account_config = self.real_account_manager.get_account_config(account_id)
                            email = account_config.email if account_config else account_id

                            # 更新进度
                            self.update_delete_progress(i, f"正在删除账户 {i+1}/{total_accounts}: {email}")

                            # 停止同步
                            self.real_account_manager.stop_account_sync(account_id)

                            # 默认保留邮件数据，只删除账户配置
                            if self.real_account_manager.remove_account_config_only(account_id):
                                success_count += 1
                                # 从选择集合中移除
                                self.selected_accounts.discard(account_id)
                                self.log_message(f"✅ 删除账户配置: {email} (邮件数据已保留)")
                            else:
                                failed_count += 1
                                self.log_message(f"❌ 删除账户失败: {email}")
                        except Exception as e:
                            failed_count += 1
                            self.logger.error(f"删除账户 {account_id} 失败: {e}")
                            self.log_message(f"❌ 删除账户失败: {e}")

                    # 完成进度
                    self.update_delete_progress(total_accounts, "正在更新界面...")

                    # 刷新表格
                    self.load_accounts()
                    self.accounts_changed.emit()

                    # 隐藏进度对话框
                    self.hide_delete_progress()

                    # 显示结果
                    if not progress.wasCanceled():
                        QMessageBox.information(
                            self,
                            "删除完成",
                            f"成功删除 {success_count} 个账户\n失败 {failed_count} 个账户"
                        )

                except Exception as e:
                    self.hide_delete_progress()
                    self.logger.error(f"删除过程中出错: {e}")
                    QMessageBox.critical(self, "删除错误", f"删除过程中出错：\n{str(e)}")
                
    def delete_account_by_id(self, account_id: str, show_confirmation: bool = True, show_progress: bool = True):
        """根据ID删除账户"""
        try:
            account_config = self.real_account_manager.get_account_config(account_id)
            if not account_config:
                self.log_message(f"❌ 账户不存在: {account_id}")
                if show_confirmation:
                    QMessageBox.warning(self, "错误", f"账户不存在: {account_id}")
                return False

            # 可选的确认删除
            if show_confirmation:
                # 获取邮件数量用于确认提示
                try:
                    email_count = len(self.real_account_manager.get_account_emails(account_id, "INBOX"))
                except Exception:
                    email_count = 0

                confirmation_text = f"确定要删除账户 '{account_config.email}' 吗？\n\n"
                confirmation_text += f"此操作将同时删除该账户的所有邮件数据（约 {email_count} 封邮件）\n\n"
                confirmation_text += "此操作不可撤销！"

                reply = QMessageBox.question(
                    self,
                    "确认删除账户和邮件数据",
                    confirmation_text,
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )

                if reply != QMessageBox.Yes:
                    return False

            # 显示进度对话框
            if show_progress:
                progress = self.show_delete_progress("删除账户", f"正在删除账户 '{account_config.email}'...", 3)

                try:
                    # 步骤1：停止同步
                    self.update_delete_progress(1, f"正在停止账户 '{account_config.email}' 的同步...")
                    self.real_account_manager.stop_account_sync(account_id)

                    # 步骤2：删除账户
                    self.update_delete_progress(2, f"正在删除账户 '{account_config.email}'...")
                    success = self.real_account_manager.remove_account(account_id)

                    # 步骤3：更新界面
                    self.update_delete_progress(3, "正在更新界面...")
                    if success:
                        # 从选择集合中移除（如果存在）
                        self.selected_accounts.discard(account_id)

                        self.load_accounts()
                        self.accounts_changed.emit()
                        self.log_message(f"✅ 账户 {account_config.email} 删除成功")

                        self.hide_delete_progress()

                        if show_confirmation:
                            QMessageBox.information(self, "删除成功", f"账户 '{account_config.email}' 已成功删除")
                        return True
                    else:
                        self.hide_delete_progress()
                        self.log_message(f"❌ 账户 {account_config.email} 删除失败")
                        if show_confirmation:
                            QMessageBox.warning(self, "删除失败", f"删除账户 '{account_config.email}' 失败")
                        return False

                except Exception as e:
                    self.hide_delete_progress()
                    raise e
            else:
                # 不显示进度的简单删除
                self.real_account_manager.stop_account_sync(account_id)

                # 默认保留邮件数据，只删除账户配置
                if self.real_account_manager.remove_account_config_only(account_id):
                    self.selected_accounts.discard(account_id)
                    self.load_accounts()
                    self.accounts_changed.emit()
                    self.log_message(f"✅ 账户配置 {account_config.email} 删除成功 (邮件数据已保留)")
                    return True
                else:
                    self.log_message(f"❌ 账户 {account_config.email} 删除失败")
                    return False

        except Exception as e:
            if show_progress:
                self.hide_delete_progress()
            self.logger.error(f"删除账户失败: {e}")
            self.log_message(f"❌ 删除账户失败: {e}")
            if show_confirmation:
                QMessageBox.critical(self, "删除错误", f"删除账户时出错：\n{str(e)}")
            return False

    def delete_account_completely(self, account_id: str):
        """完全删除账户（包括邮件数据）"""
        try:
            account_config = self.real_account_manager.get_account_config(account_id)
            if not account_config:
                QMessageBox.warning(self, "错误", "账户不存在")
                return

            # 特殊确认对话框
            reply = QMessageBox.question(
                self, "⚠️ 危险操作 - 完全删除",
                f"⚠️ 警告：您即将完全删除账户 '{account_config.email}'\n\n"
                f"🚨 此操作将删除：\n"
                f"   • 账户配置信息\n"
                f"   • 所有邮件数据\n"
                f"   • 所有文件夹信息\n\n"
                f"💀 删除后无法恢复，重新添加账户需要重新下载所有邮件！\n\n"
                f"确定要继续吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # 二次确认
            reply2 = QMessageBox.question(
                self, "最终确认",
                f"最后确认：真的要完全删除账户 '{account_config.email}' 及其所有数据吗？\n\n"
                f"此操作不可撤销！",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply2 != QMessageBox.Yes:
                return

            # 执行完全删除
            self.real_account_manager.stop_account_sync(account_id)

            if self.real_account_manager.remove_account_with_emails(account_id):
                self.selected_accounts.discard(account_id)
                self.load_accounts()
                self.accounts_changed.emit()
                self.log_message(f"💥 账户 {account_config.email} 已完全删除（包括所有邮件数据）")
                QMessageBox.information(self, "删除完成", f"账户 {account_config.email} 已完全删除")
            else:
                self.log_message(f"❌ 完全删除账户失败: {account_config.email}")
                QMessageBox.critical(self, "删除失败", f"完全删除账户失败: {account_config.email}")

        except Exception as e:
            self.logger.error(f"完全删除账户失败: {e}")
            self.log_message(f"❌ 完全删除账户失败: {e}")
            QMessageBox.critical(self, "删除错误", f"完全删除账户时出错：\n{str(e)}")
            
    def test_account(self):
        """测试选中账户的连接"""
        account_ids = self.get_selected_account_ids()

        if len(account_ids) == 0:
            QMessageBox.warning(self, "提示", "请选择要测试的账户（可以点击复选框或选择表格行）")
            return

        self.start_account_test(account_ids)
        
    def test_account_by_id(self, account_id: str):
        """根据ID测试账户连接"""
        self.start_account_test([account_id])
        
    def batch_test_accounts(self):
        """批量测试所有账户"""
        accounts = self.real_account_manager.get_all_accounts()
        account_ids = list(accounts.keys())
        
        if not account_ids:
            QMessageBox.information(self, "提示", "没有可测试的账户")
            return
            
        self.start_account_test(account_ids)
        
    def start_account_test(self, account_ids: List[str]):
        """启动账户测试"""
        try:
            if self.test_thread and self.test_thread.isRunning():
                QMessageBox.warning(self, "提示", "测试正在进行中，请稍候...")
                return
                
            self.log_message(f"🔍 开始测试 {len(account_ids)} 个账户的连接...")
            
            # 重置连接状态显示
            for row in range(self.accounts_table.rowCount()):
                email_item = self.accounts_table.item(row, 0)
                account_id = email_item.data(Qt.UserRole)
                if account_id in account_ids:
                    connection_item = self.accounts_table.item(row, 5)
                    connection_item.setText("测试中...")
                    connection_item.setBackground(QColor(255, 255, 200))
                    
            # 启动测试线程
            self.test_thread = AccountTestThread(self.real_account_manager, account_ids)
            self.test_thread.progress_updated.connect(self.on_test_progress)
            self.test_thread.test_completed.connect(self.on_test_completed)
            self.test_thread.start()
            
        except Exception as e:
            self.logger.error(f"启动账户测试失败: {e}")
            self.log_message(f"❌ 启动账户测试失败: {e}")
            
    def on_test_progress(self, account_id: str, message: str):
        """测试进度更新"""
        # 更新表格中的连接状态
        for row in range(self.accounts_table.rowCount()):
            email_item = self.accounts_table.item(row, 0)
            if email_item.data(Qt.UserRole) == account_id:
                connection_item = self.accounts_table.item(row, 5)
                connection_item.setText(message)
                break
                
    def on_test_completed(self, account_id: str, success: bool, message: str):
        """测试完成"""
        # 更新表格中的连接状态
        for row in range(self.accounts_table.rowCount()):
            email_item = self.accounts_table.item(row, 0)
            if email_item.data(Qt.UserRole) == account_id:
                connection_item = self.accounts_table.item(row, 5)
                if success:
                    connection_item.setText("✅ " + message)
                    connection_item.setBackground(QColor(200, 255, 200))
                else:
                    connection_item.setText("❌ " + message)
                    connection_item.setBackground(QColor(255, 200, 200))
                break
                
        # 记录日志
        account_config = self.real_account_manager.get_account_config(account_id)
        if account_config:
            if success:
                self.log_message(f"✅ {account_config.email} 连接测试成功")
            else:
                self.log_message(f"❌ {account_config.email} 连接测试失败: {message}")
                
    def toggle_account_status(self, account_id: str, enabled: bool):
        """切换账户启用状态"""
        try:
            account_config = self.real_account_manager.get_account_config(account_id)
            if not account_config:
                return
                
            account_config.enabled = enabled
            self.real_account_manager.save_account_config(account_id, account_config)
            
            self.load_accounts()
            self.accounts_changed.emit()
            
            status_text = "启用" if enabled else "禁用"
            self.log_message(f"✅ 账户 {account_config.email} 已{status_text}")
            
        except Exception as e:
            self.logger.error(f"切换账户状态失败: {e}")
            self.log_message(f"❌ 切换账户状态失败: {e}")
            
    def log_message(self, message: str):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        self.log_text.append(formatted_message)
        
        # 自动滚动到底部
        cursor = self.log_text.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.log_text.setTextCursor(cursor)
        
    def closeEvent(self, event):
        """关闭事件"""
        if self.test_thread and self.test_thread.isRunning():
            self.test_thread.terminate()
            self.test_thread.wait()
        event.accept()
