#!/bin/bash
# 安全的一键部署脚本 - 避免防火墙锁定问题

set -e  # 遇到错误立即退出

echo "🚀 开始安全部署许可证服务器..."

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请使用root用户运行此脚本"
    exit 1
fi

# 1. 首先配置防火墙（最重要的安全步骤）
echo "🔥 配置防火墙（防止SSH锁定）..."
if command -v ufw &> /dev/null; then
    echo "   允许SSH端口（防止锁定）..."
    ufw allow ssh
    ufw allow 22/tcp
    
    echo "   允许Web服务端口..."
    ufw allow 80/tcp
    ufw allow 443/tcp
    
    echo "   允许应用端口..."
    ufw allow 8080/tcp
    
    echo "   启用防火墙..."
    ufw --force enable
    
    echo "✅ 防火墙配置完成"
    ufw status
else
    echo "⚠️  UFW未安装，跳过防火墙配置"
fi

# 2. 更新系统
echo "📦 更新系统包..."
apt update

# 3. 安装必要软件
echo "🔧 安装必要软件..."
apt install -y python3 python3-pip python3-venv git nginx supervisor certbot python3-certbot-nginx

# 4. 创建Python虚拟环境
echo "🐍 创建Python虚拟环境..."
python3 -m venv venv
source venv/bin/activate

# 5. 升级pip并安装依赖
echo "📚 安装Python依赖..."
pip install --upgrade pip
pip install -r requirements.txt

# 6. 设置环境配置
echo "⚙️  设置环境配置..."
if [ ! -f ".env" ]; then
    cp .env.example .env
    echo "⚠️  请编辑 .env 文件设置正确的配置值"
    echo "   特别是SECRET_KEY、JWT_SECRET_KEY和ADMIN_PASSWORD"
fi

# 7. 初始化数据库
echo "🗄️  初始化数据库..."
python simple_init.py

# 8. 设置文件权限
echo "🔒 设置文件权限..."
chown -R www-data:www-data .
chmod +x run.py
chmod +x simple_init.py

# 9. 创建systemd服务
echo "🔧 创建systemd服务..."
cat > /etc/systemd/system/license-server.service << 'EOF'
[Unit]
Description=License Server
After=network.target

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory=/opt/license-server
Environment=PATH=/opt/license-server/venv/bin
EnvironmentFile=/opt/license-server/.env
ExecStart=/opt/license-server/venv/bin/gunicorn --config gunicorn.conf.py app:create_app()
ExecReload=/bin/kill -s HUP $MAINPID
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 10. 启动服务
echo "🚀 启动许可证服务器..."
systemctl daemon-reload
systemctl enable license-server
systemctl start license-server

# 11. 检查服务状态
echo "📊 检查服务状态..."
sleep 3
systemctl status license-server --no-pager

# 12. 配置Nginx
echo "🌐 配置Nginx反向代理..."
cat > /etc/nginx/sites-available/license-server << EOF
server {
    listen 80;
    server_name ka.915277.xyz;

    # 重定向HTTP到HTTPS
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name ka.915277.xyz;

    # SSL证书配置（稍后通过certbot自动配置）
    # ssl_certificate /etc/letsencrypt/live/ka.915277.xyz/fullchain.pem;
    # ssl_certificate_key /etc/letsencrypt/live/ka.915277.xyz/privkey.pem;

    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_set_header X-Forwarded-Host \$host;
        proxy_set_header X-Forwarded-Port \$server_port;
    }
}
EOF

# 启用Nginx站点
ln -sf /etc/nginx/sites-available/license-server /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default

# 测试Nginx配置
nginx -t

# 重启Nginx
systemctl restart nginx
systemctl enable nginx

# 13. 配置SSL证书
echo "🔒 配置SSL证书..."
echo "   正在申请Let's Encrypt SSL证书..."
if certbot --nginx -d ka.915277.xyz --non-interactive --agree-tos --email <EMAIL> --redirect; then
    echo "   ✅ SSL证书配置成功"
else
    echo "   ⚠️  SSL证书配置失败，将使用HTTP模式"
    # 如果SSL失败，回退到HTTP配置
    cat > /etc/nginx/sites-available/license-server << 'EOF'
server {
    listen 80;
    server_name ka.915277.xyz;

    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
EOF
    systemctl reload nginx
fi

# 14. 最终测试
echo "🧪 进行最终测试..."
sleep 2

echo "   测试本地API..."
if curl -s http://localhost:8080/api/v1/ping > /dev/null; then
    echo "   ✅ 本地API测试成功"
else
    echo "   ❌ 本地API测试失败"
fi

echo "   测试域名API..."
if curl -s https://ka.915277.xyz/api/v1/ping > /dev/null; then
    echo "   ✅ HTTPS域名API测试成功"
elif curl -s http://ka.915277.xyz/api/v1/ping > /dev/null; then
    echo "   ✅ HTTP域名API测试成功"
else
    echo "   ❌ 域名API测试失败"
fi

# 显示部署信息
echo ""
echo "🎉 许可证服务器部署完成！"
echo ""
echo "📋 服务信息:"
echo "   域名: ka.915277.xyz"
echo "   HTTPS地址: https://ka.915277.xyz"
echo "   HTTP地址: http://ka.915277.xyz (自动重定向到HTTPS)"
echo "   管理后台: https://ka.915277.xyz/admin"
echo "   API地址: https://ka.915277.xyz/api/v1"
echo ""
echo "🔧 管理命令:"
echo "   查看状态: systemctl status license-server"
echo "   查看日志: journalctl -u license-server -f"
echo "   重启服务: systemctl restart license-server"
echo ""
echo "🔑 默认登录信息:"
echo "   管理员用户: admin"
echo "   密码: 请查看 .env 文件中的 ADMIN_PASSWORD"
echo ""
echo "⚠️  重要提醒:"
echo "   1. 防火墙已安全配置并启用"
echo "   2. 请编辑 .env 文件设置安全的密码和密钥"
echo "   3. 建议配置SSL证书以启用HTTPS"
echo ""
