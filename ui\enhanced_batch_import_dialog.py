#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的批量账号导入对话框
提供优化的批量导入界面和导入后的快速操作选项
"""

import logging
import threading
import time
from datetime import datetime
from typing import Dict, List, Optional

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QProgressBar, QTableWidget, QTableWidgetItem, QHeaderView,
    QGroupBox, QTextEdit, QSplitter, QWidget, QFrame,
    QMessageBox, QCheckBox, QTabWidget, QFormLayout,
    QSpinBox, QComboBox, QLineEdit, QFileDialog, QRadioButton, QButtonGroup
)
from PySide6.QtCore import Qt, QTimer, Signal, QThread
from PySide6.QtGui import QFont, QColor, QPalette

from core.real_account_manager import RealAccountManager
from utils.batch_real_account_importer import BatchRealAccountImporter
from utils.file_parser import FileParser
from ui.loading_widget import LoadingManager, ProgressStepManager


class PostImportActionThread(QThread):
    """导入后操作线程"""
    
    progress_updated = Signal(int, str)  # 进度和消息
    action_completed = Signal(str, bool)  # 操作名称和结果
    
    def __init__(self, action_type: str, account_manager: RealAccountManager, account_ids: List[str]):
        super().__init__()
        self.action_type = action_type
        self.account_manager = account_manager
        self.account_ids = account_ids
        self.logger = logging.getLogger(__name__)
        
    def run(self):
        """执行导入后操作"""
        try:
            if self.action_type == "login_test":
                self._test_login()
            elif self.action_type == "sync_emails":
                self._sync_emails()
                
        except Exception as e:
            self.logger.error(f"导入后操作失败: {e}")
            self.action_completed.emit(self.action_type, False)
            
    def _test_login(self):
        """测试登录"""
        total = len(self.account_ids)
        success_count = 0

        self.logger.info(f"开始测试 {total} 个新导入账户的登录连接")

        for i, account_id in enumerate(self.account_ids):
            self.progress_updated.emit(
                int((i + 1) / total * 100),
                f"正在测试账户 {i + 1}/{total} 的登录..."
            )

            try:
                # 获取账户信息用于日志
                account = self.account_manager.accounts.get(account_id)
                email = account.email if account else account_id

                self.logger.info(f"测试账户登录: {email}")

                client = self.account_manager.get_client(account_id)
                if client and client.connect_and_authenticate_fast():
                    success_count += 1
                    self.logger.info(f"✅ 账户 {email} 登录测试成功")
                else:
                    self.logger.warning(f"❌ 账户 {email} 登录测试失败")

            except Exception as e:
                email = account.email if 'account' in locals() and account else account_id
                self.logger.warning(f"账户 {email} 登录测试异常: {e}")

            time.sleep(0.1)  # 避免过快请求

        self.logger.info(f"登录测试完成: {success_count}/{total} 个账户测试成功")
        self.action_completed.emit("login_test", True)
        
    def _sync_emails(self):
        """同步邮件"""
        total = len(self.account_ids)
        
        for i, account_id in enumerate(self.account_ids):
            self.progress_updated.emit(
                int((i + 1) / total * 100),
                f"正在同步账户 {i + 1}/{total} 的邮件..."
            )
            
            try:
                self.account_manager.start_account_sync(account_id)
                
            except Exception as e:
                self.logger.warning(f"账户 {account_id} 邮件同步启动失败: {e}")
                
            time.sleep(0.5)  # 给每个账户一些启动时间
            
        self.action_completed.emit("sync_emails", True)


class EnhancedBatchImportDialog(QDialog):
    """增强的批量账号导入对话框"""
    
    import_completed = Signal(int)  # 导入完成信号，传递导入数量
    
    def __init__(self, parent=None, real_account_manager: RealAccountManager = None):
        super().__init__(parent)
        self.real_account_manager = real_account_manager
        self.logger = logging.getLogger(__name__)
        
        # 导入状态
        self.imported_accounts = []
        self.is_importing = False
        self.post_action_thread = None

        # 导入统计
        self.import_success_count = 0
        self.import_failed_count = 0

        # 解析统计
        self.parsed_valid_count = 0
        self.parsed_invalid_count = 0
        self.parsed_duplicate_count = 0

        # 新导入的账户跟踪
        self.newly_imported_accounts = []  # 存储新导入的账户信息

        # 文件解析器
        self.file_parser = FileParser()

        # 设置对话框
        self.setWindowTitle("批量账号导入 - 微软邮箱批量管理1.0")
        self.setModal(True)
        self.resize(900, 650)

        # 创建UI
        self.setup_ui()

        # 初始化加载状态管理器
        self.loading_manager = LoadingManager(self)
        self.progress_manager = ProgressStepManager(self.loading_manager)
        
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        
        # 标题区域
        self.create_header_section(layout)
        
        # 主要内容区域
        tab_widget = QTabWidget()
        layout.addWidget(tab_widget)
        
        # 导入选项卡
        self.create_import_tab(tab_widget)
        
        # 导入后操作选项卡
        self.create_post_actions_tab(tab_widget)
        
        # 进度和日志区域
        self.create_progress_section(layout)
        
        # 按钮区域
        self.create_button_section(layout)
        
    def create_header_section(self, layout):
        """创建标题区域"""
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.StyledPanel)
        header_layout = QVBoxLayout(header_frame)
        
        # 主标题
        title_label = QLabel("📥 批量账号导入")
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label)
        
        # 说明文字
        desc_label = QLabel("批量导入微软邮件账户，支持导入后的快速操作选项")
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setStyleSheet("color: #666; font-size: 12px; margin-bottom: 10px;")
        header_layout.addWidget(desc_label)
        
        layout.addWidget(header_frame)
        
    def create_import_tab(self, tab_widget):
        """创建导入选项卡"""
        import_widget = QWidget()
        import_layout = QVBoxLayout(import_widget)
        
        # 导入方式选择
        import_group = QGroupBox("📋 导入方式")
        import_group_layout = QVBoxLayout(import_group)
        
        # 导入方式单选按钮组
        # 创建按钮组确保互斥
        self.import_method_group = QButtonGroup()

        # 文本导入选项
        text_import_layout = QHBoxLayout()
        self.text_import_radio = QRadioButton("文本导入")
        self.text_import_radio.setChecked(True)
        self.import_method_group.addButton(self.text_import_radio)
        text_import_layout.addWidget(self.text_import_radio)
        text_import_layout.addWidget(QLabel("直接粘贴账户信息"))
        text_import_layout.addStretch()
        import_group_layout.addLayout(text_import_layout)

        # 文件导入选项
        file_import_layout = QHBoxLayout()
        self.file_import_radio = QRadioButton("文件导入 (支持TXT、Excel、CSV)")
        self.import_method_group.addButton(self.file_import_radio)
        file_import_layout.addWidget(self.file_import_radio)
        self.file_path_edit = QLineEdit()
        self.file_path_edit.setPlaceholderText("选择包含账户信息的文件 (*.txt, *.xlsx, *.xls, *.csv)...")
        self.file_path_edit.setEnabled(False)
        file_import_layout.addWidget(self.file_path_edit)

        self.browse_btn = QPushButton("浏览...")
        self.browse_btn.setEnabled(False)
        self.browse_btn.clicked.connect(self.browse_file)
        file_import_layout.addWidget(self.browse_btn)

        # 创建示例文件按钮
        self.create_sample_btn = QPushButton("创建示例文件")
        self.create_sample_btn.setEnabled(False)
        self.create_sample_btn.setToolTip("创建TXT、Excel、CSV格式的示例文件")
        self.create_sample_btn.clicked.connect(self.create_sample_files)
        file_import_layout.addWidget(self.create_sample_btn)

        import_group_layout.addLayout(file_import_layout)
        
        # 连接单选框事件
        self.text_import_radio.toggled.connect(self.on_import_method_changed)
        self.file_import_radio.toggled.connect(self.on_import_method_changed)
        
        import_layout.addWidget(import_group)
        
        # 账户信息输入区域
        input_group = QGroupBox("📝 账户信息")
        input_layout = QVBoxLayout(input_group)
        
        # 格式说明
        format_label = QLabel("支持格式: 文本输入 | TXT文件 | Excel文件(.xlsx/.xls) | CSV文件")
        format_label.setStyleSheet("color: #666; font-size: 11px; margin-bottom: 5px;")
        input_layout.addWidget(format_label)

        # 详细格式说明
        detail_label = QLabel("文本格式: email----password----client_id----refresh_token (每行一个账户)")
        detail_label.setStyleSheet("color: #888; font-size: 10px; margin-bottom: 5px;")
        input_layout.addWidget(detail_label)

        # 文本输入框
        self.account_text = QTextEdit()
        self.account_text.setPlaceholderText(
            "请输入账户信息或选择文件导入，支持格式：\n\n"
            "文本格式:\n"
            "<EMAIL>----password1----client_id1----refresh_token1\n"
            "<EMAIL>----password2----client_id2----refresh_token2\n\n"
            "Excel/CSV格式:\n"
            "第1列: 邮箱地址\n"
            "第2列: 密码\n"
            "第3列: 客户端ID\n"
            "第4列: 刷新令牌"
        )
        self.account_text.setMinimumHeight(200)

        # 连接文本变化信号，实现实时解析预览
        self.account_text.textChanged.connect(self.on_text_changed)

        input_layout.addWidget(self.account_text)

        # 性能提示标签
        performance_tip = QLabel("💡 性能提示：建议单次导入不超过1000个账户，大批量导入请分批处理以获得最佳性能")
        performance_tip.setStyleSheet("color: #FF9800; font-size: 11px; padding: 8px; background-color: #FFF3E0; border-radius: 4px; margin-top: 5px;")
        performance_tip.setWordWrap(True)
        input_layout.addWidget(performance_tip)

        import_layout.addWidget(input_group)
        
        tab_widget.addTab(import_widget, "📥 导入设置")
        
    def create_post_actions_tab(self, tab_widget):
        """创建导入后操作选项卡"""
        actions_widget = QWidget()
        actions_layout = QVBoxLayout(actions_widget)
        
        # 快速操作选项
        quick_actions_group = QGroupBox("⚡ 导入后快速操作")
        quick_layout = QVBoxLayout(quick_actions_group)
        
        # 登录测试选项
        self.test_login_check = QCheckBox("🔐 批量登录测试")
        self.test_login_check.setToolTip("导入后立即测试所有账户的登录状态")
        self.test_login_check.setChecked(True)
        quick_layout.addWidget(self.test_login_check)
        
        # 邮件同步选项
        self.sync_emails_check = QCheckBox("📧 启动邮件同步")
        self.sync_emails_check.setToolTip("导入后自动启动所有账户的邮件同步")
        self.sync_emails_check.setChecked(True)
        quick_layout.addWidget(self.sync_emails_check)
        
        # 账户验证选项
        self.validate_accounts_check = QCheckBox("✅ 账户信息验证")
        self.validate_accounts_check.setToolTip("验证账户信息的完整性和格式正确性")
        self.validate_accounts_check.setChecked(True)
        quick_layout.addWidget(self.validate_accounts_check)
        
        actions_layout.addWidget(quick_actions_group)
        
        # 高级选项
        advanced_group = QGroupBox("🔧 高级选项")
        advanced_layout = QFormLayout(advanced_group)
        
        # 并发数量设置
        self.concurrent_spin = QSpinBox()
        self.concurrent_spin.setRange(1, 10)
        self.concurrent_spin.setValue(3)
        self.concurrent_spin.setToolTip("同时处理的账户数量")
        advanced_layout.addRow("并发处理数:", self.concurrent_spin)

        # 批处理大小设置
        self.batch_size_spin = QSpinBox()
        self.batch_size_spin.setRange(50, 1000)
        self.batch_size_spin.setValue(500)
        self.batch_size_spin.setToolTip("每批处理的账户数量，建议500个以下")
        advanced_layout.addRow("批处理大小:", self.batch_size_spin)
        
        # 超时设置
        self.timeout_spin = QSpinBox()
        self.timeout_spin.setRange(10, 120)
        self.timeout_spin.setValue(30)
        self.timeout_spin.setSuffix(" 秒")
        self.timeout_spin.setToolTip("每个账户操作的超时时间")
        advanced_layout.addRow("操作超时:", self.timeout_spin)
        
        # 重试次数
        self.retry_spin = QSpinBox()
        self.retry_spin.setRange(0, 5)
        self.retry_spin.setValue(2)
        self.retry_spin.setToolTip("操作失败时的重试次数")
        advanced_layout.addRow("重试次数:", self.retry_spin)
        
        actions_layout.addWidget(advanced_group)
        actions_layout.addStretch()
        
        tab_widget.addTab(actions_widget, "⚡ 快速操作")
        
    def create_progress_section(self, layout):
        """创建进度区域"""
        progress_group = QGroupBox("📊 导入进度")
        progress_layout = QVBoxLayout(progress_group)
        
        # 当前操作显示
        self.current_operation_label = QLabel("就绪")
        self.current_operation_label.setStyleSheet("font-weight: bold; color: #2196F3;")
        progress_layout.addWidget(self.current_operation_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setTextVisible(True)
        progress_layout.addWidget(self.progress_bar)
        
        # 统计信息
        stats_layout = QHBoxLayout()
        self.total_label = QLabel("总计: 0")
        self.total_label.setStyleSheet("font-weight: bold;")

        self.success_label = QLabel("有效: 0")  # 解析阶段显示有效账户
        self.success_label.setStyleSheet("color: green; font-weight: bold;")

        self.failed_label = QLabel("无效: 0")   # 解析阶段显示无效账户
        self.failed_label.setStyleSheet("color: red; font-weight: bold;")

        # 添加重复账户标签
        self.duplicate_label = QLabel("重复: 0")
        self.duplicate_label.setStyleSheet("color: orange; font-weight: bold;")

        # 添加状态指示器
        self.stats_status_label = QLabel("等待解析...")
        self.stats_status_label.setStyleSheet("color: #666; font-style: italic;")

        stats_layout.addWidget(self.total_label)
        stats_layout.addWidget(self.success_label)
        stats_layout.addWidget(self.failed_label)
        stats_layout.addWidget(self.duplicate_label)
        stats_layout.addWidget(self.stats_status_label)
        stats_layout.addStretch()
        progress_layout.addLayout(stats_layout)
        
        # 日志区域
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(100)
        self.log_text.setReadOnly(True)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 10px;
            }
        """)
        progress_layout.addWidget(self.log_text)
        
        layout.addWidget(progress_group)
        
    def create_button_section(self, layout):
        """创建按钮区域"""
        button_layout = QHBoxLayout()
        
        # 开始导入按钮
        self.start_import_btn = QPushButton("🚀 开始导入")
        self.start_import_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
        """)
        self.start_import_btn.clicked.connect(self.start_import)
        
        # 取消按钮
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.handle_cancel)
        
        button_layout.addWidget(self.start_import_btn)
        button_layout.addStretch()
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
        
    def on_import_method_changed(self):
        """导入方式改变事件"""
        if self.text_import_radio.isChecked():
            self.file_path_edit.setEnabled(False)
            self.browse_btn.setEnabled(False)
            self.create_sample_btn.setEnabled(False)
            self.account_text.setEnabled(True)
        else:
            self.file_path_edit.setEnabled(True)
            self.browse_btn.setEnabled(True)
            self.create_sample_btn.setEnabled(True)
            self.account_text.setEnabled(False)
            
    def browse_file(self):
        """浏览文件"""
        # 获取支持的文件格式
        file_filters = ";;".join(self.file_parser.get_supported_formats())

        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择账户文件", "", file_filters
        )

        if file_path:
            self.file_path_edit.setText(file_path)
            self.log_message(f"选择文件: {file_path}")

            # 解析文件内容
            try:
                accounts, errors = self.file_parser.parse_file(file_path)

                if errors:
                    # 显示解析错误
                    error_msg = "\n".join(errors[:5])  # 只显示前5个错误
                    if len(errors) > 5:
                        error_msg += f"\n... 还有 {len(errors) - 5} 个错误"
                    self.log_message(f"解析警告: {error_msg}")

                if accounts:
                    # 将解析的账户转换为文本格式显示
                    account_lines = []
                    for account in accounts:
                        line = f"{account['email']}----{account['password']}----{account['client_id']}----{account['refresh_token']}"
                        account_lines.append(line)

                    self.account_text.setPlainText("\n".join(account_lines))
                    self.log_message(f"✅ 成功解析 {len(accounts)} 个账户")

                    # 检查重复账户
                    duplicate_count = 0
                    for account in accounts:
                        if self._check_account_duplicate(account['email']):
                            duplicate_count += 1
                            self.log_message(f"⚠️ 发现重复账户: {account['email']} (已存在)")

                    # 更新解析预览统计
                    valid_count = len(accounts) - duplicate_count
                    self.update_parse_preview_stats(valid_count, len(errors), duplicate_count)

                    if errors:
                        QMessageBox.warning(
                            self, "解析完成",
                            f"文件解析完成！\n\n成功: {len(accounts)} 个账户\n错误: {len(errors)} 个\n\n详细错误信息请查看日志。"
                        )
                else:
                    self.log_message("❌ 未能解析出任何有效账户")
                    # 更新解析预览统计（全部无效）
                    self.update_parse_preview_stats(0, len(errors) if errors else 1, 0)
                    QMessageBox.warning(self, "解析失败", "未能从文件中解析出有效的账户信息。\n\n请检查文件格式是否正确。")

            except Exception as e:
                self.log_message(f"❌ 文件解析失败: {e}")
                # 重置统计
                self.reset_stats()
                QMessageBox.critical(self, "错误", f"文件解析失败：\n{str(e)}")

    def on_text_changed(self):
        """文本内容变化时的处理"""
        try:
            # 使用定时器延迟解析，避免频繁解析
            if hasattr(self, '_parse_timer'):
                self._parse_timer.stop()

            self._parse_timer = QTimer()
            self._parse_timer.setSingleShot(True)
            self._parse_timer.timeout.connect(self._delayed_parse_text)
            self._parse_timer.start(500)  # 500ms延迟

        except Exception as e:
            self.logger.error(f"文本变化处理失败: {e}")

    def _delayed_parse_text(self):
        """延迟解析文本内容"""
        try:
            account_text = self.account_text.toPlainText().strip()

            if not account_text:
                # 文本为空，重置统计
                self.reset_stats()
                return

            # 解析账户
            lines = account_text.split('\n')
            valid_count = 0
            invalid_count = 0
            duplicate_count = 0

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # 解析账户信息
                account_info = self._parse_account_line(line)
                if account_info:
                    # 检查是否重复
                    if self._check_account_duplicate(account_info['email']):
                        duplicate_count += 1
                        self.log_message(f"⚠️ 发现重复账户: {account_info['email']} (已存在)")
                    else:
                        valid_count += 1
                else:
                    invalid_count += 1

            # 更新解析预览统计
            if valid_count > 0 or invalid_count > 0 or duplicate_count > 0:
                self.update_parse_preview_stats(valid_count, invalid_count, duplicate_count)
            else:
                self.reset_stats()

        except Exception as e:
            self.logger.error(f"延迟解析文本失败: {e}")
            self.reset_stats()

    def create_sample_files(self):
        """创建示例文件"""
        try:
            # 选择保存目录
            save_dir = QFileDialog.getExistingDirectory(
                self, "选择保存示例文件的目录", ""
            )

            if save_dir:
                success, message = self.file_parser.create_sample_files(save_dir)

                if success:
                    self.log_message(f"✅ {message}")
                    QMessageBox.information(
                        self, "创建成功",
                        f"示例文件已创建到: {save_dir}\n\n包含文件:\n- 账户示例.txt\n- 账户示例.xlsx\n- 账户示例.csv"
                    )
                else:
                    self.log_message(f"❌ {message}")
                    QMessageBox.warning(self, "创建失败", message)

        except Exception as e:
            self.log_message(f"❌ 创建示例文件失败: {e}")
            QMessageBox.critical(self, "错误", f"创建示例文件失败：\n{str(e)}")
                
    def start_import(self):
        """开始导入"""
        try:
            # 获取账户文本
            if self.text_import_radio.isChecked():
                account_text = self.account_text.toPlainText().strip()
            else:
                if not self.file_path_edit.text():
                    QMessageBox.warning(self, "提示", "请选择要导入的文件")
                    return
                account_text = self.account_text.toPlainText().strip()

            if not account_text:
                QMessageBox.warning(self, "提示", "请输入账户信息")
                return

            # 设置导入进度步骤
            import_steps = [
                "🔍 正在解析导入文件...",
                "✅ 正在验证邮件格式...",
                "💾 正在保存邮件数据...",
                "🎉 导入完成"
            ]

            # 开始进度显示
            self.progress_manager.set_steps(import_steps)
            self.progress_manager.start_progress([self.start_import_btn, self.cancel_btn])

            # 设置导入状态
            self.is_importing = True

            # 延迟执行导入，让用户看到第一步
            QTimer.singleShot(500, lambda: self._direct_import_accounts(account_text))

        except Exception as e:
            self.logger.error(f"开始导入失败: {e}")
            self.log_message(f"❌ 开始导入失败: {e}")
            self._handle_import_error("启动阶段", str(e))
            self.is_importing = False

    def _direct_import_accounts(self, account_text: str):
        """直接导入账户（简化版本）"""
        try:
            # 步骤1: 解析导入文件
            self.log_message("🔍 开始解析账户信息...")

            # 检查是否已经有解析结果
            if self.parsed_valid_count == 0:
                # 解析账户
                lines = account_text.strip().split('\n')
                parsed_accounts = []
                invalid_count = 0
                duplicate_count = 0

                for i, line in enumerate(lines):
                    line = line.strip()
                    if not line:
                        continue

                    # 解析账户信息
                    account_info = self._parse_account_line(line)
                    if account_info:
                        # 检查是否重复
                        if self._check_account_duplicate(account_info['email']):
                            duplicate_count += 1
                            self.log_message(f"⚠️ 发现重复账户: {account_info['email']} (已存在)")
                        else:
                            parsed_accounts.append(account_info)
                    else:
                        invalid_count += 1

                self.log_message(f"✅ 成功解析 {len(parsed_accounts)} 个新账户")
                if duplicate_count > 0:
                    self.log_message(f"⚠️ 跳过 {duplicate_count} 个重复账户")

                # 更新解析预览统计
                self.update_parse_preview_stats(len(parsed_accounts), invalid_count, duplicate_count)
            else:
                # 使用已有的解析结果
                self.log_message(f"📋 使用已解析的 {self.parsed_valid_count} 个有效账户")

                # 重新解析以获取账户对象
                lines = account_text.strip().split('\n')
                parsed_accounts = []

                for line in lines:
                    line = line.strip()
                    if not line:
                        continue

                    account_info = self._parse_account_line(line)
                    if account_info:
                        parsed_accounts.append(account_info)

            if not parsed_accounts:
                self.log_message("❌ 没有有效账户可导入")
                self.progress_manager.error_progress("没有有效账户可导入")
                self.is_importing = False
                return

            # 检查账户数量并提供分批建议
            account_count = len(parsed_accounts)
            batch_size = self.batch_size_spin.value()

            if account_count > batch_size:
                self.log_message(f"⚠️ 检测到 {account_count} 个账户，建议分批处理 (每批 {batch_size} 个)")
                self.log_message(f"📊 预计需要 {(account_count + batch_size - 1) // batch_size} 批次完成")

            # 步骤2: 验证邮件格式
            self.progress_manager.next_step()
            QTimer.singleShot(300, lambda: self._validate_accounts(parsed_accounts))

        except Exception as e:
            self.logger.error(f"解析账户失败: {e}")
            self.log_message(f"❌ 解析账户失败: {e}")
            self._handle_import_error("解析阶段", str(e))
            self.is_importing = False

    def _validate_accounts(self, parsed_accounts: list):
        """验证账户格式"""
        try:
            total_accounts = len(parsed_accounts)
            self.log_message(f"✅ 开始验证 {total_accounts} 个账户格式...")

            # 验证过程
            valid_accounts = []
            invalid_count = 0

            for i, account_info in enumerate(parsed_accounts):
                # 更新进度消息
                current_progress = f"✅ 正在验证账户 ({i+1}/{total_accounts}): {account_info.get('email', 'unknown')}"
                self.progress_manager.loading_manager.update_message(current_progress)

                # 详细的验证逻辑
                email = account_info.get('email', '')
                is_valid = True
                validation_errors = []

                # 验证邮箱格式
                if '@' not in email:
                    is_valid = False
                    validation_errors.append("缺少@符号")
                elif '.' not in email.split('@')[1]:
                    is_valid = False
                    validation_errors.append("域名格式无效")

                # 验证必需字段
                required_fields = ['password', 'client_id', 'refresh_token']
                for field in required_fields:
                    if not account_info.get(field, '').strip():
                        is_valid = False
                        validation_errors.append(f"缺少{field}")

                if is_valid:
                    valid_accounts.append(account_info)
                    self.log_message(f"✅ 验证通过: {email}")
                else:
                    invalid_count += 1
                    error_msg = ", ".join(validation_errors)
                    self.log_message(f"❌ 验证失败: {email} - {error_msg}")

                # 更新进度条（第2步占25%的总进度）
                sub_progress = int((i + 1) / total_accounts * 100)
                overall_progress = 25 + (sub_progress * 25 // 100)
                self.progress_manager.loading_manager.set_progress(overall_progress, 100)

            self.log_message(f"✅ 验证完成 - 有效: {len(valid_accounts)}, 无效: {invalid_count}")

            if not valid_accounts:
                self.progress_manager.error_progress("没有有效的账户可以导入")
                self.is_importing = False
                return

            # 步骤3: 保存邮件数据
            self.progress_manager.next_step()
            QTimer.singleShot(300, lambda: self._save_accounts(valid_accounts))

        except Exception as e:
            self.logger.error(f"验证账户失败: {e}")
            self.log_message(f"❌ 验证账户失败: {e}")
            self._handle_import_error("验证阶段", str(e))
            self.is_importing = False

    def _save_accounts(self, parsed_accounts: list):
        """保存账户数据"""
        try:
            total_accounts = len(parsed_accounts)
            self.log_message(f"💾 开始保存 {total_accounts} 个账户...")

            success_count = 0
            failed_count = 0

            # 清空新导入账户列表
            self.newly_imported_accounts.clear()

            for i, account_info in enumerate(parsed_accounts):
                try:
                    # 更新进度消息，显示当前处理的账户
                    current_progress = f"💾 正在保存账户 ({i+1}/{total_accounts}): {account_info.get('email', 'unknown')}"
                    self.progress_manager.loading_manager.update_message(current_progress)

                    # 设置启用状态
                    account_info['enabled'] = True

                    # 添加账户
                    success = self.real_account_manager.add_account(account_info)
                    if success:
                        success_count += 1
                        # 记录新导入的账户
                        self.newly_imported_accounts.append({
                            'account_id': account_info['account_id'],
                            'email': account_info['email']
                        })
                        self.log_message(f"✅ 成功导入账户: {account_info['email']}")
                    else:
                        failed_count += 1
                        self.log_message(f"❌ 导入失败: {account_info['email']}")

                    # 更新进度条（基于当前步骤的子进度）
                    sub_progress = int((i + 1) / total_accounts * 100)
                    # 当前在第3步（保存数据），总共4步，所以基础进度是50%
                    overall_progress = 50 + (sub_progress * 25 // 100)  # 第3步占25%的总进度
                    self.progress_manager.loading_manager.set_progress(overall_progress, 100)

                except Exception as e:
                    self.logger.error(f"导入账户 {account_info.get('email', 'unknown')} 失败: {e}")
                    failed_count += 1
                    self.log_message(f"❌ 导入失败: {account_info.get('email', 'unknown')} - {e}")

            # 显示保存完成统计
            self.log_message(f"💾 保存完成 - 成功: {success_count}, 失败: {failed_count}")

            # 步骤4: 导入完成
            self.progress_manager.next_step()
            QTimer.singleShot(500, lambda: self._complete_import(success_count, failed_count))

        except Exception as e:
            self.logger.error(f"保存账户失败: {e}")
            self.log_message(f"❌ 保存账户失败: {e}")
            self._handle_import_error("保存阶段", str(e))
            self.is_importing = False

    def _complete_import(self, success_count: int, failed_count: int):
        """完成导入"""
        try:
            # 计算统计信息
            total_count = success_count + failed_count
            success_rate = (success_count / total_count * 100) if total_count > 0 else 0

            # 构建详细的成功消息
            if success_count > 0:
                if failed_count == 0:
                    # 全部成功
                    success_message = f"🎉 导入完美完成！成功导入 {success_count} 个账户"
                    detailed_message = (
                        f"✨ 导入统计报告 ✨\n\n"
                        f"📊 总计账户: {total_count}\n"
                        f"✅ 成功导入: {success_count}\n"
                        f"❌ 导入失败: {failed_count}\n"
                        f"📈 成功率: {success_rate:.1f}%\n\n"
                        f"🎯 所有账户都已成功导入！\n"
                        f"💡 您现在可以开始使用这些账户了。"
                    )
                else:
                    # 部分成功
                    success_message = f"🎉 导入完成！成功: {success_count}/{total_count}"
                    detailed_message = (
                        f"📊 导入统计报告 📊\n\n"
                        f"📊 总计账户: {total_count}\n"
                        f"✅ 成功导入: {success_count}\n"
                        f"❌ 导入失败: {failed_count}\n"
                        f"📈 成功率: {success_rate:.1f}%\n\n"
                        f"💡 建议：\n"
                        f"• 检查失败账户的信息是否正确\n"
                        f"• 可以重新尝试导入失败的账户\n"
                        f"• 成功导入的账户已可正常使用"
                    )
            else:
                # 全部失败
                success_message = "❌ 导入失败，没有账户成功导入"
                detailed_message = (
                    f"❌ 导入失败报告 ❌\n\n"
                    f"📊 总计账户: {total_count}\n"
                    f"✅ 成功导入: 0\n"
                    f"❌ 导入失败: {failed_count}\n\n"
                    f"💡 建议：\n"
                    f"• 检查账户信息格式是否正确\n"
                    f"• 确认使用正确的分隔符（----）\n"
                    f"• 验证所有必需字段都已填写\n"
                    f"• 查看详细错误日志"
                )

            self.log_message(success_message)

            # 显示成功的账户列表
            if success_count > 0:
                self.log_message("📋 成功导入的账户：")
                for account in self.newly_imported_accounts:
                    self.log_message(f"   ✅ {account['email']}")

            # 隐藏加载状态并显示统一的完成反馈
            if success_count > 0:
                # 隐藏加载状态
                self.progress_manager.cancel_progress()

                # 立即显示详细统计对话框（不延迟）
                QMessageBox.information(
                    self, "🎉 导入完成", detailed_message
                )
            else:
                # 全部失败时隐藏加载状态并显示错误
                self.progress_manager.cancel_progress()

                # 立即显示详细错误对话框
                QMessageBox.warning(
                    self, "❌ 导入失败", detailed_message
                )

            # 重置导入状态
            self.is_importing = False

            # 发送导入完成信号
            self.on_import_completed(success_count, failed_count)

        except Exception as e:
            self.logger.error(f"完成导入处理失败: {e}")
            self._handle_import_error("完成阶段", str(e))
            self.is_importing = False

    def handle_cancel(self):
        """处理取消操作"""
        if self.is_importing:
            # 如果正在导入，显示确认对话框
            reply = QMessageBox.question(
                self, "确认取消",
                "正在导入账户，确定要取消吗？\n\n已导入的账户将保留。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # 取消导入进度
                self.progress_manager.cancel_progress()
                self.is_importing = False
                self.log_message("❌ 用户取消了导入操作")
                self.close()
        else:
            # 直接关闭
            self.close()

    def _handle_import_error(self, stage: str, error_message: str):
        """统一处理导入错误"""
        try:
            # 构建详细的错误信息
            detailed_error = f"导入在{stage}失败"

            # 根据错误类型提供解决建议
            suggestions = []
            error_lower = error_message.lower()

            if "permission" in error_lower or "access" in error_lower:
                suggestions.append("• 检查文件访问权限")
                suggestions.append("• 确保程序有足够的系统权限")
            elif "network" in error_lower or "connection" in error_lower:
                suggestions.append("• 检查网络连接")
                suggestions.append("• 确认服务器地址正确")
            elif "format" in error_lower or "parse" in error_lower:
                suggestions.append("• 检查账户信息格式是否正确")
                suggestions.append("• 确保使用正确的分隔符（----）")
            elif "duplicate" in error_lower:
                suggestions.append("• 检查是否有重复的账户")
                suggestions.append("• 清理重复的账户信息")
            else:
                suggestions.append("• 检查账户信息是否完整")
                suggestions.append("• 确认所有必需字段都已填写")
                suggestions.append("• 重试导入操作")

            # 构建详细错误对话框消息
            error_dialog_message = f"{detailed_error}\n\n错误详情：\n{error_message}\n\n解决建议：\n" + "\n".join(suggestions)

            # 隐藏加载状态并显示统一的错误反馈
            self.progress_manager.cancel_progress()

            # 立即显示详细错误对话框
            QMessageBox.critical(
                self, "❌ 导入错误", error_dialog_message
            )

        except Exception as e:
            # 错误处理本身出错时的保险措施
            self.logger.error(f"处理导入错误时出错: {e}")
            self.progress_manager.cancel_progress()
            QMessageBox.critical(
                self, "❌ 导入错误", f"导入过程中发生错误：\n{error_message}"
            )

    def _parse_account_line(self, line: str) -> dict:
        """解析单行账户信息"""
        try:
            # 支持的格式：email----password----client_id----refresh_token
            parts = line.split('----')

            if len(parts) >= 4:
                email = parts[0].strip()
                password = parts[1].strip()
                client_id = parts[2].strip()
                refresh_token = parts[3].strip()

                # 验证邮箱格式
                if '@' not in email or '.' not in email.split('@')[1]:
                    return None

                # 生成账户ID
                account_id = email.replace('@', '_').replace('.', '_')

                return {
                    'account_id': account_id,
                    'email': email,
                    'password': password,
                    'display_name': email.split('@')[0],
                    'imap_server': 'outlook.office365.com',
                    'imap_port': 993,
                    'smtp_server': 'smtp-mail.outlook.com',
                    'smtp_port': 587,
                    'use_ssl': True,
                    'client_id': client_id,
                    'refresh_token': refresh_token,
                    'enabled': True,
                    'priority': 1,
                    'max_retries': 3,
                    'timeout': 30,
                    'keep_alive': True,
                    'sync_interval': 5,
                    'max_emails': 1000,
                    'download_attachments': False
                }

            return None

        except Exception as e:
            self.logger.error(f"解析账户行失败: {e}")
            return None

    def _check_account_duplicate(self, email: str) -> bool:
        """检查账户是否重复"""
        try:
            if not self.real_account_manager:
                return False

            # 检查邮箱是否已存在
            for account_id, account in self.real_account_manager.accounts.items():
                if account.email.lower() == email.lower():
                    return True

            return False

        except Exception as e:
            self.logger.error(f"检查账户重复失败: {e}")
            return False

    def _on_parsing_completed_start_import(self, success_count: int, failed_count: int):
        """解析完成后开始导入"""
        try:
            if success_count > 0:
                self.log_message(f"📋 解析完成，开始导入 {success_count} 个账户...")
                self.current_operation_label.setText("正在导入账户...")
                # 开始导入
                self.batch_importer.start_import()
            else:
                self.log_message("❌ 没有有效账户可导入")
                self.start_import_btn.setEnabled(True)
        except Exception as e:
            self.logger.error(f"解析完成后开始导入失败: {e}")
            self.log_message(f"❌ 解析完成后开始导入失败: {e}")
            self.start_import_btn.setEnabled(True)

    def on_import_completed(self, success_count: int, failed_count: int):
        """导入完成回调"""
        try:
            self.logger.info(f"收到导入完成信号: 成功={success_count}, 失败={failed_count}")

            # 保存统计数据
            self.import_success_count = success_count
            self.import_failed_count = failed_count

            # 只有在非进度管理模式下才更新这些UI元素
            if not self.is_importing:
                self.current_operation_label.setText("导入完成")
                self.progress_bar.setValue(100)

            # 更新导入结果统计
            self.update_import_result_stats(success_count, failed_count)

            # 如果有成功导入的账户，执行后续操作
            if success_count > 0:
                self.execute_post_actions(success_count)
            else:
                # 只有在非进度管理模式下才重新启用按钮
                if not self.is_importing:
                    self.start_import_btn.setEnabled(True)

        except Exception as e:
            self.logger.error(f"处理导入完成事件失败: {e}")
            if self.is_importing:
                self.progress_manager.error_progress(f"完成处理失败: {str(e)}")
                self.is_importing = False
            
    def execute_post_actions(self, imported_count: int):
        """执行导入后操作"""
        try:
            # 获取新导入的账户ID（排除重复账户）
            new_account_ids = [account['account_id'] for account in self.newly_imported_accounts]

            if not new_account_ids:
                self.log_message("📋 没有新导入的账户需要执行后续操作")
                self.complete_import(imported_count)
                return

            # 显示将要操作的账户信息
            self.log_message(f"📋 准备对 {len(new_account_ids)} 个新导入账户执行后续操作")
            for account in self.newly_imported_accounts:
                self.log_message(f"   • {account['email']}")

            # 如果有重复账户被跳过，显示提示
            if self.parsed_duplicate_count > 0:
                self.log_message(f"🔄 已跳过 {self.parsed_duplicate_count} 个重复账户的后续操作")

            # 执行选中的操作
            if self.test_login_check.isChecked():
                self.start_post_action("login_test", new_account_ids)
            elif self.sync_emails_check.isChecked():
                self.start_post_action("sync_emails", new_account_ids)
            else:
                self.complete_import(imported_count)

        except Exception as e:
            self.logger.error(f"执行导入后操作失败: {e}")
            self.complete_import(imported_count)
            
    def start_post_action(self, action_type: str, account_ids: List[str]):
        """启动导入后操作"""
        try:
            action_names = {
                "login_test": "登录测试",
                "sync_emails": "邮件同步"
            }

            action_name = action_names.get(action_type, action_type)
            self.current_operation_label.setText(f"正在执行{action_name}...")
            self.progress_bar.setValue(0)

            # 记录操作开始日志
            self.log_message(f"🚀 开始执行{action_name}，目标账户: {len(account_ids)} 个")

            # 如果是登录测试，显示具体的账户信息
            if action_type == "login_test":
                self.log_message("📋 将测试以下新导入账户的连接:")
                for account_id in account_ids:
                    # 获取账户邮箱用于显示
                    account = self.real_account_manager.accounts.get(account_id)
                    email = account.email if account else account_id
                    self.log_message(f"   • {email}")

            # 创建操作线程
            self.post_action_thread = PostImportActionThread(action_type, self.real_account_manager, account_ids)
            self.post_action_thread.progress_updated.connect(self.on_post_action_progress)
            self.post_action_thread.action_completed.connect(self.on_post_action_completed)
            self.post_action_thread.start()

        except Exception as e:
            self.logger.error(f"启动导入后操作失败: {e}")
            self.complete_import(len(account_ids))
            
    def on_post_action_progress(self, progress: int, message: str):
        """导入后操作进度更新"""
        self.progress_bar.setValue(progress)
        self.log_message(message)
        
    def on_post_action_completed(self, action_type: str, success: bool):
        """导入后操作完成"""
        action_names = {
            "login_test": "登录测试",
            "sync_emails": "邮件同步"
        }
        
        action_name = action_names.get(action_type, action_type)
        
        if success:
            self.log_message(f"✅ {action_name}完成")
        else:
            self.log_message(f"❌ {action_name}失败")
            
        # 检查是否还有其他操作要执行
        if action_type == "login_test" and self.sync_emails_check.isChecked():
            # 继续执行邮件同步
            account_ids = []  # 这里需要保存账户ID列表
            self.start_post_action("sync_emails", account_ids)
        else:
            # 所有操作完成
            self.complete_import(self.import_success_count)
            
    def complete_import(self, imported_count: int):
        """完成导入"""
        self.current_operation_label.setText("所有操作完成")
        self.progress_bar.setValue(100)
        self.start_import_btn.setEnabled(True)
        
        # 发送完成信号
        self.import_completed.emit(imported_count)
        
        # 显示完成消息
        QMessageBox.information(
            self, "导入完成",
            f"批量导入完成！\n\n成功导入 {imported_count} 个账户\n相关操作已自动执行"
        )
        
    def log_message(self, message: str):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        self.log_text.append(formatted_message)
        
        # 自动滚动到底部
        cursor = self.log_text.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.log_text.setTextCursor(cursor)

    def update_parse_preview_stats(self, valid_count: int, invalid_count: int, duplicate_count: int = 0):
        """更新解析预览统计"""
        try:
            self.parsed_valid_count = valid_count
            self.parsed_invalid_count = invalid_count
            self.parsed_duplicate_count = duplicate_count
            total = valid_count + invalid_count + duplicate_count

            # 更新统计显示（解析阶段）
            self.total_label.setText(f"总计: {total}")
            self.success_label.setText(f"有效: {valid_count}")
            self.failed_label.setText(f"无效: {invalid_count}")
            self.duplicate_label.setText(f"重复: {duplicate_count}")

            # 性能评估和建议
            batch_size = self.batch_size_spin.value()
            if valid_count > batch_size:
                performance_msg = f"⚠️ 建议分批处理 (当前{valid_count}个，建议每批{batch_size}个)"
                self.stats_status_label.setText(performance_msg)
                self.stats_status_label.setStyleSheet("color: #FF9800; font-weight: bold;")
            elif valid_count > 1000:
                self.stats_status_label.setText("⚠️ 大批量导入，预计耗时较长")
                self.stats_status_label.setStyleSheet("color: #FF5722; font-weight: bold;")
            else:
                self.stats_status_label.setText("✅ 解析完成，可以开始导入")
                self.stats_status_label.setStyleSheet("color: #28a745; font-style: italic; font-weight: bold;")

            # 强制刷新UI
            self.total_label.repaint()
            self.success_label.repaint()
            self.failed_label.repaint()
            self.duplicate_label.repaint()
            self.stats_status_label.repaint()

            self.log_message(f"📊 解析统计 - 总计: {total}, 有效: {valid_count}, 无效: {invalid_count}, 重复: {duplicate_count}")

        except Exception as e:
            self.logger.error(f"更新解析预览统计失败: {e}")

    def update_import_result_stats(self, success_count: int, failed_count: int):
        """更新导入结果统计"""
        try:
            # 计算总数（包含跳过的重复账户）
            skipped_count = self.parsed_duplicate_count
            total = success_count + failed_count + skipped_count

            # 更新统计显示（导入阶段）
            self.total_label.setText(f"总计: {total}")
            self.success_label.setText(f"成功: {success_count}")
            self.failed_label.setText(f"失败: {failed_count}")
            self.duplicate_label.setText(f"跳过: {skipped_count}")
            self.stats_status_label.setText("导入完成")
            self.stats_status_label.setStyleSheet("color: #17a2b8; font-style: italic; font-weight: bold;")

            # 强制刷新UI
            self.total_label.repaint()
            self.success_label.repaint()
            self.failed_label.repaint()
            self.duplicate_label.repaint()
            self.stats_status_label.repaint()

            self.log_message(f"📊 导入统计 - 总计: {total}, 成功: {success_count}, 失败: {failed_count}, 跳过: {skipped_count}")

        except Exception as e:
            self.logger.error(f"更新导入结果统计失败: {e}")

    def reset_stats(self):
        """重置统计显示"""
        self.parsed_valid_count = 0
        self.parsed_invalid_count = 0
        self.parsed_duplicate_count = 0
        self.import_success_count = 0
        self.import_failed_count = 0

        # 清空新导入账户列表
        self.newly_imported_accounts.clear()

        self.total_label.setText("总计: 0")
        self.success_label.setText("有效: 0")
        self.failed_label.setText("无效: 0")
        self.duplicate_label.setText("重复: 0")
        self.stats_status_label.setText("等待解析...")
        self.stats_status_label.setStyleSheet("color: #666; font-style: italic;")

    def test_update_stats(self, success: int, failed: int):
        """测试统计更新（调试用）"""
        self.logger.info(f"测试统计更新: 成功={success}, 失败={failed}")
        total = success + failed
        self.total_label.setText(f"总计: {total}")
        self.success_label.setText(f"成功: {success}")
        self.failed_label.setText(f"失败: {failed}")
        self.log_message(f"🧪 测试统计更新 - 总计: {total}, 成功: {success}, 失败: {failed}")
