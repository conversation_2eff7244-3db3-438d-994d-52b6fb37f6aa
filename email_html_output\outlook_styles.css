/* Outlook风格邮件查看器样式 */

/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: #323130;
    background-color: #faf9f8;
}

/* Outlook容器 */
.outlook-container {
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    overflow: hidden;
}

/* 应用头部 */
.outlook-header {
    background: #0078d4;
    color: white;
    padding: 12px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.app-title {
    font-weight: 600;
    font-size: 16px;
}

.header-actions .btn-close {
    background: none;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 2px;
}

.header-actions .btn-close:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* 邮件内容区域 */
.outlook-content {
    padding: 0;
}

.outlook-email-container {
    background: white;
}

/* 邮件工具栏 */
.email-toolbar {
    background: #f3f2f1;
    border-bottom: 1px solid #edebe9;
    padding: 8px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.toolbar-left,
.toolbar-right {
    display: flex;
    gap: 8px;
}

.email-toolbar button {
    background: none;
    border: 1px solid transparent;
    padding: 6px 12px;
    border-radius: 2px;
    cursor: pointer;
    font-size: 13px;
    color: #323130;
    display: flex;
    align-items: center;
    gap: 4px;
}

.email-toolbar button:hover {
    background: #e1dfdd;
    border-color: #c8c6c4;
}

.email-toolbar button .icon {
    font-size: 14px;
}

/* 邮件头部区域 */
.email-header-section {
    padding: 20px;
    border-bottom: 1px solid #edebe9;
}

.subject-line {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
}

.email-subject {
    font-size: 20px;
    font-weight: 600;
    color: #323130;
    margin: 0;
    flex: 1;
    margin-right: 16px;
}

.status-badges {
    display: flex;
    gap: 4px;
    align-items: center;
}

.badge {
    font-size: 12px;
    padding: 2px 4px;
    border-radius: 2px;
}

.unread-badge {
    color: #0078d4;
    font-weight: bold;
}

.flag-badge {
    color: #d13438;
}

.important-badge {
    color: #ff8c00;
}

/* 发件人信息行 */
.sender-info-line {
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.sender-avatar {
    flex-shrink: 0;
}

.avatar-circle {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #0078d4;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 14px;
}

.sender-details {
    flex: 1;
}

.sender-name-line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.sender-name {
    font-weight: 600;
    color: #323130;
    margin-right: 8px;
}

.sender-email {
    color: #605e5c;
    font-size: 13px;
}

.email-date {
    color: #605e5c;
    font-size: 13px;
    white-space: nowrap;
}

.recipients-line {
    color: #605e5c;
    font-size: 13px;
}

.to-label {
    font-weight: 600;
    margin-right: 4px;
}

.recipient {
    color: #323130;
}

.others-count {
    color: #605e5c;
    font-style: italic;
}

/* 邮件正文区域 */
.email-body-section {
    padding: 20px;
    border-bottom: 1px solid #edebe9;
}

.body-content {
    max-width: 100%;
    overflow-x: auto;
}

.html-content {
    /* HTML邮件样式 */
}

.outlook-html-content {
    font-family: inherit;
    line-height: 1.5;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.email-content-wrapper {
    max-width: 100%;
    overflow-x: auto;
}

.outlook-html-content img {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 8px 0;
}

.outlook-html-content table {
    border-collapse: collapse;
    max-width: 100%;
    width: 100%;
    margin: 8px 0;
}

.outlook-html-content td,
.outlook-html-content th {
    padding: 8px;
    border: 1px solid #edebe9;
    vertical-align: top;
}

.outlook-html-content p {
    margin: 8px 0;
    line-height: 1.6;
}

.outlook-html-content h1,
.outlook-html-content h2,
.outlook-html-content h3,
.outlook-html-content h4,
.outlook-html-content h5,
.outlook-html-content h6 {
    margin: 16px 0 8px 0;
    color: #323130;
}

.outlook-html-content ul,
.outlook-html-content ol {
    margin: 8px 0;
    padding-left: 24px;
}

.outlook-html-content li {
    margin: 4px 0;
}

.outlook-html-content a {
    color: #0078d4;
    text-decoration: none;
}

.outlook-html-content a:hover {
    text-decoration: underline;
}

.outlook-html-content blockquote {
    border-left: 4px solid #0078d4;
    margin: 16px 0;
    padding-left: 16px;
    color: #605e5c;
}

.outlook-html-content pre {
    background: #f8f8f8;
    border: 1px solid #edebe9;
    border-radius: 4px;
    padding: 12px;
    overflow-x: auto;
    font-family: 'Consolas', 'Courier New', monospace;
}

.outlook-html-content code {
    background: #f8f8f8;
    padding: 2px 4px;
    border-radius: 2px;
    font-family: 'Consolas', 'Courier New', monospace;
}

.text-content {
    /* 纯文本邮件样式 */
}

.text-body-wrapper {
    background: #f8f8f8;
    border: 1px solid #edebe9;
    border-radius: 4px;
    padding: 16px;
    font-family: 'Consolas', 'Courier New', monospace;
    font-size: 13px;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.text-content-formatted {
    line-height: 1.6;
}

.email-link {
    color: #0078d4;
    text-decoration: none;
}

.email-link:hover {
    text-decoration: underline;
}

.empty-content {
    text-align: center;
    padding: 40px;
    color: #605e5c;
}

.empty-message .icon {
    font-size: 48px;
    margin-bottom: 16px;
    display: block;
}

/* 附件区域 */
.email-attachments-section {
    padding: 20px;
    background: #f8f8f8;
    border-top: 1px solid #edebe9;
}

.attachments-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    font-weight: 600;
    color: #323130;
}

.attachments-header .icon {
    font-size: 16px;
}

.attachments-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 12px;
}

.attachment-item {
    background: white;
    border: 1px solid #edebe9;
    border-radius: 4px;
    padding: 12px;
    display: flex;
    align-items: center;
    gap: 12px;
    transition: box-shadow 0.2s;
}

.attachment-item:hover {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.attachment-icon {
    flex-shrink: 0;
}

.file-icon {
    font-size: 24px;
}

.attachment-details {
    flex: 1;
    min-width: 0;
}

.attachment-name {
    font-weight: 600;
    color: #323130;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 2px;
}

.attachment-meta {
    font-size: 12px;
    color: #605e5c;
}

.attachment-actions {
    flex-shrink: 0;
}

.btn-download {
    background: #0078d4;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 2px;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.btn-download:hover {
    background: #106ebe;
}

/* 错误样式 */
.error {
    background: #fdf2f2;
    border: 1px solid #e74c3c;
    color: #e74c3c;
    padding: 16px;
    border-radius: 4px;
    margin: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .outlook-container {
        margin: 0;
        border-radius: 0;
    }
    
    .email-header-section,
    .email-body-section,
    .email-attachments-section {
        padding: 16px;
    }
    
    .sender-name-line {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
    
    .attachments-grid {
        grid-template-columns: 1fr;
    }
    
    .email-toolbar {
        padding: 8px 16px;
        flex-wrap: wrap;
        gap: 8px;
    }
    
    .toolbar-left,
    .toolbar-right {
        flex-wrap: wrap;
    }
}

/* 深色主题 */
.outlook-theme-dark {
    background-color: #1e1e1e;
    color: #ffffff;
}

.outlook-theme-dark .outlook-container {
    background: #2d2d30;
    color: #ffffff;
}

.outlook-theme-dark .email-toolbar {
    background: #3c3c3c;
    border-bottom-color: #464647;
}

.outlook-theme-dark .email-header-section,
.outlook-theme-dark .email-body-section {
    border-bottom-color: #464647;
}

.outlook-theme-dark .text-body-wrapper {
    background: #1e1e1e;
    border-color: #464647;
    color: #ffffff;
}

.outlook-theme-dark .attachment-item {
    background: #3c3c3c;
    border-color: #464647;
}

/* 打印样式 */
@media print {
    .outlook-header,
    .email-toolbar {
        display: none;
    }
    
    .outlook-container {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .email-body-section {
        border-bottom: none;
    }
}
