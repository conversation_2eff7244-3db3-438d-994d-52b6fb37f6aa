# 📧 内嵌HTML邮件查看器使用指南

## 🎉 新功能完成！

✅ **内嵌HTML邮件查看功能已完全实现！**

现在您可以直接在应用程序内查看HTML格式的邮件，无需跳转到外部浏览器！

## 🚀 主要功能特点

### 1. **双模式查看** 📱
- **文本模式**: 传统的纯文本显示，快速浏览邮件内容
- **HTML模式**: 完整的HTML渲染，保持原始格式和样式

### 2. **智能内容处理** 🧠
- **自动检测**: 自动识别邮件是否包含HTML内容
- **智能转换**: 纯文本邮件也可以在HTML模式下查看
- **安全渲染**: 自动处理特殊字符，防止安全问题

### 3. **无缝切换** ⚡
- **标签页切换**: 点击"文本"或"HTML"标签页即可切换
- **快捷按钮**: 使用"HTML模式"/"文本模式"按钮快速切换
- **实时更新**: 选择不同邮件时自动更新显示内容

## 📋 使用方法

### 方法1: 使用标签页切换
```
1. 选择一封邮件
2. 在邮件内容预览区域，点击"HTML"标签页
3. 即可看到完整的HTML格式邮件
4. 点击"文本"标签页可切换回文本模式
```

### 方法2: 使用快捷按钮
```
1. 选择一封邮件
2. 点击"HTML模式"按钮
3. 自动切换到HTML标签页并显示HTML内容
4. 再次点击（现在显示为"文本模式"）可切换回文本模式
```

## 🎨 界面布局

### 邮件内容预览区域
```
┌─────────────────────────────────────────┐
│ [复制内容] [保存内容] [HTML模式]        │
├─────────────────────────────────────────┤
│ [文本] [HTML] ← 标签页切换              │
├─────────────────────────────────────────┤
│                                         │
│     邮件内容显示区域                    │
│   (文本模式 或 HTML渲染模式)            │
│                                         │
└─────────────────────────────────────────┘
```

## 🔧 技术特性

### HTML渲染引擎
- **WebEngine**: 使用PySide6的WebEngine组件
- **完整支持**: 支持CSS样式、表格、图片等所有HTML元素
- **响应式**: 自动适应预览窗口大小

### 内容处理
- **自动包装**: 自动为HTML片段添加完整的文档结构
- **样式优化**: 添加适合邮件查看的默认样式
- **字符转义**: 安全处理特殊字符和脚本内容

### 性能优化
- **延迟加载**: 只在切换到HTML模式时才渲染内容
- **内存管理**: 自动释放不需要的资源
- **快速切换**: 标签页切换响应迅速

## 📧 支持的邮件类型

### 1. **纯HTML邮件**
- 完整显示所有HTML格式和样式
- 支持表格、列表、链接等元素
- 保持原始布局和颜色

### 2. **纯文本邮件**
- 在HTML模式下以格式化方式显示
- 保持换行和段落结构
- 使用优雅的字体和间距

### 3. **混合格式邮件**
- 优先显示HTML版本
- 文本模式显示纯文本版本
- 可在两种格式间自由切换

## 🛡️ 安全特性

### 内容安全
- **脚本过滤**: 自动处理潜在的脚本内容
- **字符转义**: 正确转义HTML特殊字符
- **沙盒环境**: 在受控环境中渲染HTML内容

### 隐私保护
- **本地渲染**: 所有内容在本地处理，不发送到外部服务器
- **无网络请求**: 默认不加载外部资源
- **安全隔离**: HTML内容在独立的渲染环境中显示

## 🎯 使用场景

### 1. **营销邮件查看**
- 完整显示营销邮件的设计和布局
- 查看图片、按钮、链接等元素
- 体验邮件的原始视觉效果

### 2. **工作邮件处理**
- 查看包含表格的数据报告
- 阅读格式化的文档和通知
- 处理包含HTML签名的邮件

### 3. **技术邮件分析**
- 查看HTML邮件的源代码结构
- 分析邮件的样式和布局
- 调试邮件显示问题

## 🔍 故障排除

### 常见问题

1. **HTML标签页不显示**
   - 检查WebEngine是否正确安装
   - 重启应用程序
   - 查看控制台错误信息

2. **HTML内容显示异常**
   - 切换到文本模式查看原始内容
   - 检查邮件的HTML格式是否正确
   - 尝试重新选择邮件

3. **切换模式无响应**
   - 确保已选择邮件
   - 检查邮件是否包含内容
   - 查看状态栏的错误提示

### 性能优化建议

1. **大邮件处理**
   - 对于很大的HTML邮件，首次加载可能较慢
   - 可以先在文本模式下查看摘要
   - 需要时再切换到HTML模式

2. **内存使用**
   - 长时间使用后可重启应用程序
   - 避免同时打开过多HTML邮件
   - 定期清理浏览器缓存

## 🎉 总结

### 新功能优势
✅ **无需外部浏览器** - 直接在应用内查看HTML邮件  
✅ **完整格式支持** - 保持邮件的原始样式和布局  
✅ **快速切换** - 文本和HTML模式间无缝切换  
✅ **安全可靠** - 本地渲染，保护隐私和安全  
✅ **用户友好** - 直观的界面和操作方式  

### 使用建议
1. **日常使用**: 默认使用文本模式快速浏览，需要时切换到HTML模式
2. **重要邮件**: 使用HTML模式查看完整格式，确保不遗漏信息
3. **技术分析**: 结合"原始"按钮查看邮件源代码和HTML模式的渲染效果

---

**内嵌HTML邮件查看功能现已完全可用！** 🎉

享受更流畅、更专业的邮件查看体验吧！

**下一步建议**: 可以继续实现邮件搜索、操作管理等其他高级功能。
