#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
许可证本地存储管理模块
负责授权状态的持久化存储和加密处理
"""

import sqlite3
import hashlib
import base64
import json
import os
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from pathlib import Path


class LicenseStorage:
    """许可证本地存储管理器"""
    
    def __init__(self, db_path: str = "license.db"):
        """
        初始化存储管理器
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self._init_database()
    
    def _init_database(self):
        """初始化数据库表结构"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 创建许可证表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS licenses (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        license_key TEXT UNIQUE NOT NULL,
                        license_hash TEXT NOT NULL,
                        status TEXT NOT NULL DEFAULT 'inactive',
                        activation_time TEXT,
                        expiry_time TEXT,
                        machine_id TEXT,
                        validation_data TEXT,
                        created_at TEXT NOT NULL,
                        updated_at TEXT NOT NULL
                    )
                ''')
                
                # 创建验证记录表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS validation_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        license_key TEXT NOT NULL,
                        validation_time TEXT NOT NULL,
                        validation_result TEXT NOT NULL,
                        server_response TEXT,
                        error_message TEXT
                    )
                ''')
                
                conn.commit()
                
        except Exception as e:
            print(f"数据库初始化失败: {e}")
            raise
    
    def _encrypt_data(self, data: str) -> str:
        """加密数据"""
        try:
            # 使用base64编码进行基础加密
            encoded = base64.b64encode(data.encode('utf-8')).decode('utf-8')
            return encoded
        except Exception:
            return data
    
    def _decrypt_data(self, encrypted_data: str) -> str:
        """解密数据"""
        try:
            # 使用base64解码进行解密
            decoded = base64.b64decode(encrypted_data.encode('utf-8')).decode('utf-8')
            return decoded
        except Exception:
            return encrypted_data
    
    def _generate_license_hash(self, license_key: str) -> str:
        """生成许可证哈希值"""
        return hashlib.sha256(license_key.encode('utf-8')).hexdigest()
    
    def _get_machine_id(self) -> str:
        """获取机器唯一标识"""
        try:
            import platform
            import uuid
            
            # 组合多个系统信息生成唯一ID
            machine_info = f"{platform.node()}-{platform.system()}-{platform.processor()}"
            machine_hash = hashlib.md5(machine_info.encode('utf-8')).hexdigest()
            return machine_hash[:16]  # 取前16位
        except Exception:
            return "unknown-machine"
    
    def save_license(self, license_key: str, validation_data: Dict[str, Any]) -> bool:
        """
        保存许可证信息
        
        Args:
            license_key: 许可证密钥
            validation_data: 验证数据
            
        Returns:
            bool: 保存是否成功
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                license_hash = self._generate_license_hash(license_key)
                machine_id = self._get_machine_id()
                current_time = datetime.now().isoformat()
                
                # 加密敏感数据
                encrypted_key = self._encrypt_data(license_key)
                encrypted_validation_data = self._encrypt_data(json.dumps(validation_data))
                
                # 插入或更新许可证记录
                cursor.execute('''
                    INSERT OR REPLACE INTO licenses 
                    (license_key, license_hash, status, activation_time, expiry_time, 
                     machine_id, validation_data, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    encrypted_key,
                    license_hash,
                    validation_data.get('status', 'active'),
                    validation_data.get('activation_time', current_time),
                    validation_data.get('expiry_time'),
                    machine_id,
                    encrypted_validation_data,
                    current_time,
                    current_time
                ))
                
                conn.commit()
                return True
                
        except Exception as e:
            print(f"保存许可证失败: {e}")
            return False
    
    def get_license(self, license_key: str) -> Optional[Dict[str, Any]]:
        """
        获取许可证信息
        
        Args:
            license_key: 许可证密钥
            
        Returns:
            Optional[Dict]: 许可证信息，如果不存在返回None
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                license_hash = self._generate_license_hash(license_key)
                
                cursor.execute('''
                    SELECT * FROM licenses WHERE license_hash = ?
                ''', (license_hash,))
                
                row = cursor.fetchone()
                if not row:
                    return None
                
                # 解密数据
                decrypted_key = self._decrypt_data(row[1])
                decrypted_validation_data = self._decrypt_data(row[7])
                
                return {
                    'id': row[0],
                    'license_key': decrypted_key,
                    'license_hash': row[2],
                    'status': row[3],
                    'activation_time': row[4],
                    'expiry_time': row[5],
                    'machine_id': row[6],
                    'validation_data': json.loads(decrypted_validation_data) if decrypted_validation_data else {},
                    'created_at': row[8],
                    'updated_at': row[9]
                }
                
        except Exception as e:
            print(f"获取许可证失败: {e}")
            return None
    
    def is_license_valid(self, license_key: str) -> bool:
        """
        检查许可证是否有效
        
        Args:
            license_key: 许可证密钥
            
        Returns:
            bool: 许可证是否有效
        """
        license_info = self.get_license(license_key)
        if not license_info:
            return False
        
        # 检查状态
        if license_info['status'] != 'active':
            return False
        
        # 检查过期时间
        if license_info['expiry_time']:
            try:
                expiry_time = datetime.fromisoformat(license_info['expiry_time'])
                if datetime.now() > expiry_time:
                    return False
            except Exception:
                pass
        
        return True
    
    def log_validation(self, license_key: str, result: str, server_response: str = None, error_message: str = None):
        """
        记录验证日志
        
        Args:
            license_key: 许可证密钥
            result: 验证结果
            server_response: 服务器响应
            error_message: 错误信息
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                current_time = datetime.now().isoformat()
                
                cursor.execute('''
                    INSERT INTO validation_logs 
                    (license_key, validation_time, validation_result, server_response, error_message)
                    VALUES (?, ?, ?, ?, ?)
                ''', (
                    self._generate_license_hash(license_key),
                    current_time,
                    result,
                    server_response,
                    error_message
                ))
                
                conn.commit()
                
        except Exception as e:
            print(f"记录验证日志失败: {e}")
    
    def clear_expired_licenses(self):
        """清理过期的许可证"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                current_time = datetime.now().isoformat()
                
                cursor.execute('''
                    DELETE FROM licenses 
                    WHERE expiry_time IS NOT NULL AND expiry_time < ?
                ''', (current_time,))
                
                conn.commit()
                
        except Exception as e:
            print(f"清理过期许可证失败: {e}")
