#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志查看器对话框
提供实时日志查看、历史日志筛选、关键词搜索等功能
"""

import os
import sys
import logging
import re
from datetime import datetime, timedelta
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTextEdit, QPushButton,
    QComboBox, QLineEdit, QLabel, QDateEdit, QCheckBox,
    QSplitter, QGroupBox, QFileDialog, QMessageBox,
    QProgressDialog, QApplication
)
from PySide6.QtCore import Qt, QTimer, QThread, Signal, QDate
from PySide6.QtGui import QFont, QTextCursor, QColor

class LogReaderThread(QThread):
    """日志读取线程"""
    log_updated = Signal(str)
    
    def __init__(self, log_file_path):
        super().__init__()
        self.log_file_path = log_file_path
        self.running = False
        self.last_position = 0
    
    def run(self):
        """运行日志读取"""
        self.running = True
        while self.running:
            try:
                if os.path.exists(self.log_file_path):
                    with open(self.log_file_path, 'r', encoding='utf-8') as f:
                        f.seek(self.last_position)
                        new_content = f.read()
                        if new_content:
                            self.log_updated.emit(new_content)
                            self.last_position = f.tell()
                
                self.msleep(1000)  # 每秒检查一次
            except Exception as e:
                self.log_updated.emit(f"读取日志文件失败: {e}\n")
                self.msleep(5000)  # 出错时等待5秒
    
    def stop(self):
        """停止日志读取"""
        self.running = False

class LogViewerDialog(QDialog):
    """日志查看器对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("📋 日志查看器")
        self.setModal(False)
        self.resize(1000, 700)
        
        # 日志读取线程
        self.log_reader_thread = None
        
        # 设置日志目录
        self.log_directory = "logs"
        if not os.path.exists(self.log_directory):
            os.makedirs(self.log_directory)
        
        self.setup_ui()
        self.setup_connections()
        self.load_log_files()
        
        # 设置日志记录
        self.logger = logging.getLogger(__name__)
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        layout.addWidget(splitter)
        
        # 左侧控制面板
        control_panel = self.create_control_panel()
        splitter.addWidget(control_panel)
        
        # 右侧日志显示区域
        log_display = self.create_log_display()
        splitter.addWidget(log_display)
        
        # 设置分割器比例
        splitter.setSizes([300, 700])
        
        # 底部按钮
        button_layout = QHBoxLayout()
        
        self.export_btn = QPushButton("💾 导出日志")
        self.export_btn.setToolTip("将当前显示的日志导出到文件")
        button_layout.addWidget(self.export_btn)
        
        self.clear_btn = QPushButton("🗑️ 清空显示")
        self.clear_btn.setToolTip("清空当前日志显示区域")
        button_layout.addWidget(self.clear_btn)
        
        button_layout.addStretch()
        
        self.close_btn = QPushButton("❌ 关闭")
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)
    
    def create_control_panel(self):
        """创建控制面板"""
        panel = QGroupBox("日志控制")
        layout = QVBoxLayout(panel)
        
        # 日志文件选择
        file_group = QGroupBox("日志文件")
        file_layout = QVBoxLayout(file_group)
        
        self.file_combo = QComboBox()
        self.file_combo.setToolTip("选择要查看的日志文件")
        file_layout.addWidget(QLabel("选择日志文件:"))
        file_layout.addWidget(self.file_combo)
        
        self.refresh_files_btn = QPushButton("🔄 刷新文件列表")
        file_layout.addWidget(self.refresh_files_btn)
        
        layout.addWidget(file_group)
        
        # 实时日志
        realtime_group = QGroupBox("实时日志")
        realtime_layout = QVBoxLayout(realtime_group)
        
        self.realtime_checkbox = QCheckBox("启用实时日志监控")
        self.realtime_checkbox.setToolTip("监控当前日志文件的实时更新")
        realtime_layout.addWidget(self.realtime_checkbox)
        
        layout.addWidget(realtime_group)
        
        # 日志级别过滤
        level_group = QGroupBox("日志级别过滤")
        level_layout = QVBoxLayout(level_group)
        
        self.level_combo = QComboBox()
        self.level_combo.addItems(["全部", "DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"])
        self.level_combo.setCurrentText("全部")
        level_layout.addWidget(QLabel("日志级别:"))
        level_layout.addWidget(self.level_combo)
        
        layout.addWidget(level_group)
        
        # 日期筛选
        date_group = QGroupBox("日期筛选")
        date_layout = QVBoxLayout(date_group)
        
        self.date_filter_checkbox = QCheckBox("启用日期筛选")
        date_layout.addWidget(self.date_filter_checkbox)
        
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addDays(-7))
        self.start_date.setEnabled(False)
        date_layout.addWidget(QLabel("开始日期:"))
        date_layout.addWidget(self.start_date)
        
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setEnabled(False)
        date_layout.addWidget(QLabel("结束日期:"))
        date_layout.addWidget(self.end_date)
        
        layout.addWidget(date_group)
        
        # 关键词搜索
        search_group = QGroupBox("关键词搜索")
        search_layout = QVBoxLayout(search_group)
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入搜索关键词...")
        search_layout.addWidget(self.search_input)
        
        self.search_btn = QPushButton("🔍 搜索")
        self.highlight_checkbox = QCheckBox("高亮显示搜索结果")
        self.highlight_checkbox.setChecked(True)
        
        search_layout.addWidget(self.search_btn)
        search_layout.addWidget(self.highlight_checkbox)
        
        layout.addWidget(search_group)
        
        # 应用筛选按钮
        self.apply_filter_btn = QPushButton("✅ 应用筛选")
        self.apply_filter_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                font-weight: bold;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        layout.addWidget(self.apply_filter_btn)
        
        return panel
    
    def create_log_display(self):
        """创建日志显示区域"""
        panel = QGroupBox("日志内容")
        layout = QVBoxLayout(panel)
        
        # 状态信息
        self.status_label = QLabel("准备就绪")
        self.status_label.setStyleSheet("color: #666; font-size: 12px;")
        layout.addWidget(self.status_label)
        
        # 日志文本显示
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 10))
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #1e1e1e;
                color: #ffffff;
                border: 1px solid #555;
            }
        """)
        layout.addWidget(self.log_text)
        
        return panel
    
    def setup_connections(self):
        """设置信号连接"""
        self.file_combo.currentTextChanged.connect(self.on_file_changed)
        self.refresh_files_btn.clicked.connect(self.load_log_files)
        self.realtime_checkbox.toggled.connect(self.toggle_realtime_monitoring)
        self.date_filter_checkbox.toggled.connect(self.toggle_date_filter)
        self.search_btn.clicked.connect(self.search_logs)
        self.search_input.returnPressed.connect(self.search_logs)
        self.apply_filter_btn.clicked.connect(self.apply_filters)
        self.export_btn.clicked.connect(self.export_logs)
        self.clear_btn.clicked.connect(self.clear_display)
        self.close_btn.clicked.connect(self.close)
    
    def load_log_files(self):
        """加载日志文件列表"""
        try:
            self.file_combo.clear()
            
            if not os.path.exists(self.log_directory):
                self.status_label.setText("日志目录不存在")
                return
            
            log_files = []
            for file in os.listdir(self.log_directory):
                if file.endswith('.log'):
                    log_files.append(file)
            
            # 按修改时间排序，最新的在前
            log_files.sort(key=lambda x: os.path.getmtime(os.path.join(self.log_directory, x)), reverse=True)
            
            if log_files:
                self.file_combo.addItems(log_files)
                self.status_label.setText(f"找到 {len(log_files)} 个日志文件")
            else:
                self.status_label.setText("未找到日志文件")
                
        except Exception as e:
            self.status_label.setText(f"加载日志文件失败: {e}")
            self.logger.error(f"加载日志文件失败: {e}")
    
    def on_file_changed(self, filename):
        """日志文件改变时的处理"""
        if filename:
            self.status_label.setText(f"选择了日志文件: {filename}")
            if not self.realtime_checkbox.isChecked():
                self.load_log_content(filename)
    
    def toggle_realtime_monitoring(self, enabled):
        """切换实时监控"""
        if enabled:
            self.start_realtime_monitoring()
        else:
            self.stop_realtime_monitoring()
    
    def start_realtime_monitoring(self):
        """开始实时监控"""
        current_file = self.file_combo.currentText()
        if not current_file:
            self.realtime_checkbox.setChecked(False)
            QMessageBox.warning(self, "警告", "请先选择一个日志文件")
            return
        
        log_file_path = os.path.join(self.log_directory, current_file)
        
        if self.log_reader_thread:
            self.log_reader_thread.stop()
            self.log_reader_thread.wait()
        
        self.log_reader_thread = LogReaderThread(log_file_path)
        self.log_reader_thread.log_updated.connect(self.append_log_content)
        self.log_reader_thread.start()
        
        self.status_label.setText(f"正在实时监控: {current_file}")
    
    def stop_realtime_monitoring(self):
        """停止实时监控"""
        if self.log_reader_thread:
            self.log_reader_thread.stop()
            self.log_reader_thread.wait()
            self.log_reader_thread = None
        
        self.status_label.setText("已停止实时监控")
    
    def toggle_date_filter(self, enabled):
        """切换日期筛选"""
        self.start_date.setEnabled(enabled)
        self.end_date.setEnabled(enabled)
    
    def load_log_content(self, filename):
        """加载日志内容"""
        try:
            log_file_path = os.path.join(self.log_directory, filename)
            
            if not os.path.exists(log_file_path):
                self.log_text.setText("日志文件不存在")
                return
            
            with open(log_file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            self.log_text.setText(content)
            self.status_label.setText(f"已加载日志文件: {filename}")
            
            # 滚动到底部
            cursor = self.log_text.textCursor()
            cursor.movePosition(QTextCursor.MoveOperation.End)
            self.log_text.setTextCursor(cursor)
            
        except Exception as e:
            self.log_text.setText(f"加载日志文件失败: {e}")
            self.status_label.setText(f"加载失败: {e}")
            self.logger.error(f"加载日志文件失败: {e}")
    
    def append_log_content(self, content):
        """追加日志内容"""
        cursor = self.log_text.textCursor()
        cursor.movePosition(QTextCursor.MoveOperation.End)
        cursor.insertText(content)
        self.log_text.setTextCursor(cursor)
        
        # 自动滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
    
    def apply_filters(self):
        """应用筛选条件"""
        try:
            current_file = self.file_combo.currentText()
            if not current_file:
                QMessageBox.warning(self, "警告", "请先选择一个日志文件")
                return
            
            # 停止实时监控
            if self.realtime_checkbox.isChecked():
                self.stop_realtime_monitoring()
                self.realtime_checkbox.setChecked(False)
            
            # 加载原始内容
            log_file_path = os.path.join(self.log_directory, current_file)
            with open(log_file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 应用筛选
            filtered_lines = self.filter_log_lines(lines)
            
            # 显示筛选结果
            self.log_text.setText(''.join(filtered_lines))
            self.status_label.setText(f"筛选完成: {len(filtered_lines)}/{len(lines)} 行")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"应用筛选失败: {e}")
            self.logger.error(f"应用筛选失败: {e}")
    
    def filter_log_lines(self, lines):
        """筛选日志行"""
        filtered_lines = []
        
        level_filter = self.level_combo.currentText()
        date_filter_enabled = self.date_filter_checkbox.isChecked()
        
        for line in lines:
            # 日志级别筛选
            if level_filter != "全部":
                if level_filter not in line:
                    continue
            
            # 日期筛选
            if date_filter_enabled:
                if not self.is_line_in_date_range(line):
                    continue
            
            filtered_lines.append(line)
        
        return filtered_lines
    
    def is_line_in_date_range(self, line):
        """检查日志行是否在日期范围内"""
        try:
            # 尝试从日志行中提取日期
            date_pattern = r'(\d{4}-\d{2}-\d{2})'
            match = re.search(date_pattern, line)
            
            if match:
                log_date = datetime.strptime(match.group(1), '%Y-%m-%d').date()
                start_date = self.start_date.date().toPython()
                end_date = self.end_date.date().toPython()
                
                return start_date <= log_date <= end_date
            
            return True  # 如果无法提取日期，则包含该行
            
        except Exception:
            return True
    
    def search_logs(self):
        """搜索日志"""
        keyword = self.search_input.text().strip()
        if not keyword:
            return
        
        try:
            # 获取当前文本
            content = self.log_text.toPlainText()
            
            if keyword.lower() in content.lower():
                # 高亮显示搜索结果
                if self.highlight_checkbox.isChecked():
                    self.highlight_search_results(keyword)
                
                self.status_label.setText(f"搜索到关键词: {keyword}")
            else:
                self.status_label.setText(f"未找到关键词: {keyword}")
                QMessageBox.information(self, "搜索结果", f"未找到关键词: {keyword}")
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"搜索失败: {e}")
            self.logger.error(f"搜索失败: {e}")
    
    def highlight_search_results(self, keyword):
        """高亮显示搜索结果"""
        try:
            cursor = self.log_text.textCursor()
            format = cursor.charFormat()
            
            # 清除之前的高亮
            cursor.select(QTextCursor.SelectionType.Document)
            cursor.setCharFormat(format)
            cursor.clearSelection()
            
            # 高亮新的搜索结果
            cursor.movePosition(QTextCursor.MoveOperation.Start)
            
            while True:
                cursor = self.log_text.document().find(keyword, cursor)
                if cursor.isNull():
                    break
                
                # 设置高亮格式
                highlight_format = cursor.charFormat()
                highlight_format.setBackground(QColor("yellow"))
                highlight_format.setForeground(QColor("black"))
                cursor.setCharFormat(highlight_format)
                
        except Exception as e:
            self.logger.error(f"高亮显示失败: {e}")
    
    def export_logs(self):
        """导出日志"""
        try:
            content = self.log_text.toPlainText()
            if not content:
                QMessageBox.warning(self, "警告", "没有日志内容可导出")
                return
            
            filename, _ = QFileDialog.getSaveFileName(
                self,
                "导出日志",
                f"log_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
                "文本文件 (*.txt);;所有文件 (*)"
            )
            
            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                QMessageBox.information(self, "成功", f"日志已导出到: {filename}")
                self.status_label.setText(f"日志已导出到: {filename}")
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出日志失败: {e}")
            self.logger.error(f"导出日志失败: {e}")
    
    def clear_display(self):
        """清空显示"""
        self.log_text.clear()
        self.status_label.setText("显示已清空")
    
    def closeEvent(self, event):
        """关闭事件"""
        self.stop_realtime_monitoring()
        event.accept()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    dialog = LogViewerDialog()
    dialog.show()
    sys.exit(app.exec())
