#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
许可证管理器模块
统一管理许可证验证、存储和状态检查
"""

import os
import json
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Tuple, List
from pathlib import Path

from .license_validator import LicenseValidator
from .license_storage import LicenseStorage


class LicenseManager:
    """许可证管理器 - 统一管理授权系统"""
    
    def __init__(self,
                 api_base_url: str = "https://ka.915277.xyz/api/v1",
                 db_path: str = "license.db",
                 config_path: str = "license_config.json"):
        """
        初始化许可证管理器
        
        Args:
            api_base_url: 验证服务器API地址
            db_path: 本地数据库路径
            config_path: 配置文件路径
        """
        self.validator = LicenseValidator(api_base_url)
        self.storage = LicenseStorage(db_path)
        self.config_path = config_path
        self.config = self._load_config()
        
        # 当前激活的许可证
        self._current_license = None
        self._license_status = 'inactive'
        
        # 自动加载已保存的许可证
        self._load_saved_license()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        default_config = {
            'auto_validate': True,
            'validation_interval': 24,  # 小时
            'offline_grace_period': 72,  # 小时
        }
        
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # 合并默认配置
                    default_config.update(config)
            return default_config
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return default_config
    
    def _save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def _load_saved_license(self):
        """加载已保存的许可证"""
        try:
            # 这里可以从配置文件或其他地方获取上次使用的许可证密钥
            # 暂时跳过自动加载，需要用户手动输入
            pass
        except Exception as e:
            print(f"加载已保存许可证失败: {e}")
    
    def validate_license_key(self, license_key: str, force_online: bool = False) -> Tuple[bool, str, Dict[str, Any]]:
        """
        验证许可证密钥（简化版 - 仅基于时间验证）

        Args:
            license_key: 许可证密钥
            force_online: 是否强制在线验证

        Returns:
            Tuple[bool, str, Dict]: (验证是否成功, 消息, 简化的许可证信息)
        """
        if not license_key or not license_key.strip():
            return False, "密钥不能为空", {}

        license_key = license_key.strip()

        # 首先检查本地缓存
        if not force_online:
            local_info = self.storage.get_license(license_key)
            if local_info and self._is_license_time_valid(local_info):
                self._current_license = license_key
                self._license_status = 'active'
                return True, "密钥有效", self._simplify_license_info(local_info)

        # 进行在线验证
        try:
            success, validation_data = self.validator.validate_license(license_key)

            if success:
                # 验证成功，保存到本地
                self.storage.save_license(license_key, validation_data)

                self._current_license = license_key
                self._license_status = 'active'

                return True, "密钥验证成功", self._simplify_license_info(validation_data)
            else:
                # 验证失败
                error_msg = validation_data.get('error', '密钥验证失败')

                self._current_license = None
                self._license_status = 'invalid'

                return False, error_msg, {}

        except Exception as e:
            # 网络验证失败，检查本地缓存
            local_info = self.storage.get_license(license_key)
            if local_info and self._is_license_time_valid(local_info):
                self._current_license = license_key
                self._license_status = 'active'
                return True, f"网络验证失败，使用离线模式", self._simplify_license_info(local_info)
            else:
                self._current_license = None
                self._license_status = 'invalid'
                return False, f"网络验证失败且无有效缓存: {str(e)}", {}
    
    def _is_license_time_valid(self, license_info: Dict[str, Any]) -> bool:
        """检查许可证是否在有效期内（简化版）"""
        try:
            expiry_time = license_info.get('expiry_time')
            if not expiry_time:
                return False

            # 支持多种时间格式
            if isinstance(expiry_time, str):
                try:
                    expiry_dt = datetime.fromisoformat(expiry_time.replace('Z', '+00:00'))
                except ValueError:
                    # 尝试其他格式
                    expiry_dt = datetime.strptime(expiry_time, '%Y-%m-%d %H:%M:%S')
            else:
                return False

            return datetime.now() < expiry_dt
        except Exception:
            return False

    def _simplify_license_info(self, license_info: Dict[str, Any]) -> Dict[str, Any]:
        """简化许可证信息，只保留关键字段"""
        return {
            'status': 'active' if self._is_license_time_valid(license_info) else 'expired',
            'expiry_time': license_info.get('expiry_time', '未知'),
            'license_key': license_info.get('license_key', self._current_license)
        }
    

    

    
    def check_license_status(self, license_key: str = None) -> Tuple[bool, Dict[str, Any]]:
        """
        检查许可证状态（简化版）

        Args:
            license_key: 许可证密钥，如果为None则检查当前许可证

        Returns:
            Tuple[bool, Dict]: (检查是否成功, 简化的状态信息)
        """
        if license_key is None:
            license_key = self._current_license

        if not license_key:
            return False, {'error': '没有可检查的许可证'}

        try:
            # 检查本地状态
            local_info = self.storage.get_license(license_key)

            if local_info:
                simplified_info = self._simplify_license_info(local_info)
                return True, simplified_info
            else:
                return False, {'error': '无法获取许可证状态'}

        except Exception as e:
            return False, {'error': f'检查状态时发生错误: {str(e)}'}
    
    def is_licensed(self) -> bool:
        """检查当前是否有有效许可证"""
        return self._license_status == 'active' and self._current_license is not None
    
    def get_current_license_info(self) -> Optional[Dict[str, Any]]:
        """获取当前许可证信息（简化版）"""
        if not self._current_license:
            return None

        local_info = self.storage.get_license(self._current_license)
        if local_info:
            return self._simplify_license_info(local_info)
        return None
    

    
    def test_connection(self) -> Tuple[bool, str]:
        """测试与验证服务器的连接"""
        return self.validator.test_connection()
    
    def cleanup_expired_licenses(self):
        """清理过期的许可证"""
        try:
            self.storage.clear_expired_licenses()
        except Exception as e:
            print(f"清理过期许可证失败: {e}")
    

