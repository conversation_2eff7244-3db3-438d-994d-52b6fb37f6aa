/* 微软邮箱批量管理1.0 - 主样式文件 */
/* 提取自enterprise_email_manager.py的内联样式 */

/* ===== 全局样式 ===== */
QPushButton {
    font-size: 11px;
    padding: 2px 6px;
    border: 1px solid #ccc;
    border-radius: 3px;
    background-color: #f8f9fa;
    min-height: 18px;
}

QPushButton:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
}

QPushButton:pressed {
    background-color: #dee2e6;
}

QComboBox {
    font-size: 11px;
    padding: 2px 4px;
    border: 1px solid #ccc;
    border-radius: 3px;
    min-height: 18px;
}

/* 🔒 全局文本保护样式 */
QLabel {
    selection-background-color: transparent;
}

QTableWidget::item {
    selection-background-color: #0d6efd;
    selection-color: white;
}

/* ===== 分割器样式 ===== */
QSplitter::handle {
    background-color: #e0e0e0;
    border: 1px solid #c0c0c0;
}

QSplitter::handle:horizontal {
    width: 3px;
    margin: 2px 0;
}

QSplitter::handle:vertical {
    height: 3px;
    margin: 0 2px;
}

QSplitter::handle:hover {
    background-color: #c0c0c0;
}

/* ===== 工具栏样式 ===== */
QToolBar {
    spacing: 2px;
    padding: 2px;
    border: none;
}

QToolBar QPushButton {
    padding: 2px 6px;
    margin: 1px;
    font-size: 11px;
}

/* ===== 面板标题样式 ===== */
QLabel#panel-title {
    background-color: #f8f9fa;
    border: 1px solid #c0c0c0;
    padding: 2px 4px;
    font-weight: bold;
    font-size: 11px;
    color: #333333;
}

/* ===== 账户树样式 ===== */
QTreeWidget {
    font-size: 12px;
    border: 1px solid #ccc;
    background-color: white;
    outline: none;
}

QTreeWidget::item {
    height: 20px;
    padding: 1px 4px;
    color: #212529;
    border: none;
    text-align: left;
    margin-left: 0px;
}

QTreeWidget::item:selected {
    background-color: #0d6efd;
    color: white;
}

QTreeWidget::item:hover:!selected {
    background-color: #e9ecef;
    color: #212529;
}

QTreeWidget::item:focus {
    background-color: #0d6efd;
    color: white;
    outline: none;
}

QTreeWidget::branch {
    background-color: transparent;
    width: 0px;
}

QTreeWidget::branch:selected {
    background-color: #0d6efd;
}

/* 分组项目样式 */
QTreeWidget::item[data-type="group"] {
    font-weight: bold;
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

/* 账户项目样式 */
QTreeWidget::item[data-type="account"] {
    padding-left: 8px;
    background-color: #ffffff;
}

/* ===== 邮件表格样式 ===== */
QTableWidget {
    font-size: 12px;
    gridline-color: #e0e0e0;
    border: 1px solid #ccc;
    background-color: white;
    alternate-background-color: #f8f9fa;
}

QTableWidget::item {
    padding: 2px 4px;
    border: none;
    color: #212529;
    background-color: transparent;
}

QTableWidget::item:selected {
    background-color: #0d6efd;
    color: white;
    border: none;
    font-weight: normal;
}

QTableWidget::item:hover:!selected {
    background-color: #e9ecef;
    color: #212529;
}

QTableWidget::item:alternate {
    background-color: #f8f9fa;
}

QTableWidget::item:alternate:selected {
    background-color: #0d6efd;
    color: white;
}

QTableWidget::item:focus {
    background-color: #0d6efd;
    color: white;
    outline: none;
}

QHeaderView::section {
    background-color: #f1f3f4;
    padding: 2px 4px;
    border: 1px solid #dee2e6;
    font-size: 11px;
    font-weight: bold;
    color: #495057;
}

QHeaderView::section:hover {
    background-color: #e9ecef;
}

/* ===== 状态栏样式 ===== */
QStatusBar {
    border-top: 1px solid #ccc;
    padding: 2px;
    font-size: 11px;
}

QStatusBar QLabel {
    padding: 1px 4px;
}

/* ===== 附件标签样式 ===== */
QLabel#attachment-label-with-attachments {
    color: #0078d4;
}

QLabel#attachment-label-no-attachments {
    color: #666666;
}

/* ===== 对话框样式 ===== */
QDialog {
    background-color: #f0f0f0;
}

QDialog QTextEdit {
    background-color: #ffffff;
    border: 1px solid #ccc;
    font-family: 'Courier New', monospace;
    font-size: 10px;
}

QDialog QPushButton {
    min-width: 80px;
    padding: 5px 10px;
}

/* ===== 邮件预览样式 ===== */
QTextEdit {
    border: 1px solid #ccc;
    background-color: white;
    font-size: 12px;
    padding: 4px;
}

QTextEdit:read-only {
    background-color: #fafafa;
}

/* ===== 进度条样式 ===== */
QProgressBar {
    border: 1px solid #ccc;
    border-radius: 3px;
    text-align: center;
    font-size: 10px;
}

QProgressBar::chunk {
    background-color: #0d6efd;
    border-radius: 2px;
}

/* ===== 输入框样式 ===== */
QLineEdit {
    border: 1px solid #ccc;
    border-radius: 3px;
    padding: 2px 4px;
    font-size: 11px;
}

QLineEdit:focus {
    border-color: #0d6efd;
    outline: none;
}

/* ===== 复选框样式 ===== */
QCheckBox {
    font-size: 11px;
    spacing: 5px;
}

QCheckBox::indicator {
    width: 13px;
    height: 13px;
}

QCheckBox::indicator:unchecked {
    border: 1px solid #ccc;
    background-color: white;
}

QCheckBox::indicator:checked {
    border: 1px solid #0d6efd;
    background-color: #0d6efd;
}

/* ===== 滚动条样式 ===== */
QScrollBar:vertical {
    background-color: #f1f1f1;
    width: 12px;
    border: none;
}

QScrollBar::handle:vertical {
    background-color: #c1c1c1;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #a8a8a8;
}

QScrollBar:horizontal {
    background-color: #f1f1f1;
    height: 12px;
    border: none;
}

QScrollBar::handle:horizontal {
    background-color: #c1c1c1;
    border-radius: 6px;
    min-width: 20px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #a8a8a8;
}
