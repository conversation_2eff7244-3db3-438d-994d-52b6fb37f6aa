#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试授权成功对话框
演示新的到期时间显示UI
"""

import sys
from datetime import datetime, timedelta
from PySide6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from PySide6.QtCore import Qt

# 添加auth模块到路径
sys.path.append('.')

from auth.license_success_dialog import LicenseSuccessDialog


class TestMainWindow(QMainWindow):
    """测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("授权成功界面测试")
        self.setFixedSize(400, 300)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(50, 50, 50, 50)
        
        # 标题
        title_label = QPushButton("🧪 授权成功界面测试")
        title_label.setEnabled(False)
        title_label.setStyleSheet("""
            QPushButton {
                font-size: 18px;
                font-weight: bold;
                color: #333;
                background: transparent;
                border: none;
                padding: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # 测试按钮
        self._create_test_buttons(layout)
        
        # 设置样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8f9fa;
                font-family: 'Microsoft YaHei', 'Segoe UI', Arial, sans-serif;
            }
            
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4CAF50, stop:1 #45a049);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
                margin: 5px;
            }
            
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #45a049, stop:1 #3d8b40);
            }
            
            QPushButton:pressed {
                background: #3d8b40;
            }
        """)
    
    def _create_test_buttons(self, layout):
        """创建测试按钮"""
        # 永久许可证测试
        permanent_btn = QPushButton("🎉 测试永久许可证")
        permanent_btn.clicked.connect(self._test_permanent_license)
        layout.addWidget(permanent_btn)
        
        # 正常许可证测试（还有很长时间）
        normal_btn = QPushButton("✅ 测试正常许可证（1年后到期）")
        normal_btn.clicked.connect(self._test_normal_license)
        layout.addWidget(normal_btn)
        
        # 即将到期许可证测试
        warning_btn = QPushButton("⚠️ 测试即将到期许可证（30天后）")
        warning_btn.clicked.connect(self._test_warning_license)
        layout.addWidget(warning_btn)
        
        # 紧急到期许可证测试
        urgent_btn = QPushButton("🚨 测试紧急到期许可证（3天后）")
        urgent_btn.clicked.connect(self._test_urgent_license)
        layout.addWidget(urgent_btn)
        
        # 已过期许可证测试
        expired_btn = QPushButton("❌ 测试已过期许可证")
        expired_btn.clicked.connect(self._test_expired_license)
        layout.addWidget(expired_btn)
    
    def _test_permanent_license(self):
        """测试永久许可证"""
        license_data = {
            'license_key': 'PERM-ANEN-T123-4567',
            'license_type': 'enterprise',
            'status': 'active',
            'user_name': '永久用户',
            'company_name': '永久科技有限公司',
            'user_email': '<EMAIL>',
            'max_activations': 999,
            'created_at': '2024-01-01T00:00:00',
            'expiry_time': '永久',
            'features': ['email_management', 'batch_import', 'advanced_search']
        }
        
        dialog = LicenseSuccessDialog(license_data, self)
        dialog.start_application.connect(self._on_start_application)
        dialog.exec()
    
    def _test_normal_license(self):
        """测试正常许可证（1年后到期）"""
        expiry_time = datetime.now() + timedelta(days=365)
        
        license_data = {
            'license_key': 'NORM-AL12-3456-7890',
            'license_type': 'premium',
            'status': 'active',
            'user_name': '正常用户',
            'company_name': '正常科技有限公司',
            'user_email': '<EMAIL>',
            'max_activations': 5,
            'created_at': '2024-01-01T00:00:00',
            'expiry_time': expiry_time.strftime('%Y-%m-%dT%H:%M:%S'),
            'features': ['email_management', 'batch_import']
        }
        
        dialog = LicenseSuccessDialog(license_data, self)
        dialog.start_application.connect(self._on_start_application)
        dialog.exec()
    
    def _test_warning_license(self):
        """测试即将到期许可证（30天后）"""
        expiry_time = datetime.now() + timedelta(days=30)
        
        license_data = {
            'license_key': 'WARN-ING1-2345-6789',
            'license_type': 'standard',
            'status': 'active',
            'user_name': '警告用户',
            'company_name': '警告科技有限公司',
            'user_email': '<EMAIL>',
            'max_activations': 3,
            'created_at': '2024-01-01T00:00:00',
            'expiry_time': expiry_time.strftime('%Y-%m-%dT%H:%M:%S'),
            'features': ['email_management']
        }
        
        dialog = LicenseSuccessDialog(license_data, self)
        dialog.start_application.connect(self._on_start_application)
        dialog.exec()
    
    def _test_urgent_license(self):
        """测试紧急到期许可证（3天后）"""
        expiry_time = datetime.now() + timedelta(days=3, hours=12, minutes=30)
        
        license_data = {
            'license_key': 'URGE-NT12-3456-7890',
            'license_type': 'premium',
            'status': 'active',
            'user_name': '紧急用户',
            'company_name': '紧急科技有限公司',
            'user_email': '<EMAIL>',
            'max_activations': 2,
            'created_at': '2024-01-01T00:00:00',
            'expiry_time': expiry_time.strftime('%Y-%m-%dT%H:%M:%S'),
            'features': ['email_management', 'advanced_search']
        }
        
        dialog = LicenseSuccessDialog(license_data, self)
        dialog.start_application.connect(self._on_start_application)
        dialog.exec()
    
    def _test_expired_license(self):
        """测试已过期许可证"""
        expiry_time = datetime.now() - timedelta(days=10)
        
        license_data = {
            'license_key': 'EXPI-RED1-2345-6789',
            'license_type': 'standard',
            'status': 'expired',
            'user_name': '过期用户',
            'company_name': '过期科技有限公司',
            'user_email': '<EMAIL>',
            'max_activations': 1,
            'created_at': '2024-01-01T00:00:00',
            'expiry_time': expiry_time.strftime('%Y-%m-%dT%H:%M:%S'),
            'features': ['email_management']
        }
        
        dialog = LicenseSuccessDialog(license_data, self)
        dialog.start_application.connect(self._on_start_application)
        dialog.exec()
    
    def _on_start_application(self):
        """开始使用应用程序"""
        print("🚀 用户选择开始使用应用程序！")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("授权成功界面测试")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("微软Ou工具")
    
    # 创建并显示主窗口
    window = TestMainWindow()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
