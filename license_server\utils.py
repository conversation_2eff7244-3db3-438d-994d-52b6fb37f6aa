#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
许可证服务器工具函数
"""

import re
import hashlib
import platform
import json
from datetime import datetime
from functools import wraps
from flask import request, jsonify, current_app


def generate_machine_fingerprint(request_data):
    """从请求数据生成机器指纹"""
    try:
        # 获取机器信息
        machine_info = request_data.get('machine_fingerprint', '')
        
        if not machine_info:
            # 如果没有提供机器指纹，使用IP地址作为备选
            ip_address = request.remote_addr or 'unknown'
            user_agent = request.headers.get('User-Agent', 'unknown')
            machine_info = f"{ip_address}-{user_agent}"
        
        # 生成哈希
        fingerprint = hashlib.sha256(machine_info.encode('utf-8')).hexdigest()
        return fingerprint[:32]  # 取前32位
        
    except Exception:
        return 'unknown-fingerprint'


def validate_license_key_format(license_key):
    """验证许可证密钥格式"""
    if not license_key:
        return False
    
    # 格式: XXXX-XXXX-XXXX-XXXX (16个字符，4个段，每段4个字符)
    pattern = r'^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$'
    return bool(re.match(pattern, license_key.upper()))


def get_client_info(request):
    """获取客户端信息"""
    return {
        'ip_address': request.remote_addr,
        'user_agent': request.headers.get('User-Agent', ''),
        'timestamp': datetime.utcnow().isoformat()
    }


def validate_request_data(required_fields):
    """验证请求数据装饰器"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not request.is_json:
                return jsonify({'error': '请求必须是JSON格式'}), 400
            
            data = request.get_json()
            if not data:
                return jsonify({'error': '请求数据不能为空'}), 400
            
            missing_fields = []
            for field in required_fields:
                if field not in data or not data[field]:
                    missing_fields.append(field)
            
            if missing_fields:
                return jsonify({
                    'error': '缺少必需字段',
                    'missing_fields': missing_fields
                }), 400
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator


def log_api_request(endpoint, data=None, result=None):
    """记录API请求日志"""
    try:
        log_data = {
            'endpoint': endpoint,
            'ip_address': request.remote_addr,
            'user_agent': request.headers.get('User-Agent', ''),
            'timestamp': datetime.utcnow().isoformat(),
            'data': data,
            'result': result
        }
        
        current_app.logger.info(f"API请求: {json.dumps(log_data, ensure_ascii=False)}")
        
    except Exception as e:
        current_app.logger.error(f"记录API请求日志失败: {e}")


def create_api_response(success=True, message='', data=None, error_code=None):
    """创建标准API响应"""
    response = {
        'success': success,
        'message': message,
        'timestamp': datetime.utcnow().isoformat()
    }
    
    if data is not None:
        response['data'] = data
    
    if error_code is not None:
        response['error_code'] = error_code
    
    return response


def validate_license_features(features):
    """验证许可证功能列表"""
    if not features:
        return ['email_management']  # 默认功能
    
    if isinstance(features, str):
        try:
            features = json.loads(features)
        except json.JSONDecodeError:
            return ['email_management']
    
    if not isinstance(features, list):
        return ['email_management']
    
    # 可用功能列表
    available_features = [
        'email_management',
        'batch_import',
        'advanced_search',
        'attachment_management',
        'html_email_compose',
        'auto_sync',
        'multi_account',
        'export_import',
        'custom_filters',
        'email_templates'
    ]
    
    # 过滤有效功能
    valid_features = [f for f in features if f in available_features]
    
    # 确保至少有基本功能
    if not valid_features:
        valid_features = ['email_management']
    
    return valid_features


def format_license_response(license_obj, include_sensitive=False):
    """格式化许可证响应数据"""
    if not license_obj:
        return None
    
    # 解析功能列表
    features = []
    if license_obj.features:
        try:
            features = json.loads(license_obj.features)
        except json.JSONDecodeError:
            features = ['email_management']
    
    response_data = {
        'valid': license_obj.is_valid(),
        'status': license_obj.status,
        'license_type': license_obj.license_type,
        'product_name': license_obj.product_name,
        'version': license_obj.version,
        'features': features,
        'max_activations': license_obj.max_activations,
        'current_activations': license_obj.current_activations,
        'created_at': license_obj.created_at.isoformat() if license_obj.created_at else None,
        'activated_at': license_obj.activated_at.isoformat() if license_obj.activated_at else None,
        'expires_at': license_obj.expires_at.isoformat() if license_obj.expires_at else None,
        'server_time': datetime.utcnow().isoformat()
    }
    
    if include_sensitive:
        response_data.update({
            'license_key': license_obj.license_key,
            'user_name': license_obj.user_name,
            'user_email': license_obj.user_email,
            'company_name': license_obj.company_name
        })
    
    return response_data


def check_license_expiry(license_obj):
    """检查许可证是否即将过期"""
    if not license_obj.expires_at:
        return {'expired': False, 'days_remaining': None}
    
    now = datetime.utcnow()
    if now > license_obj.expires_at:
        return {'expired': True, 'days_remaining': 0}
    
    days_remaining = (license_obj.expires_at - now).days
    return {
        'expired': False,
        'days_remaining': days_remaining,
        'expires_soon': days_remaining <= 30  # 30天内过期
    }


def sanitize_user_input(data):
    """清理用户输入数据"""
    if isinstance(data, dict):
        sanitized = {}
        for key, value in data.items():
            if isinstance(value, str):
                # 移除潜在的危险字符
                sanitized[key] = value.strip()[:500]  # 限制长度
            elif isinstance(value, (int, float, bool)):
                sanitized[key] = value
            elif isinstance(value, dict):
                sanitized[key] = sanitize_user_input(value)
            elif isinstance(value, list):
                sanitized[key] = [sanitize_user_input(item) if isinstance(item, dict) else str(item)[:100] for item in value[:10]]  # 限制列表长度
        return sanitized
    elif isinstance(data, str):
        return data.strip()[:500]
    else:
        return data
