#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试科幻风格的授权成功对话框
"""

import sys
import os
from datetime import datetime, timedelta
from PySide6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget

# 确保能找到auth模块
# from auth.sci_fi_license_dialog import SciFiLicenseDialog
# The script's directory is automatically in sys.path
from auth.sci_fi_license_dialog import SciFiLicenseDialog


class TestMainWindow(QMainWindow):
    """测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("科幻授权界面测试")
        self.setFixedSize(400, 300)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(50, 50, 50, 50)
        
        test_button = QPushButton("🚀 启动授权测试")
        test_button.clicked.connect(self._test_normal_license)
        layout.addWidget(test_button)
        
        self.setStyleSheet("""
            QMainWindow { background-color: #0D0D1A; }
            QPushButton {
                background-color: #00A8A8;
                color: #0D0D1A;
                border: 1px solid #00A8A8;
                border-radius: 4px;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
                font-family: 'Geist Mono', monospace;
            }
            QPushButton:hover { background-color: #00E0E0; }
        """)
    
    def _test_normal_license(self):
        """测试正常许可证"""
        expiry_time = datetime.now() + timedelta(days=365)
        
        license_data = {
            'license_key': 'NORM-AL12-3456-7890',
            'license_type': 'premium',
            'status': 'active',
            'user_name': '测试用户',
            'company_name': '未来科技',
            'user_email': '<EMAIL>',
            'max_activations': 5,
            'created_at': '2024-01-01T00:00:00',
            'expiry_time': expiry_time.strftime('%Y-%m-%dT%H:%M:%S'),
            'features': ['邮件管理', '批量导入', '高级搜索']
        }
        
        dialog = SciFiLicenseDialog(license_data, self)
        dialog.start_application.connect(self._on_start_application)
        dialog.exec()
    
    def _on_start_application(self):
        """开始使用应用程序"""
        print("🚀 用户选择开始使用应用程序！主窗口收到信号。")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    window = TestMainWindow()
    window.show()
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
