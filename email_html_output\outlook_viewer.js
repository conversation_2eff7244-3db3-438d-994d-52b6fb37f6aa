
// Outlook邮件查看器JavaScript

// 下载附件功能
function downloadAttachment(filename, index) {
    alert('附件下载功能: ' + filename);
    console.log('下载附件:', filename, '索引:', index);
}

// 工具栏按钮功能
document.addEventListener('DOMContentLoaded', function() {
    // 回复按钮
    const replyBtn = document.querySelector('.btn-reply');
    if (replyBtn) {
        replyBtn.addEventListener('click', function() {
            alert('回复功能暂未实现');
        });
    }
    
    // 全部回复按钮
    const replyAllBtn = document.querySelector('.btn-reply-all');
    if (replyAllBtn) {
        replyAllBtn.addEventListener('click', function() {
            alert('全部回复功能暂未实现');
        });
    }
    
    // 转发按钮
    const forwardBtn = document.querySelector('.btn-forward');
    if (forwardBtn) {
        forwardBtn.addEventListener('click', function() {
            alert('转发功能暂未实现');
        });
    }
    
    // 标记按钮
    const flagBtn = document.querySelector('.btn-flag');
    if (flagBtn) {
        flagBtn.addEventListener('click', function() {
            alert('标记功能暂未实现');
        });
    }
    
    // 删除按钮
    const deleteBtn = document.querySelector('.btn-delete');
    if (deleteBtn) {
        deleteBtn.addEventListener('click', function() {
            if (confirm('确定要删除这封邮件吗？')) {
                alert('删除功能暂未实现');
            }
        });
    }
    
    // 更多操作按钮
    const moreBtn = document.querySelector('.btn-more');
    if (moreBtn) {
        moreBtn.addEventListener('click', function() {
            alert('更多操作功能暂未实现');
        });
    }
});

// 图片加载错误处理
document.addEventListener('DOMContentLoaded', function() {
    const images = document.querySelectorAll('img');
    images.forEach(function(img) {
        img.addEventListener('error', function() {
            this.style.display = 'none';
        });
    });
});
