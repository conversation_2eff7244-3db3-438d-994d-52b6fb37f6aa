#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
企业级邮件管理系统 - 主应用程序
基于PySide6实现，参考专业邮件客户端界面设计
"""

import sys
import os
import logging
from pathlib import Path
from typing import Optional, Dict, List
from datetime import datetime

from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QSplitter, QTreeWidget, QTreeWidgetItem, QTableWidget, QTableWidgetItem,
    QTextEdit, QToolBar, QStatusBar, QMenu, QProgressBar, QProgressDialog,
    QLabel, QPushButton, QLineEdit, QComboBox, QGroupBox, QTabWidget,
    QMessageBox, QFileDialog, QDialog, QFormLayout, QCheckBox, QSpinBox,
    QHeaderView, QAbstractItemView, QSizePolicy, QInputDialog, QFrame
)
from PySide6.QtGui import QAction
from PySide6.QtCore import (
    Qt, QTimer, QThread, Signal, QSize, QSettings, QRect, QDateTime
)
from PySide6.QtGui import (
    QIcon, QFont, QPixmap, QPalette, QColor, QAction, QKeySequence
)

# 导入WebEngine用于HTML邮件显示
try:
    from PySide6.QtWebEngineWidgets import QWebEngineView
    from PySide6.QtCore import QUrl
    WEBENGINE_AVAILABLE = True
except ImportError:
    WEBENGINE_AVAILABLE = False
    print("WebEngine不可用，将使用文本模式显示HTML邮件")

# 导入现有的核心模块
from core.production_optimized_v2 import ProductionOptimizedClientV2, EmailContent
from core.multi_account_manager import MultiAccountManager, AccountConfig
from core.email_database import EmailDatabase, EmailRecord, FolderRecord, AccountRecord
from utils.email_html_converter import EmailHTMLConverter
from ui.email_compose_dialog import EmailComposeDialog
from utils.email_sender import EmailSender
from core.real_account_manager import RealAccountManager
from ui.real_account_config_dialog import RealAccountConfigDialog

# 导入许可证授权模块
from auth.license_manager import LicenseManager
from auth.license_dialog import LicenseDialog

class EnterpriseEmailManager(QMainWindow):
    """企业级邮件管理系统主窗口"""

    # 定义信号
    email_sync_completed = Signal(str, int)  # account_id, new_email_count

    def __init__(self):
        super().__init__()

        # 应用程序信息
        self.setWindowTitle("微软邮箱批量管理1.0")
        self.setMinimumSize(1200, 800)  # 增大最小尺寸
        self.resize(1400, 900)  # 增大初始尺寸

        # 🎨 加载CSS样式并设置标志
        self.css_loaded = self.load_styles()

        # 核心组件初始化
        self.multi_account_manager = MultiAccountManager()
        self.email_database = EmailDatabase()
        self.html_converter = EmailHTMLConverter()
        self.email_sender = EmailSender()
        self.real_account_manager = RealAccountManager(self.email_database)

        # 许可证管理器初始化
        self.license_manager = LicenseManager()

        # 定期检查许可证状态（每小时检查一次）
        self.license_check_timer = QTimer()
        self.license_check_timer.timeout.connect(self._check_license_status)
        self.license_check_timer.start(3600000)  # 1小时 = 3600000毫秒

        # 设置邮件同步完成回调
        self.real_account_manager.set_sync_callback(self.on_email_sync_completed)

        # 连接信号到槽
        self.email_sync_completed.connect(self.handle_email_sync_completed)

        # UI状态管理
        self.current_account = None
        self.current_folder = None
        self.current_emails = []
        self.selected_email = None

        # 🔴 新邮件红点提醒状态管理
        self.unread_counts = {}  # 账户ID -> 未读邮件数量
        self.account_tree_items = {}  # 账户ID -> QTreeWidgetItem 映射

        # 📬 收件控制相关属性
        self.auto_fetch_enabled = False  # 自动收件是否启用
        self.auto_fetch_interval = 5  # 自动收件间隔（分钟）
        self.auto_fetch_timer = None  # 自动收件定时器
        self.is_fetching = False  # 是否正在收件
        self.last_fetch_time = None  # 上次收件时间

        # 初始化用户界面
        self.init_ui()

        # 设置日志系统
        self.setup_logging()

        # 加载初始数据
        self.load_initial_data()

        # 设置定时器
        self.setup_timers()

    def load_styles(self):
        """加载CSS样式文件，如果文件不存在则使用内联样式"""
        css_file_path = "styles/main.css"

        try:
            # 尝试加载外部CSS文件
            if os.path.exists(css_file_path):
                with open(css_file_path, 'r', encoding='utf-8') as f:
                    css_content = f.read()
                self.setStyleSheet(css_content)
                print(f"✅ 成功加载CSS文件: {css_file_path}")
                return True  # 返回True表示CSS文件加载成功
            else:
                # 文件不存在，使用内联样式
                print("CSS文件不存在，将使用内联样式")
                self.apply_inline_styles()
                return False  # 返回False表示使用内联样式
        except Exception as e:
            print(f"⚠️ 加载CSS文件失败: {e}")
            # 加载失败，使用内联样式
            self.apply_inline_styles()
            return False  # 返回False表示使用内联样式

    def apply_inline_styles(self):
        """应用内联样式作为回退方案"""
        # 设置全局样式
        self.setStyleSheet("""
            QPushButton {
                font-size: 11px;
                padding: 2px 6px;
                border: 1px solid #ccc;
                border-radius: 3px;
                background-color: #f8f9fa;
                min-height: 18px;
            }
            QPushButton:hover {
                background-color: #e9ecef;
                border-color: #adb5bd;
            }
            QPushButton:pressed {
                background-color: #dee2e6;
            }
            QComboBox {
                font-size: 11px;
                padding: 2px 4px;
                border: 1px solid #ccc;
                border-radius: 3px;
                min-height: 18px;
            }
            /* 🔒 全局文本保护样式 */
            QLabel {
                selection-background-color: transparent;
            }
            QTableWidget::item {
                selection-background-color: #0d6efd;
                selection-color: white;
            }
        """)
    


    def init_ui(self):
        """初始化用户界面"""
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建工具栏
        self.create_toolbar()

        # 创建主分割器
        main_splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(main_splitter)
        
        # 创建左侧面板（账户和文件夹树）
        self.create_account_tree_panel(main_splitter)
        
        # 创建中间面板（邮件列表）
        self.create_email_list_panel(main_splitter)
        
        # 创建右侧面板（邮件预览）
        self.create_email_preview_panel(main_splitter)
        
        # 设置分割器比例 - 优化为更紧凑的布局
        main_splitter.setSizes([200, 500, 300])  # 减小各面板宽度
        
        # 创建状态栏
        self.create_status_bar()

        # 菜单栏已移除 - 使用工具栏按钮替代

    # 批量选择面板已移除，功能保留在工具栏按钮中

    def update_selective_sync_panel_info(self):
        """更新批量选择同步按钮状态（增强实时检测）"""
        try:
            self.logger.info("开始更新批量选择同步按钮状态")

            # 检查必要的组件是否存在
            if not hasattr(self, 'real_account_manager'):
                self.logger.warning("real_account_manager 不存在，跳过按钮状态更新")
                return

            if not hasattr(self, 'selective_sync_btn'):
                self.logger.warning("selective_sync_btn 不存在，跳过按钮状态更新")
                return

            # 🔄 实时获取最新的账户状态
            enabled_accounts = self.real_account_manager.get_enabled_accounts()
            count = len(enabled_accounts)

            self.logger.info(f"检测到可用账户数量: {count}")

            # 记录账户详细信息
            if count > 0:
                for i, account in enumerate(enabled_accounts):
                    self.logger.info(f"  账户 {i+1}: {account.email} (ID: {account.account_id})")
            else:
                self.logger.info("  当前没有可用的邮箱账户")

            # 更新按钮状态和文本
            self.selective_sync_btn.setEnabled(count > 0)

            if count == 0:
                self.selective_sync_btn.setText("🚫 暂无可用账户")
                self.selective_sync_btn.setToolTip("没有可用的邮箱账户进行同步\n请先在账户管理中添加并启用账户")
                self.logger.info("批量选择按钮已禁用（无可用账户）")
            else:
                self.selective_sync_btn.setText("🎯 批量选择同步")
                tooltip_text = f"选择特定邮箱账户进行批量同步\n当前可用账户: {count} 个\n\n可用账户列表:\n"
                for account in enabled_accounts[:5]:  # 最多显示5个账户
                    tooltip_text += f"• {account.email}\n"
                if count > 5:
                    tooltip_text += f"• ... 还有 {count - 5} 个账户"

                self.selective_sync_btn.setToolTip(tooltip_text)
                self.logger.info(f"批量选择按钮已启用（{count} 个可用账户）")

        except Exception as e:
            if hasattr(self, 'logger'):
                self.logger.error(f"更新批量选择按钮状态失败: {e}")
                import traceback
                self.logger.error(f"详细错误信息: {traceback.format_exc()}")

    def create_toolbar(self):
        """创建专业工具栏"""
        toolbar = self.addToolBar("主工具栏")
        toolbar.setMovable(False)
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        toolbar.setIconSize(QSize(14, 14))  # 减小图标尺寸

        # 🎨 工具栏样式已在外部CSS文件中定义
        # 如果使用内联样式，则应用工具栏特定样式
        if not self.css_loaded:
            toolbar.setStyleSheet("""
                QToolBar {
                    spacing: 2px;
                    padding: 2px;
                    border: none;
                }
                QPushButton {
                    padding: 2px 6px;
                    margin: 1px;
                    font-size: 11px;
                }
            """)
        
        # 菜单按钮已移除 - 直接使用功能按钮

        # 📥 批量账号导入 - 主要功能按钮（优化响应性）
        self.batch_import_btn = QPushButton("📥 批量账号导入")
        self.batch_import_btn.setToolTip("批量导入微软邮件账户，支持导入后快速操作")
        self.batch_import_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 14px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #1976D2;
                border: 2px solid #0D47A1;
            }
            QPushButton:pressed {
                background-color: #0D47A1;
                border: 2px solid #01579B;
            }
            QPushButton:disabled {
                background-color: #BDBDBD;
                color: #757575;
                border: none;
            }
        """)
        self.batch_import_btn.clicked.connect(self.enhanced_batch_import_with_feedback)
        toolbar.addWidget(self.batch_import_btn)

        # 🎯 批量选择邮箱同步 - 独立突出按钮
        self.selective_sync_btn = QPushButton("🎯 批量选择同步")
        self.selective_sync_btn.setToolTip("选择特定邮箱账户进行批量同步\n支持多账户选择和进度监控")
        self.selective_sync_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF6B35;
                color: white;
                border: none;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 14px;
                border-radius: 5px;
                margin: 2px;
            }
            QPushButton:hover {
                background-color: #E55A2B;
                border: 2px solid #CC4A21;
            }
            QPushButton:pressed {
                background-color: #CC4A21;
                border: 2px solid #B8421C;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        self.selective_sync_btn.clicked.connect(self.show_selective_fetch_dialog)
        toolbar.addWidget(self.selective_sync_btn)

        toolbar.addSeparator()

        # 账户管理按钮（优化尺寸，与立即收件按钮一致）
        self.real_account_btn = QPushButton("🔐 账户管理")
        self.real_account_btn.setToolTip("配置和管理微软邮件账户\n添加、编辑、删除邮箱账户")
        self.real_account_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                font-weight: bold;
                border-radius: 4px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #45a049;
                border: 2px solid #3d8b40;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
                border: 2px solid #2E7D32;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
                border: none;
            }
        """)
        self.real_account_btn.clicked.connect(self.manage_real_accounts_with_feedback)
        toolbar.addWidget(self.real_account_btn)

        toolbar.addSeparator()

        # 📋 日志查看器按钮
        self.log_viewer_btn = QPushButton("📋 查看日志")
        self.log_viewer_btn.setToolTip("打开日志查看器\n查看系统运行日志和历史记录")
        self.log_viewer_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                padding: 8px 16px;
                font-weight: bold;
                border-radius: 4px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #F57C00;
                border: 2px solid #E65100;
            }
            QPushButton:pressed {
                background-color: #E65100;
                border: 2px solid #BF360C;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
                border: none;
            }
        """)
        self.log_viewer_btn.clicked.connect(self.show_log_viewer)
        toolbar.addWidget(self.log_viewer_btn)

        # ⚠️ 注意事项按钮
        self.notice_btn = QPushButton("⚠️ 注意事项")
        self.notice_btn.setToolTip("查看系统使用注意事项\n网络环境、安全建议、常见问题等")
        self.notice_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF5722;
                color: white;
                border: none;
                padding: 8px 16px;
                font-weight: bold;
                border-radius: 4px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #E64A19;
                border: 2px solid #D84315;
            }
            QPushButton:pressed {
                background-color: #D84315;
                border: 2px solid #BF360C;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
                border: none;
            }
        """)
        self.notice_btn.clicked.connect(self.show_notice_panel)
        toolbar.addWidget(self.notice_btn)

        toolbar.addSeparator()

        # 搜索框
        search_label = QLabel("搜索:")
        toolbar.addWidget(search_label)
        
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("搜索邮件...")
        self.search_edit.setMaximumWidth(150)
        self.search_edit.returnPressed.connect(self.search_emails)
        self.search_edit.textChanged.connect(self.on_search_text_changed)
        toolbar.addWidget(self.search_edit)

        # 搜索定时器（用于实时搜索）
        from PySide6.QtCore import QTimer
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self.perform_realtime_search)
        self.search_timer.setInterval(500)  # 500ms延迟
        
        search_btn = QPushButton("搜索")
        search_btn.clicked.connect(self.search_emails)
        toolbar.addWidget(search_btn)

        # 高级搜索按钮
        advanced_search_btn = QPushButton("高级搜索")
        advanced_search_btn.setToolTip("打开高级搜索对话框")
        advanced_search_btn.clicked.connect(self.show_advanced_search)
        toolbar.addWidget(advanced_search_btn)

        toolbar.addSeparator()

        # 邮件撰写按钮
        compose_btn = QPushButton("撰写")
        compose_btn.setToolTip("撰写新邮件")
        compose_btn.clicked.connect(self.compose_new_email)
        toolbar.addWidget(compose_btn)

        # 回复按钮
        reply_btn = QPushButton("回复")
        reply_btn.setToolTip("回复选中的邮件")
        reply_btn.clicked.connect(self.reply_email)
        toolbar.addWidget(reply_btn)

        # 转发按钮
        forward_btn = QPushButton("转发")
        forward_btn.setToolTip("转发选中的邮件")
        forward_btn.clicked.connect(self.forward_email)
        toolbar.addWidget(forward_btn)

        toolbar.addSeparator()

        # 📬 收件控制区域
        # 立即收件按钮（带下拉菜单）
        self.fetch_now_btn = QPushButton("📬 立即收件")
        self.fetch_now_btn.setToolTip("立即同步所有启用账户的邮件")
        self.fetch_now_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                font-weight: bold;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)

        # 🎯 创建收件菜单
        fetch_menu = QMenu(self)

        # 当前账户收件动作
        fetch_current_action = QAction("📧 同步当前邮箱", self)
        fetch_current_action.setToolTip("同步当前选中的邮箱账户")
        fetch_current_action.triggered.connect(self.fetch_current_account)
        fetch_menu.addAction(fetch_current_action)

        fetch_menu.addSeparator()

        # 全部收件动作
        fetch_all_action = QAction("📬 同步所有邮箱", self)
        fetch_all_action.setToolTip("立即同步所有启用账户的邮件")
        fetch_all_action.triggered.connect(self.fetch_emails_now)
        fetch_menu.addAction(fetch_all_action)

        # 批量选择收件动作已移动到独立按钮区域

        # 设置按钮菜单
        self.fetch_now_btn.setMenu(fetch_menu)
        self.fetch_now_btn.clicked.connect(self.fetch_emails_now)  # 默认点击行为
        toolbar.addWidget(self.fetch_now_btn)

        # 自动收件设置按钮
        self.auto_fetch_btn = QPushButton("⏰ 自动收件")
        self.auto_fetch_btn.setToolTip("配置自动收件间隔和设置")
        self.auto_fetch_btn.setCheckable(True)
        self.auto_fetch_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                padding: 8px 16px;
                font-weight: bold;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
            QPushButton:checked {
                background-color: #4CAF50;
            }
            QPushButton:checked:hover {
                background-color: #45a049;
            }
        """)
        self.auto_fetch_btn.clicked.connect(self.toggle_auto_fetch)
        toolbar.addWidget(self.auto_fetch_btn)

        # 收件状态指示器
        self.fetch_status_label = QLabel("📭 待机")
        self.fetch_status_label.setToolTip("当前收件状态")
        self.fetch_status_label.setStyleSheet("""
            QLabel {
                padding: 4px 8px;
                background-color: #f0f0f0;
                border: 1px solid #ddd;
                border-radius: 3px;
                font-size: 11px;
            }
        """)
        toolbar.addWidget(self.fetch_status_label)

        # 弹性空间
        spacer = QWidget()
        spacer.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        toolbar.addWidget(spacer)
        
        # 状态信息
        self.account_count_label = QLabel("账户: 0")
        toolbar.addWidget(self.account_count_label)
        
        self.email_count_label = QLabel("邮件: 0")
        toolbar.addWidget(self.email_count_label)
    
    def setup_logging(self):
        """设置日志系统"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('enterprise_email_manager.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        self.logger.info("企业邮件管理系统启动")
    
    def load_initial_data(self):
        """加载初始数据"""
        try:
            # 加载账户配置
            config_file = "multi_account_config.json"
            if os.path.exists(config_file):
                self.multi_account_manager.load_accounts_from_config(config_file)
            else:
                self.logger.info("配置文件不存在，将创建空的账户管理器")

            # 刷新账户树
            self.refresh_account_tree()

            # 更新批量选择面板信息
            self.update_selective_sync_panel_info()

            # 启动真实账户同步
            if hasattr(self, 'real_account_manager'):
                self.real_account_manager.start_all_syncs()
                self.logger.info("已启动真实账户自动同步")

            # 更新状态
            self.update_status()

        except Exception as e:
            self.logger.error(f"加载初始数据失败: {e}")
            QMessageBox.warning(self, "警告", f"加载初始数据失败: {e}")
    
    def setup_timers(self):
        """设置定时器"""
        # 状态更新定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(5000)  # 每5秒更新一次状态

        # 📬 自动收件定时器
        self.auto_fetch_timer = QTimer()
        self.auto_fetch_timer.timeout.connect(self.auto_fetch_emails)
        self.auto_fetch_timer.setSingleShot(False)  # 重复执行

    def create_account_tree_panel(self, parent_splitter):
        """创建账户和文件夹树面板"""
        # 左侧面板容器
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(1, 1, 1, 1)  # 减小边距
        left_layout.setSpacing(1)  # 减小间距

        # 账户树标题
        tree_header = QLabel("账户和文件夹")
        tree_header.setTextInteractionFlags(Qt.NoTextInteraction)  # 🔒 防止文本选择
        tree_header.setObjectName("panel-title")  # 🎨 设置CSS类名

        # 🎨 如果使用内联样式，则应用标题特定样式
        if not self.css_loaded:
            tree_header.setStyleSheet("""
                QLabel {
                    background-color: #f8f9fa;
                    border: 1px solid #c0c0c0;
                    padding: 2px 4px;
                    font-weight: bold;
                    font-size: 11px;
                    color: #333333;
                }
            """)
        left_layout.addWidget(tree_header)

        # 创建账户树
        self.account_tree = QTreeWidget()
        self.account_tree.setHeaderHidden(True)
        self.account_tree.setRootIsDecorated(True)

        # 🎯 设置账户树缩进为0，使账户名称与分组标题左对齐
        self.account_tree.setIndentation(0)

        # 🔒 设置账户树为只读，防止意外编辑账户名和文件夹名
        self.account_tree.setEditTriggers(QAbstractItemView.NoEditTriggers)

        # 🎨 账户树样式已在外部CSS文件中定义
        # 如果使用内联样式，则应用账户树特定样式
        if not self.css_loaded:
            self.account_tree.setStyleSheet("""
                QTreeWidget {
                    font-size: 12px;
                    border: 1px solid #ccc;
                    background-color: white;
                    outline: none;
                }
                QTreeWidget::item {
                    height: 20px;
                    padding: 1px 4px;
                    color: #212529;
                    border: none;
                    text-align: left;
                    margin-left: 0px;
                }
                QTreeWidget::item:selected {
                    background-color: #0d6efd;
                    color: white;
                }
                QTreeWidget::item:hover:!selected {
                    background-color: #e9ecef;
                    color: #212529;
                }
                QTreeWidget::item:focus {
                    background-color: #0d6efd;
                    color: white;
                    outline: none;
                }
                QTreeWidget::branch {
                    background-color: transparent;
                    width: 0px;
                }
                QTreeWidget::branch:selected {
                    background-color: #0d6efd;
                }
                /* 分组项目样式 */
                QTreeWidget::item[data-type="group"] {
                    font-weight: bold;
                    background-color: #f8f9fa;
                    border-bottom: 1px solid #dee2e6;
                }
                /* 账户项目样式 */
                QTreeWidget::item[data-type="account"] {
                    padding-left: 8px;
                    background-color: #ffffff;
                }
            """)
        self.account_tree.setAlternatingRowColors(True)
        self.account_tree.itemClicked.connect(self.on_tree_item_clicked)
        # 🔒 完全禁用双击事件，防止意外操作
        # self.account_tree.itemDoubleClicked.connect(self.on_tree_item_double_clicked)  # 已禁用

        # 📋 添加右键菜单支持
        self.account_tree.setContextMenuPolicy(Qt.CustomContextMenu)
        self.account_tree.customContextMenuRequested.connect(self.show_account_context_menu)

        left_layout.addWidget(self.account_tree)

        # 添加到分割器
        parent_splitter.addWidget(left_panel)

    def create_email_list_panel(self, parent_splitter):
        """创建邮件列表面板"""
        # 中间面板容器
        middle_panel = QWidget()
        middle_layout = QVBoxLayout(middle_panel)
        middle_layout.setContentsMargins(1, 1, 1, 1)  # 减小边距
        middle_layout.setSpacing(1)  # 减小间距

        # 邮件列表标题和工具栏
        list_header_layout = QHBoxLayout()

        list_title = QLabel("邮件列表")
        list_title.setTextInteractionFlags(Qt.NoTextInteraction)  # 🔒 防止文本选择
        list_title.setObjectName("panel-title")  # 🎨 设置CSS类名

        # 🎨 如果使用内联样式，则应用标题特定样式
        if not self.css_loaded:
            list_title.setStyleSheet("""
                QLabel {
                    background-color: #f8f9fa;
                    border: 1px solid #c0c0c0;
                    padding: 2px 4px;
                    font-weight: bold;
                    font-size: 11px;
                    color: #333333;
                }
            """)
        list_header_layout.addWidget(list_title)

        # 邮件列表工具按钮
        refresh_btn = QPushButton("刷新")
        refresh_btn.setMaximumWidth(50)  # 减小按钮宽度
        refresh_btn.setMaximumHeight(24)  # 减小按钮高度
        refresh_btn.clicked.connect(self.refresh_email_list)
        list_header_layout.addWidget(refresh_btn)

        sort_combo = QComboBox()
        sort_combo.addItems(["按时间", "按发件人", "按主题", "按大小"])
        sort_combo.setMaximumWidth(70)  # 减小下拉框宽度
        sort_combo.setMaximumHeight(24)  # 减小下拉框高度
        sort_combo.currentTextChanged.connect(self.sort_emails)
        list_header_layout.addWidget(sort_combo)

        middle_layout.addLayout(list_header_layout)

        # 邮件操作工具栏
        email_actions_layout = QHBoxLayout()

        # 标记操作
        mark_read_btn = QPushButton("已读")
        mark_read_btn.setMaximumWidth(50)  # 减小宽度并简化文本
        mark_read_btn.setMaximumHeight(22)  # 减小高度
        mark_read_btn.setToolTip("将选中邮件标记为已读")
        mark_read_btn.clicked.connect(self.mark_emails_as_read)
        email_actions_layout.addWidget(mark_read_btn)

        mark_unread_btn = QPushButton("未读")
        mark_unread_btn.setMaximumWidth(50)  # 减小宽度并简化文本
        mark_unread_btn.setMaximumHeight(22)  # 减小高度
        mark_unread_btn.setToolTip("将选中邮件标记为未读")
        mark_unread_btn.clicked.connect(self.mark_emails_as_unread)
        email_actions_layout.addWidget(mark_unread_btn)

        # 删除操作
        delete_btn = QPushButton("删除")
        delete_btn.setMaximumWidth(50)  # 减小宽度
        delete_btn.setMaximumHeight(22)  # 减小高度
        delete_btn.setToolTip("删除选中的邮件")
        delete_btn.clicked.connect(self.delete_emails)
        email_actions_layout.addWidget(delete_btn)

        # 移动操作
        move_btn = QPushButton("移动")
        move_btn.setMaximumWidth(50)  # 减小宽度并简化文本
        move_btn.setMaximumHeight(22)  # 减小高度
        move_btn.setToolTip("移动选中邮件到其他文件夹")
        move_btn.clicked.connect(self.move_emails)
        email_actions_layout.addWidget(move_btn)

        # 附件操作
        attachment_btn = QPushButton("附件")
        attachment_btn.setMaximumWidth(50)  # 减小宽度
        attachment_btn.setMaximumHeight(22)  # 减小高度
        attachment_btn.setToolTip("管理选中邮件的附件")
        attachment_btn.clicked.connect(self.manage_attachments)
        email_actions_layout.addWidget(attachment_btn)

        email_actions_layout.addStretch()

        # 选择操作
        select_all_btn = QPushButton("全选")
        select_all_btn.setMaximumWidth(45)  # 减小宽度
        select_all_btn.setMaximumHeight(22)  # 减小高度
        select_all_btn.setToolTip("选择所有邮件")
        select_all_btn.clicked.connect(self.select_all_emails)
        email_actions_layout.addWidget(select_all_btn)

        select_none_btn = QPushButton("取消")
        select_none_btn.setMaximumWidth(45)  # 减小宽度并简化文本
        select_none_btn.setMaximumHeight(22)  # 减小高度
        select_none_btn.setToolTip("取消所有选择")
        select_none_btn.clicked.connect(self.select_none_emails)
        email_actions_layout.addWidget(select_none_btn)

        middle_layout.addLayout(email_actions_layout)

        # 创建邮件列表表格
        self.email_table = QTableWidget()
        self.email_table.setColumnCount(6)
        self.email_table.setHorizontalHeaderLabels([
            "状态", "主题", "发件人", "时间", "大小", "附件"
        ])

        # 设置表格属性
        self.email_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.email_table.setSelectionMode(QAbstractItemView.ExtendedSelection)  # 支持多选
        self.email_table.setAlternatingRowColors(True)
        self.email_table.setSortingEnabled(True)
        self.email_table.verticalHeader().setVisible(False)
        self.email_table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)

        # 🔒 设置表格为只读，防止意外编辑
        self.email_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.email_table.setFocusPolicy(Qt.NoFocus)  # 防止意外获得焦点

        # 🔒 额外的双击保护设置
        self.email_table.setProperty("doubleClickEnabled", False)
        self.email_table.viewport().setAttribute(Qt.WA_AcceptTouchEvents, False)

        # 🎨 邮件表格样式已在外部CSS文件中定义
        # 如果使用内联样式，则应用表格特定样式
        if not self.css_loaded:
            self.email_table.setStyleSheet("""
                QTableWidget {
                    font-size: 12px;
                    gridline-color: #e0e0e0;
                    border: 1px solid #ccc;
                    background-color: white;
                    alternate-background-color: #f8f9fa;
                }
                QTableWidget::item {
                    padding: 2px 4px;
                    border: none;
                    color: #212529;
                    background-color: transparent;
                }
                QTableWidget::item:selected {
                    background-color: #0d6efd;
                    color: white;
                    border: none;
                    font-weight: normal;
                }
                QTableWidget::item:hover:!selected {
                    background-color: #e9ecef;
                    color: #212529;
                }
                QTableWidget::item:alternate {
                    background-color: #f8f9fa;
                }
                QTableWidget::item:alternate:selected {
                    background-color: #0d6efd;
                    color: white;
                }
                QTableWidget::item:focus {
                    background-color: #0d6efd;
                    color: white;
                    outline: none;
                }
                QHeaderView::section {
                    background-color: #f1f3f4;
                    padding: 2px 4px;
                    border: 1px solid #dee2e6;
                    font-size: 11px;
                    font-weight: bold;
                    color: #495057;
                }
                QHeaderView::section:hover {
                    background-color: #e9ecef;
                }
            """)

        # 设置行高
        self.email_table.verticalHeader().setDefaultSectionSize(22)

        # 设置列宽 - 优化为更紧凑的布局
        header = self.email_table.horizontalHeader()
        header.resizeSection(0, 30)   # 状态 (减小)
        header.resizeSection(1, 220)  # 主题 (减小)
        header.resizeSection(2, 120)  # 发件人 (减小)
        header.resizeSection(3, 100)  # 时间 (减小)
        header.resizeSection(4, 50)   # 大小 (减小)
        header.resizeSection(5, 30)   # 附件 (减小)
        header.setStretchLastSection(True)  # 允许最后一列自动拉伸

        # 连接信号
        self.email_table.itemSelectionChanged.connect(self.on_email_selected)
        # 🔒 完全禁用双击事件，防止意外操作
        # self.email_table.itemDoubleClicked.connect(self.on_email_double_clicked)  # 已禁用
        self.email_table.customContextMenuRequested.connect(self.show_email_context_menu)

        middle_layout.addWidget(self.email_table)

        # 分页控件
        pagination_layout = QHBoxLayout()

        self.page_label = QLabel("第 1 页，共 1 页")
        pagination_layout.addWidget(self.page_label)

        pagination_layout.addStretch()

        self.prev_btn = QPushButton("上一页")
        self.prev_btn.setEnabled(False)
        self.prev_btn.clicked.connect(self.prev_page)
        pagination_layout.addWidget(self.prev_btn)

        self.next_btn = QPushButton("下一页")
        self.next_btn.setEnabled(False)
        self.next_btn.clicked.connect(self.next_page)
        pagination_layout.addWidget(self.next_btn)

        middle_layout.addLayout(pagination_layout)

        # 添加到分割器
        parent_splitter.addWidget(middle_panel)

    def create_email_preview_panel(self, parent_splitter):
        """创建邮件预览面板"""
        # 右侧面板容器
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        right_layout.setContentsMargins(1, 1, 1, 1)  # 减小边距
        right_layout.setSpacing(1)  # 减小间距

        # 预览标题和工具栏
        preview_header_layout = QHBoxLayout()

        preview_title = QLabel("邮件预览")
        preview_title.setTextInteractionFlags(Qt.NoTextInteraction)  # 🔒 防止文本选择
        preview_title.setObjectName("panel-title")  # 🎨 设置CSS类名

        # 🎨 如果使用内联样式，则应用标题特定样式
        if not self.css_loaded:
            preview_title.setStyleSheet("""
                QLabel {
                    background-color: #f8f9fa;
                    border: 1px solid #c0c0c0;
                    padding: 2px 4px;
                    font-weight: bold;
                    font-size: 11px;
                    color: #333333;
                }
            """)
        preview_header_layout.addWidget(preview_title)

        # 预览工具按钮
        html_btn = QPushButton("HTML")
        html_btn.setMaximumWidth(50)
        html_btn.setToolTip("在浏览器中查看HTML格式邮件")
        html_btn.clicked.connect(self.view_email_html)
        preview_header_layout.addWidget(html_btn)

        raw_btn = QPushButton("原始")
        raw_btn.setMaximumWidth(50)
        raw_btn.setToolTip("查看邮件原始内容")
        raw_btn.clicked.connect(self.view_email_raw)
        preview_header_layout.addWidget(raw_btn)

        list_btn = QPushButton("列表")
        list_btn.setMaximumWidth(50)
        list_btn.setToolTip("在浏览器中查看邮件列表")
        list_btn.clicked.connect(self.view_email_list)
        preview_header_layout.addWidget(list_btn)

        right_layout.addLayout(preview_header_layout)

        # 邮件信息区域
        info_group = QGroupBox("邮件信息")
        info_layout = QFormLayout(info_group)

        # 🔒 创建只读邮件信息标签，防止意外选择和编辑
        self.subject_label = QLabel("(未选择邮件)")
        self.subject_label.setTextInteractionFlags(Qt.NoTextInteraction)

        self.sender_label = QLabel("")
        self.sender_label.setTextInteractionFlags(Qt.NoTextInteraction)

        self.date_label = QLabel("")
        self.date_label.setTextInteractionFlags(Qt.NoTextInteraction)

        self.size_label = QLabel("")
        self.size_label.setTextInteractionFlags(Qt.NoTextInteraction)

        self.attachment_label = QLabel("")
        self.attachment_label.setTextInteractionFlags(Qt.NoTextInteraction)

        info_layout.addRow("主题:", self.subject_label)
        info_layout.addRow("发件人:", self.sender_label)
        info_layout.addRow("时间:", self.date_label)
        info_layout.addRow("大小:", self.size_label)
        info_layout.addRow("附件:", self.attachment_label)

        right_layout.addWidget(info_group)

        # 邮件内容预览
        content_group = QGroupBox("邮件内容")
        content_layout = QVBoxLayout(content_group)

        # 内容操作按钮
        content_actions_layout = QHBoxLayout()

        copy_content_btn = QPushButton("复制内容")
        copy_content_btn.setMaximumWidth(80)
        copy_content_btn.setToolTip("复制邮件内容到剪贴板")
        copy_content_btn.clicked.connect(self.copy_email_content)
        content_actions_layout.addWidget(copy_content_btn)

        save_content_btn = QPushButton("保存内容")
        save_content_btn.setMaximumWidth(80)
        save_content_btn.setToolTip("保存邮件内容到文件")
        save_content_btn.clicked.connect(self.save_email_content)
        content_actions_layout.addWidget(save_content_btn)

        # 添加查看模式切换按钮
        self.view_mode_btn = QPushButton("HTML模式")
        self.view_mode_btn.setMaximumWidth(80)
        self.view_mode_btn.setToolTip("切换HTML/文本查看模式")
        self.view_mode_btn.clicked.connect(self.toggle_view_mode)
        content_actions_layout.addWidget(self.view_mode_btn)

        content_actions_layout.addStretch()
        content_layout.addLayout(content_actions_layout)

        # 创建标签页用于不同的查看模式
        self.content_tabs = QTabWidget()
        self.content_tabs.currentChanged.connect(self.on_tab_changed)

        # 文本预览标签页
        self.text_tab = QWidget()
        text_tab_layout = QVBoxLayout(self.text_tab)
        self.preview_text = QTextEdit()
        self.preview_text.setReadOnly(True)
        self.preview_text.setPlainText("请选择邮件查看内容...")
        text_tab_layout.addWidget(self.preview_text)
        self.content_tabs.addTab(self.text_tab, "文本")

        # HTML预览标签页（如果WebEngine可用）
        if WEBENGINE_AVAILABLE:
            self.html_tab = QWidget()
            html_tab_layout = QVBoxLayout(self.html_tab)
            self.html_viewer = QWebEngineView()
            self.html_viewer.setHtml("<html><body><p style='color: #666; text-align: center; margin-top: 50px;'>请选择邮件查看HTML内容...</p></body></html>")
            html_tab_layout.addWidget(self.html_viewer)
            self.content_tabs.addTab(self.html_tab, "HTML")
            # 设置HTML标签页为默认选中
            self.content_tabs.setCurrentIndex(1)  # HTML标签页的索引是1
        else:
            self.html_viewer = None

        content_layout.addWidget(self.content_tabs)
        right_layout.addWidget(content_group)

        # 添加到分割器
        parent_splitter.addWidget(right_panel)

    def create_status_bar(self):
        """创建状态栏"""
        status_bar = self.statusBar()

        # 🎨 状态栏样式已在外部CSS文件中定义
        # 如果使用内联样式，则应用状态栏特定样式
        if not self.css_loaded:
            status_bar.setStyleSheet("""
                QStatusBar {
                    border-top: 1px solid #ccc;
                    padding: 2px;
                    font-size: 11px;
                }
                QLabel {
                    padding: 1px 4px;
                }
            """)

        # 主状态信息
        self.status_label = QLabel("就绪")
        self.status_label.setTextInteractionFlags(Qt.NoTextInteraction)  # 🔒 防止文本选择
        status_bar.addWidget(self.status_label)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(150)  # 减小进度条宽度
        self.progress_bar.setMaximumHeight(16)  # 减小进度条高度
        status_bar.addPermanentWidget(self.progress_bar)

        # 连接状态
        self.connection_label = QLabel("未连接")
        self.connection_label.setTextInteractionFlags(Qt.NoTextInteraction)  # 🔒 防止文本选择
        status_bar.addPermanentWidget(self.connection_label)

        # 内存使用
        self.memory_label = QLabel("内存: 0MB")
        self.memory_label.setTextInteractionFlags(Qt.NoTextInteraction)  # 🔒 防止文本选择
        status_bar.addPermanentWidget(self.memory_label)

    # 菜单栏功能已移除 - 所有功能通过工具栏按钮和右键菜单提供

    # ==================== 事件处理方法 ====================

    def on_tree_item_clicked(self, item, column):
        """账户树项目点击事件"""
        try:
            # 获取项目数据
            item_data = item.data(0, Qt.UserRole)
            if not item_data:
                return

            item_type = item_data.get('type')

            if item_type == 'account':
                self.current_account = item_data.get('account_id')
                self.current_folder = None
                self.current_account_type = 'sim'  # 模拟账户
                self.status_label.setText(f"已选择模拟账户: {item_data.get('email')}")

            elif item_type == 'real_account':
                self.current_account = item_data.get('account_id')
                self.current_folder = None
                self.current_account_type = 'real'  # 微软账户
                self.status_label.setText(f"已选择微软账户: {item_data.get('email')}")

                # 🔴 标记账户为已查看，清除红点
                self.mark_account_as_viewed(self.current_account)

            elif item_type == 'folder':
                self.current_account = item_data.get('account_id')
                self.current_folder = item_data.get('folder_name')
                self.current_account_type = 'sim'  # 模拟账户文件夹
                self.status_label.setText(f"已选择模拟文件夹: {item_data.get('folder_name')}")

                # 加载该文件夹的邮件
                self.load_folder_emails()

            elif item_type == 'real_folder':
                self.current_account = item_data.get('account_id')
                self.current_folder = item_data.get('folder_name')
                self.current_account_type = 'real'  # 微软账户文件夹
                self.status_label.setText(f"已选择微软文件夹: {item_data.get('folder_name')}")

                # 加载该文件夹的邮件
                self.load_real_folder_emails()

            elif item_type == 'group':
                # 分组节点，不做特殊处理
                pass

        except Exception as e:
            self.logger.error(f"处理树项目点击事件失败: {e}")

    def show_account_context_menu(self, position):
        """显示账户树右键菜单"""
        try:
            item = self.account_tree.itemAt(position)
            if not item:
                return

            item_data = item.data(0, Qt.UserRole)
            if not item_data:
                return

            item_type = item_data.get('type')

            # 只为账户项目显示菜单
            if item_type not in ['account', 'real_account']:
                return

            # 创建右键菜单
            context_menu = QMenu(self)

            # 复制邮箱地址动作
            copy_email_action = QAction("📋 复制邮箱地址", self)
            copy_email_action.setToolTip("将邮箱地址复制到剪贴板")
            copy_email_action.triggered.connect(lambda: self.copy_email_address(item_data))
            context_menu.addAction(copy_email_action)

            # 如果是真实账户，添加更多选项
            if item_type == 'real_account':
                context_menu.addSeparator()

                # 同步当前账户
                sync_account_action = QAction("📧 同步此账户", self)
                sync_account_action.setToolTip("立即同步此账户的邮件")
                sync_account_action.triggered.connect(lambda: self.sync_specific_account(item_data.get('account_id')))
                context_menu.addAction(sync_account_action)

                # 账户管理
                manage_account_action = QAction("⚙️ 管理账户", self)
                manage_account_action.setToolTip("打开账户管理界面")
                manage_account_action.triggered.connect(self.manage_real_accounts)
                context_menu.addAction(manage_account_action)

            # 显示菜单
            context_menu.exec(self.account_tree.mapToGlobal(position))

        except Exception as e:
            self.logger.error(f"显示账户右键菜单失败: {e}")

    def copy_email_address(self, item_data):
        """复制邮箱地址到剪贴板"""
        try:
            email = item_data.get('email')
            if not email:
                QMessageBox.warning(self, "错误", "无法获取邮箱地址")
                return

            # 复制到剪贴板
            from PySide6.QtWidgets import QApplication
            clipboard = QApplication.clipboard()
            clipboard.setText(email)

            # 显示成功提示
            self.status_label.setText(f"已复制邮箱地址: {email}")

            # 可选：显示临时提示
            QMessageBox.information(self, "复制成功", f"邮箱地址已复制到剪贴板:\n{email}")

        except Exception as e:
            self.logger.error(f"复制邮箱地址失败: {e}")
            QMessageBox.warning(self, "错误", f"复制邮箱地址失败: {e}")

    def sync_specific_account(self, account_id):
        """同步指定账户"""
        try:
            if not account_id:
                return

            # 设置当前账户
            old_account = self.current_account
            old_type = self.current_account_type

            self.current_account = account_id
            self.current_account_type = 'real'

            # 执行同步
            self.fetch_current_account()

            # 恢复之前的选择（可选）
            # self.current_account = old_account
            # self.current_account_type = old_type

        except Exception as e:
            self.logger.error(f"同步指定账户失败: {e}")

    def on_tree_item_double_clicked(self, item, column):
        """账户树项目双击事件 - 已禁用"""
        # 🔒 双击事件已完全禁用，防止意外操作
        pass

    def on_email_selected(self):
        """邮件选择事件"""
        try:
            current_row = self.email_table.currentRow()
            if current_row < 0 or current_row >= len(self.current_emails):
                return

            # 获取选中的邮件
            email_record = self.current_emails[current_row]
            self.selected_email = email_record

            # 更新预览信息
            self.update_email_info(email_record)

        except Exception as e:
            self.logger.error(f"处理邮件选择事件失败: {e}")

    def on_email_double_clicked(self, item):
        """邮件双击事件 - 已禁用"""
        # 🔒 双击事件已完全禁用，防止意外操作
        pass

    # ==================== 业务逻辑方法 ====================

    def refresh_account_tree(self):
        """刷新账户树"""
        try:
            self.account_tree.clear()

            # 添加微软账号邮箱管理节点
            real_accounts = self.real_account_manager.accounts
            if real_accounts:
                # 创建微软账号邮箱管理分组
                real_group_item = QTreeWidgetItem(self.account_tree)
                real_group_item.setText(0, "🔐 微软账号邮箱管理")
                real_group_item.setTextAlignment(0, Qt.AlignLeft | Qt.AlignVCenter)  # 设置左对齐
                real_group_item.setData(0, Qt.UserRole, {'type': 'group', 'group_name': 'real_accounts'})

                # 🎯 为分组项目设置特殊样式
                real_group_item.setBackground(0, QColor("#f8f9fa"))
                font = real_group_item.font(0)
                font.setBold(True)
                real_group_item.setFont(0, font)

                for account_id, account_config in real_accounts.items():
                    # 创建微软账户节点
                    account_item = QTreeWidgetItem(real_group_item)
                    status_icon = "🟢" if account_config.enabled else "🔴"

                    # 🔴 添加新邮件红点提醒
                    unread_count = self.get_unread_email_count(account_id)
                    if unread_count > 0:
                        account_text = f"{status_icon} {account_config.email} 🔴{unread_count}"
                    else:
                        account_text = f"{status_icon} {account_config.email}"

                    account_item.setText(0, account_text)
                    account_item.setTextAlignment(0, Qt.AlignLeft | Qt.AlignVCenter)  # 设置左对齐
                    account_item.setData(0, Qt.UserRole, {
                        'type': 'real_account',
                        'account_id': account_id,
                        'email': account_config.email
                    })

                    # 🔴 保存账户项目引用用于后续更新红点
                    self.account_tree_items[account_id] = account_item

                    # 添加文件夹节点
                    folders = [
                        ('📥', 'INBOX', '收件箱'),
                        ('📤', 'Sent', '已发送'),
                        ('📝', 'Drafts', '草稿箱'),
                        ('🗑️', 'Deleted', '已删除'),
                        ('⚠️', 'Junk', '垃圾邮件')
                    ]

                    for icon, folder_name, display_name in folders:
                        folder_item = QTreeWidgetItem(account_item)
                        # 获取文件夹邮件数量
                        email_count = self.email_database.get_folder_email_count(account_id, folder_name)
                        folder_text = f"{icon} {display_name}"
                        if email_count > 0:
                            folder_text += f" ({email_count})"
                        folder_item.setText(0, folder_text)
                        folder_item.setTextAlignment(0, Qt.AlignLeft | Qt.AlignVCenter)  # 设置左对齐
                        folder_item.setData(0, Qt.UserRole, {
                            'type': 'real_folder',
                            'account_id': account_id,
                            'folder_name': folder_name,
                            'display_name': display_name
                        })

                    # 展开账户节点
                    account_item.setExpanded(True)

                # 展开真实账户分组
                real_group_item.setExpanded(True)

            # 添加模拟账户节点
            sim_accounts = self.multi_account_manager.accounts
            if sim_accounts:
                # 创建模拟账户分组
                sim_group_item = QTreeWidgetItem(self.account_tree)
                sim_group_item.setText(0, "🧪 模拟账户")
                sim_group_item.setTextAlignment(0, Qt.AlignLeft | Qt.AlignVCenter)  # 设置左对齐
                sim_group_item.setData(0, Qt.UserRole, {'type': 'group', 'group_name': 'sim_accounts'})

                for account_id, account_config in sim_accounts.items():
                    # 创建模拟账户节点
                    account_item = QTreeWidgetItem(sim_group_item)
                    account_item.setText(0, f"📧 {account_config.email}")
                    account_item.setTextAlignment(0, Qt.AlignLeft | Qt.AlignVCenter)  # 设置左对齐
                    account_item.setData(0, Qt.UserRole, {
                        'type': 'account',
                        'account_id': account_id,
                        'email': account_config.email
                    })

                    # 添加文件夹节点
                    folders = [
                        ('📥', 'INBOX', '收件箱'),
                        ('📤', 'Sent', '已发送'),
                        ('📝', 'Drafts', '草稿箱'),
                        ('🗑️', 'Deleted', '已删除'),
                        ('⚠️', 'Junk', '垃圾邮件'),
                        ('📋', 'Notes', '便笺')
                    ]

                    for icon, folder_name, display_name in folders:
                        folder_item = QTreeWidgetItem(account_item)
                        # 获取文件夹邮件数量
                        email_count = self.email_database.get_folder_email_count(account_id, folder_name)
                        folder_text = f"{icon} {display_name}"
                        if email_count > 0:
                            folder_text += f" ({email_count})"
                        folder_item.setText(0, folder_text)
                        folder_item.setTextAlignment(0, Qt.AlignLeft | Qt.AlignVCenter)  # 设置左对齐
                        folder_item.setData(0, Qt.UserRole, {
                            'type': 'folder',
                            'account_id': account_id,
                            'folder_name': folder_name,
                            'display_name': display_name
                        })

                    # 展开账户节点
                    account_item.setExpanded(True)

                # 展开模拟账户分组
                sim_group_item.setExpanded(True)

            self.logger.info("账户树刷新完成")

        except Exception as e:
            self.logger.error(f"刷新账户树失败: {e}")
            QMessageBox.warning(self, "错误", f"刷新账户树失败: {e}")

    def load_folder_emails(self):
        """加载文件夹邮件"""
        try:
            if not self.current_account or not self.current_folder:
                return

            # 从数据库加载邮件
            emails = self.email_database.get_emails_by_folder(
                self.current_account, self.current_folder
            )

            self.current_emails = emails
            self.update_email_table()

            self.status_label.setText(f"已加载 {len(emails)} 封邮件")

        except Exception as e:
            self.logger.error(f"加载文件夹邮件失败: {e}")
            QMessageBox.warning(self, "错误", f"加载文件夹邮件失败: {e}")

    def load_real_folder_emails(self):
        """加载真实账户文件夹邮件"""
        try:
            if not self.current_account or not self.current_folder:
                return

            # 从真实账户管理器获取邮件
            emails = self.real_account_manager.get_account_emails(
                self.current_account, self.current_folder
            )

            self.current_emails = emails
            self.update_email_table()

            self.status_label.setText(f"已加载 {len(emails)} 封微软邮件")

        except Exception as e:
            self.logger.error(f"加载真实账户文件夹邮件失败: {e}")
            QMessageBox.warning(self, "错误", f"加载真实账户文件夹邮件失败: {e}")

    def update_email_table(self):
        """更新邮件表格"""
        try:
            self.email_table.setRowCount(len(self.current_emails))

            for row, email in enumerate(self.current_emails):
                # 状态
                is_read = email.flags and "\\Seen" in email.flags
                if is_read:
                    status_item = QTableWidgetItem("📖")  # 已读
                    status_item.setToolTip("已读")
                    status_item.setForeground(QColor("#666666"))
                else:
                    status_item = QTableWidgetItem("📧")  # 未读
                    status_item.setToolTip("未读")
                    status_item.setForeground(QColor("#0078d4"))
                status_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.email_table.setItem(row, 0, status_item)

                # 主题
                subject_item = QTableWidgetItem(email.subject or "(无主题)")
                self.email_table.setItem(row, 1, subject_item)

                # 发件人
                sender_item = QTableWidgetItem(email.sender or "(未知发件人)")
                self.email_table.setItem(row, 2, sender_item)

                # 时间
                date_str = self.format_date(email.date_sent, "%Y-%m-%d %H:%M")
                date_item = QTableWidgetItem(date_str)
                self.email_table.setItem(row, 3, date_item)

                # 大小
                size_str = self.format_size(email.size) if email.size else "0B"
                size_item = QTableWidgetItem(size_str)
                size_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                self.email_table.setItem(row, 4, size_item)

                # 附件
                if self.has_attachments(email):
                    attachment_item = QTableWidgetItem("📎")
                    attachment_item.setToolTip("包含附件，双击查看")
                    attachment_item.setForeground(QColor("#0078d4"))
                else:
                    attachment_item = QTableWidgetItem("")
                    attachment_item.setToolTip("无附件")
                attachment_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.email_table.setItem(row, 5, attachment_item)

            # 更新分页信息
            self.update_pagination()

        except Exception as e:
            self.logger.error(f"更新邮件表格失败: {e}")

    def update_email_info(self, email_record):
        """更新邮件信息"""
        try:
            # 更新邮件信息
            self.subject_label.setText(email_record.subject or "(无主题)")
            self.sender_label.setText(email_record.sender or "(未知发件人)")

            date_str = self.format_date(email_record.date_sent, "%Y-%m-%d %H:%M:%S")
            self.date_label.setText(date_str)

            size_str = self.format_size(email_record.size) if email_record.size else "0B"
            self.size_label.setText(size_str)

            # 更新附件信息
            if self.has_attachments(email_record):
                attachment_count = self.get_attachment_count(email_record)
                self.attachment_label.setText(f"📎 {attachment_count} 个附件")
                # 🎨 使用CSS类名或内联样式
                if self.css_loaded:
                    self.attachment_label.setObjectName("attachment-label-with-attachments")
                else:
                    self.attachment_label.setStyleSheet("QLabel { color: #0078d4; }")
                self.attachment_label.setToolTip("点击'附件'按钮管理附件")
            else:
                self.attachment_label.setText("无附件")
                # 🎨 使用CSS类名或内联样式
                if self.css_loaded:
                    self.attachment_label.setObjectName("attachment-label-no-attachments")
                else:
                    self.attachment_label.setStyleSheet("QLabel { color: #666666; }")
                self.attachment_label.setToolTip("")

            # 更新邮件内容预览
            self.update_email_preview(email_record)

            # 如果有HTML内容且WebEngine可用，优先显示HTML标签页
            if WEBENGINE_AVAILABLE and email_record.html_body:
                self.content_tabs.setCurrentIndex(1)  # 切换到HTML标签页
                self.update_html_preview()
            elif WEBENGINE_AVAILABLE and self.content_tabs.currentIndex() == 1:
                # 如果当前在HTML标签页但没有HTML内容，更新HTML预览
                self.update_html_preview()

        except Exception as e:
            self.logger.error(f"更新邮件信息失败: {e}")

    def update_email_preview(self, email_record):
        """更新邮件预览内容"""
        try:
            if email_record.text_body:
                # 显示文本内容的前1000个字符
                preview_text = email_record.text_body[:1000]
                if len(email_record.text_body) > 1000:
                    preview_text += "\n\n... (内容已截断，点击'HTML'或'原始'按钮查看完整内容)"
                self.preview_text.setPlainText(preview_text)
            elif email_record.html_body:
                # 如果只有HTML内容，尝试提取纯文本预览
                try:
                    import re
                    # 简单的HTML标签清理
                    clean_text = re.sub(r'<[^>]+>', '', email_record.html_body)
                    clean_text = re.sub(r'\s+', ' ', clean_text).strip()

                    if clean_text:
                        preview_text = clean_text[:1000]
                        if len(clean_text) > 1000:
                            preview_text += "\n\n... (HTML内容已截断，切换到HTML标签页查看完整内容)"
                        self.preview_text.setPlainText(preview_text)
                    else:
                        self.preview_text.setPlainText("此邮件包含HTML内容，请切换到HTML标签页查看完整内容。")
                except Exception as e:
                    self.logger.warning(f"HTML内容预览失败: {e}")
                    self.preview_text.setPlainText("此邮件包含HTML内容，请切换到HTML标签页查看完整内容。")
            else:
                self.preview_text.setPlainText("(无邮件内容)")

        except Exception as e:
            self.logger.error(f"更新邮件预览失败: {e}")
            self.preview_text.setPlainText(f"预览失败: {e}")

    def on_tab_changed(self, index):
        """标签页切换事件"""
        try:
            if index == 1 and WEBENGINE_AVAILABLE:  # 切换到HTML标签页
                self.view_mode_btn.setText("文本模式")
                self.view_mode_btn.setToolTip("切换到文本查看模式")
                # 更新HTML预览
                if self.selected_email:
                    self.update_html_preview()
            else:  # 切换到文本标签页
                self.view_mode_btn.setText("HTML模式")
                self.view_mode_btn.setToolTip("切换到HTML查看模式")

        except Exception as e:
            self.logger.error(f"标签页切换失败: {e}")

    def format_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0B"

        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1

        return f"{size_bytes:.1f}{size_names[i]}"

    def format_date(self, date_value, format_str="%Y-%m-%d %H:%M"):
        """格式化日期时间 - 统一处理字符串和datetime对象（增强时区支持）"""
        if not date_value:
            return "(未知时间)"

        try:
            if isinstance(date_value, str):
                # 尝试解析字符串格式的日期
                from dateutil import parser
                from datetime import timezone
                date_obj = parser.parse(date_value)
                # 确保有时区信息
                if date_obj.tzinfo is None:
                    date_obj = date_obj.replace(tzinfo=timezone.utc)
                return date_obj.strftime(format_str)
            else:
                # 假设是datetime对象
                from datetime import timezone
                if hasattr(date_value, 'tzinfo') and date_value.tzinfo is None:
                    date_value = date_value.replace(tzinfo=timezone.utc)
                return date_value.strftime(format_str)
        except Exception as e:
            self.logger.warning(f"日期格式化失败: {date_value}, 错误: {e}")
            return str(date_value) if date_value else "(未知时间)"

    def update_pagination(self):
        """更新分页信息"""
        # 简单实现，后续可以扩展为真正的分页
        total_emails = len(self.current_emails)
        self.page_label.setText(f"共 {total_emails} 封邮件")

    def update_status(self):
        """更新状态信息"""
        try:
            # 更新账户数量（包括微软账户和模拟账户）
            sim_account_count = len(self.multi_account_manager.accounts)
            real_account_count = len(self.real_account_manager.accounts)
            total_account_count = sim_account_count + real_account_count
            self.account_count_label.setText(f"账户: {total_account_count}")

            # 更新邮件数量（显示数据库中的总邮件数量）
            stats = self.email_database.get_database_statistics()
            total_email_count = stats.get('total_emails', 0)
            current_email_count = len(self.current_emails)
            if self.current_folder and self.current_account:
                self.email_count_label.setText(f"邮件: {current_email_count}/{total_email_count}")
            else:
                self.email_count_label.setText(f"邮件: {total_email_count}")

            # 更新内存使用（简单实现）
            import psutil
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024
            self.memory_label.setText(f"内存: {memory_mb:.1f}MB")

        except Exception as e:
            self.logger.error(f"更新状态失败: {e}")

    # ==================== 功能方法 ====================

    def add_account(self):
        """添加账户"""
        try:
            from ui.account_dialog import AccountDialog
            dialog = AccountDialog(self)
            if dialog.exec() == QDialog.Accepted:
                account_config = dialog.get_account_config()
                self.multi_account_manager.add_account(account_config)
                self.refresh_account_tree()
                self.status_label.setText("账户添加成功")

        except ImportError:
            # 如果没有账户对话框，使用简单输入
            self.show_simple_add_account_dialog()
        except Exception as e:
            self.logger.error(f"添加账户失败: {e}")
            QMessageBox.warning(self, "错误", f"添加账户失败: {e}")

    def show_simple_add_account_dialog(self):
        """显示简单的添加账户对话框"""
        dialog = QDialog(self)
        dialog.setWindowTitle("添加账户")
        dialog.setModal(True)
        dialog.resize(350, 250)  # 减小对话框尺寸

        layout = QFormLayout(dialog)

        email_edit = QLineEdit()
        email_edit.setPlaceholderText("<EMAIL>")
        layout.addRow("邮箱地址:", email_edit)

        client_id_edit = QLineEdit()
        client_id_edit.setText("9e5f94bc-e8a4-4e73-b8be-63364c29d753")
        layout.addRow("Client ID:", client_id_edit)

        refresh_token_edit = QLineEdit()
        refresh_token_edit.setPlaceholderText("输入Refresh Token")
        layout.addRow("Refresh Token:", refresh_token_edit)

        password_edit = QLineEdit()
        password_edit.setEchoMode(QLineEdit.Password)
        layout.addRow("密码:", password_edit)

        # 按钮
        button_layout = QHBoxLayout()
        ok_btn = QPushButton("确定")
        cancel_btn = QPushButton("取消")

        ok_btn.clicked.connect(dialog.accept)
        cancel_btn.clicked.connect(dialog.reject)

        button_layout.addWidget(ok_btn)
        button_layout.addWidget(cancel_btn)
        layout.addRow(button_layout)

        if dialog.exec() == QDialog.Accepted:
            try:
                account_config = AccountConfig(
                    account_id=f"account_{len(self.multi_account_manager.accounts) + 1}",
                    email=email_edit.text(),
                    client_id=client_id_edit.text(),
                    refresh_token=refresh_token_edit.text(),
                    password=password_edit.text(),
                    enabled=True,
                    priority=1,
                    max_retries=3
                )

                self.multi_account_manager.add_account(account_config)
                self.refresh_account_tree()
                self.status_label.setText("账户添加成功")

            except Exception as e:
                QMessageBox.warning(self, "错误", f"添加账户失败: {e}")

    def manage_accounts(self):
        """管理账户"""
        QMessageBox.information(self, "提示", "账户管理功能开发中...")

    def manage_real_accounts_with_feedback(self):
        """优化的账户管理（增强响应性和用户反馈）"""
        try:
            # 立即提供视觉反馈
            self.logger.info("用户点击账户管理按钮")

            # 禁用按钮防止重复点击
            self.real_account_btn.setEnabled(False)
            self.real_account_btn.setText("🔄 启动中...")

            # 使用QTimer延迟执行，确保UI立即响应
            QTimer.singleShot(50, self._execute_account_management)

        except Exception as e:
            self.logger.error(f"账户管理按钮响应失败: {e}")
            self._reset_account_management_button()
            QMessageBox.critical(self, "错误", f"启动账户管理失败：\n{str(e)}")

    def _execute_account_management(self):
        """执行账户管理的实际逻辑"""
        try:
            # 更新按钮状态
            self.real_account_btn.setText("📂 加载中...")

            # 创建进度指示器
            progress = QProgressDialog("正在初始化账户管理功能...", "取消", 0, 0, self)
            progress.setWindowModality(Qt.WindowModal)
            progress.setMinimumDuration(300)  # 300ms后显示进度条
            progress.show()

            # 处理事件以确保进度条显示
            QApplication.processEvents()

            try:
                from ui.account_management_dialog import AccountManagementDialog

                progress.setLabelText("正在创建账户管理对话框...")
                QApplication.processEvents()

                dialog = AccountManagementDialog(self, self.real_account_manager)
                dialog.accounts_changed.connect(self.on_accounts_changed)

                # 关闭进度指示器
                progress.close()

                # 恢复按钮状态
                self._reset_account_management_button()

                # 显示对话框
                self.logger.info("显示账户管理对话框")
                dialog.exec()

            except ImportError as e:
                progress.close()
                self.logger.error(f"导入账户管理模块失败: {e}")

                # 回退到简化的账户配置界面
                self.real_account_btn.setText("🔧 简化模式...")
                QApplication.processEvents()

                QTimer.singleShot(100, self._fallback_account_management)

        except Exception as e:
            self.logger.error(f"执行账户管理失败: {e}")
            self._reset_account_management_button()
            QMessageBox.critical(self, "错误", f"账户管理功能出错：\n{str(e)}")

    def _fallback_account_management(self):
        """回退到简化的账户管理功能"""
        try:
            self._fallback_to_single_account_dialog()
        except Exception as e:
            self.logger.error(f"简化账户管理失败: {e}")
            QMessageBox.critical(self, "错误", f"账户管理功能不可用：\n{str(e)}")
        finally:
            self._reset_account_management_button()

    def _reset_account_management_button(self):
        """重置账户管理按钮状态"""
        try:
            self.real_account_btn.setEnabled(True)
            self.real_account_btn.setText("🔐 账户管理")
            self.logger.info("账户管理按钮状态已重置")
        except Exception as e:
            self.logger.error(f"重置账户管理按钮失败: {e}")

    def manage_real_accounts(self):
        """保留原有方法名以兼容性（重定向到新方法）"""
        self.manage_real_accounts_with_feedback()

    def show_log_viewer(self):
        """显示日志查看器"""
        try:
            from ui.log_viewer_dialog import LogViewerDialog

            self.logger.info("打开日志查看器")

            # 创建日志查看器对话框
            log_viewer = LogViewerDialog(self)
            log_viewer.show()

            self.logger.info("日志查看器已打开")

        except ImportError as e:
            self.logger.error(f"导入日志查看器模块失败: {e}")
            QMessageBox.critical(self, "错误", f"日志查看器功能不可用：\n{str(e)}")
        except Exception as e:
            self.logger.error(f"打开日志查看器失败: {e}")
            QMessageBox.critical(self, "错误", f"打开日志查看器失败：\n{str(e)}")

    def show_notice_panel(self):
        """显示注意事项面板"""
        try:
            from ui.notice_panel_dialog import NoticePanelDialog

            self.logger.info("打开注意事项面板")

            # 创建注意事项对话框
            notice_panel = NoticePanelDialog(self)
            notice_panel.show()

            self.logger.info("注意事项面板已打开")

        except ImportError as e:
            self.logger.error(f"导入注意事项面板模块失败: {e}")
            QMessageBox.critical(self, "错误", f"注意事项面板功能不可用：\n{str(e)}")
        except Exception as e:
            self.logger.error(f"打开注意事项面板失败: {e}")
            QMessageBox.critical(self, "错误", f"打开注意事项面板失败：\n{str(e)}")

    def _fallback_to_single_account_dialog(self):
        """回退到单个账户配置对话框"""
        try:
            from ui.real_account_config_dialog import RealAccountConfigDialog
            dialog = RealAccountConfigDialog(self)
            if dialog.exec() == QDialog.Accepted:
                # 获取配置并添加账户
                config = dialog.get_account_config()
                success = self.real_account_manager.add_account(config)

                if success:
                    QMessageBox.information(self, "成功", "微软账户添加成功！")
                    self.on_accounts_changed()
                else:
                    QMessageBox.warning(self, "失败", "添加微软账户失败，请检查配置。")
        except Exception as fallback_error:
            QMessageBox.critical(self, "错误", f"账户管理功能不可用：\n{str(fallback_error)}")

    def on_accounts_changed(self):
        """账户变更回调"""
        try:
            self.logger.info("开始处理账户变更")

            # 更新批量选择面板信息
            self.update_selective_sync_panel_info()

            # 检查当前选中的账户是否仍然存在
            if self.current_account:
                account_exists = (
                    self.current_account in self.multi_account_manager.accounts or
                    self.current_account in self.real_account_manager.accounts
                )

                if not account_exists:
                    self.logger.info(f"当前账户 {self.current_account} 已被删除，重置选择状态")
                    # 重置当前选择状态
                    self.current_account = None
                    self.current_folder = None
                    self.current_emails = []
                    self.selected_email = None

                    # 清空邮件表格
                    self.email_table.setRowCount(0)
                    self.preview_text.clear()
                    if WEBENGINE_AVAILABLE and hasattr(self, 'html_viewer'):
                        self.html_viewer.setHtml("<html><body><p style='color: #666; text-align: center; margin-top: 50px;'>请选择邮件查看HTML内容...</p></body></html>")

                    # 清空邮件信息显示
                    self.clear_email_info()

                    # 清空账户树选择
                    self.account_tree.clearSelection()

            # 刷新账户树
            self.refresh_account_tree()

            # 刷新邮件列表（如果有选中的账户）
            if self.current_account and self.current_folder:
                self.refresh_email_list()
            else:
                # 没有选中账户时，清空邮件列表
                self.email_table.setRowCount(0)
                self.preview_text.clear()
                if WEBENGINE_AVAILABLE and hasattr(self, 'html_viewer'):
                    self.html_viewer.setHtml("<html><body><p style='color: #666; text-align: center; margin-top: 50px;'>请选择邮件查看HTML内容...</p></body></html>")

            # 更新状态
            self.update_status()

            self.logger.info("账户变更处理完成")

        except Exception as e:
            self.logger.error(f"处理账户变更失败: {e}")
            # 即使出现异常，也要尝试基本的界面更新
            try:
                # 强制刷新账户树
                self.refresh_account_tree()
                # 清空邮件列表以防止显示过期数据
                self.email_table.setRowCount(0)
                self.preview_text.clear()
                if WEBENGINE_AVAILABLE and hasattr(self, 'html_viewer'):
                    self.html_viewer.setHtml("<html><body><p style='color: #666; text-align: center; margin-top: 50px;'>请选择邮件查看HTML内容...</p></body></html>")
                # 更新状态栏
                self.status_label.setText("账户变更处理出现异常，请刷新界面")
            except Exception as inner_e:
                self.logger.error(f"紧急界面更新也失败: {inner_e}")
                # 最后的保险措施
                try:
                    self.status_label.setText("界面更新失败，请重启应用")
                except:
                    pass

    def clear_email_info(self):
        """清空邮件信息显示"""
        try:
            # 清空邮件信息标签
            if hasattr(self, 'subject_label'):
                self.subject_label.setText("(未选择邮件)")
            if hasattr(self, 'sender_label'):
                self.sender_label.setText("")
            if hasattr(self, 'date_label'):
                self.date_label.setText("")
            if hasattr(self, 'size_label'):
                self.size_label.setText("")
            if hasattr(self, 'attachment_label'):
                self.attachment_label.setText("")
        except Exception as e:
            self.logger.error(f"清空邮件信息失败: {e}")

    def add_real_account(self):
        """添加真实账户"""
        self.manage_real_accounts()

    def sync_real_accounts(self):
        """同步所有真实账户"""
        try:
            enabled_accounts = self.real_account_manager.get_enabled_accounts()
            if not enabled_accounts:
                QMessageBox.information(self, "提示", "没有启用的微软账户需要同步。")
                return

            # 显示进度对话框
            progress_dialog = QProgressBar(self)
            progress_dialog.setRange(0, len(enabled_accounts))
            progress_dialog.setValue(0)

            synced_count = 0
            for i, account_config in enumerate(enabled_accounts):
                try:
                    # 使用智能同步账户邮件（增强版错误处理）
                    self.logger.info(f"开始同步账户: {account_config.email}")

                    emails = self.real_account_manager.sync_account_emails_smart(account_config.account_id)
                    synced_count += len(emails) if emails else 0
                    progress_dialog.setValue(i + 1)

                    self.logger.info(f"同步账户 {account_config.email} 成功: {len(emails) if emails else 0} 封邮件")

                except Exception as e:
                    error_msg = str(e)
                    self.logger.error(f"同步账户 {account_config.email} 失败: {error_msg}")

                    # 特殊处理时区错误
                    if "can't subtract offset-naive and offset-aware datetimes" in error_msg:
                        self.logger.error("🚨 检测到时区错误！")
                        self.logger.error(f"账户: {account_config.email}")
                        self.logger.error("详细调用栈:")
                        import traceback
                        for line in traceback.format_exc().split('\n'):
                            if line.strip():
                                self.logger.error(f"  {line}")

                        # 尝试修复并重试
                        self.logger.info("尝试修复时区问题并重试...")
                        try:
                            # 强制刷新账户同步状态
                            from datetime import datetime, timezone
                            sync_state = self.real_account_manager.database.get_account_sync_state(account_config.account_id)
                            if sync_state:
                                # 确保所有时间字段都有时区信息
                                if sync_state.last_full_sync_time and sync_state.last_full_sync_time.tzinfo is None:
                                    sync_state.last_full_sync_time = sync_state.last_full_sync_time.replace(tzinfo=timezone.utc)
                                if sync_state.last_incremental_sync_time and sync_state.last_incremental_sync_time.tzinfo is None:
                                    sync_state.last_incremental_sync_time = sync_state.last_incremental_sync_time.replace(tzinfo=timezone.utc)

                                # 保存修复后的状态
                                self.real_account_manager.database.save_account_sync_state(sync_state)
                                self.logger.info("时区修复完成，重试同步...")

                                # 重试同步
                                emails = self.real_account_manager.sync_account_emails_smart(account_config.account_id)
                                synced_count += len(emails) if emails else 0
                                self.logger.info(f"重试同步成功: {len(emails) if emails else 0} 封邮件")

                        except Exception as retry_error:
                            self.logger.error(f"重试同步失败: {retry_error}")

                    progress_dialog.setValue(i + 1)  # 即使失败也要更新进度

            # 刷新界面
            self.refresh_account_tree()
            self.update_status()

            QMessageBox.information(
                self,
                "同步完成",
                f"同步完成！共同步 {synced_count} 封新邮件。"
            )

        except Exception as e:
            self.logger.error(f"同步真实账户失败: {e}")
            QMessageBox.critical(self, "错误", f"同步真实账户时出错：\n{str(e)}")

    def enhanced_batch_import_with_feedback(self):
        """优化的批量账号导入（增强响应性和用户反馈）"""
        try:
            # 立即提供视觉反馈
            self.logger.info("用户点击批量账号导入按钮")

            # 禁用按钮防止重复点击
            self.batch_import_btn.setEnabled(False)
            self.batch_import_btn.setText("🔄 正在启动...")

            # 使用QTimer延迟执行，确保UI立即响应
            QTimer.singleShot(50, self._execute_batch_import)

        except Exception as e:
            self.logger.error(f"批量导入按钮响应失败: {e}")
            self._reset_batch_import_button()
            QMessageBox.critical(self, "错误", f"启动批量导入失败：\n{str(e)}")

    def _execute_batch_import(self):
        """执行批量导入的实际逻辑"""
        try:
            # 更新按钮状态
            self.batch_import_btn.setText("📂 加载中...")

            # 创建进度指示器
            progress = QProgressDialog("正在初始化批量导入功能...", "取消", 0, 0, self)
            progress.setWindowModality(Qt.WindowModal)
            progress.setMinimumDuration(500)  # 500ms后显示进度条
            progress.show()

            # 处理事件以确保进度条显示
            QApplication.processEvents()

            try:
                from ui.enhanced_batch_import_dialog import EnhancedBatchImportDialog

                progress.setLabelText("正在创建导入对话框...")
                QApplication.processEvents()

                dialog = EnhancedBatchImportDialog(self, self.real_account_manager)
                dialog.import_completed.connect(self.on_enhanced_import_completed)

                # 关闭进度指示器
                progress.close()

                # 恢复按钮状态
                self._reset_batch_import_button()

                # 显示对话框
                self.logger.info("显示批量导入对话框")
                dialog.exec()

            except ImportError as e:
                progress.close()
                self.logger.error(f"导入增强批量导入模块失败: {e}")

                # 回退到原有的批量导入功能
                self.batch_import_btn.setText("📥 使用基础导入...")
                QApplication.processEvents()

                QTimer.singleShot(100, self._fallback_batch_import)

        except Exception as e:
            self.logger.error(f"执行批量导入失败: {e}")
            self._reset_batch_import_button()
            QMessageBox.critical(self, "错误", f"批量导入功能出错：\n{str(e)}")

    def _fallback_batch_import(self):
        """回退到基础批量导入功能"""
        try:
            self.batch_import_real_accounts()
        except Exception as e:
            self.logger.error(f"基础批量导入失败: {e}")
            QMessageBox.critical(self, "错误", f"批量导入功能不可用：\n{str(e)}")
        finally:
            self._reset_batch_import_button()

    def _reset_batch_import_button(self):
        """重置批量导入按钮状态"""
        try:
            self.batch_import_btn.setEnabled(True)
            self.batch_import_btn.setText("📥 批量账号导入")
            self.logger.info("批量导入按钮状态已重置")
        except Exception as e:
            self.logger.error(f"重置批量导入按钮失败: {e}")

    def enhanced_batch_import(self):
        """保留原有方法名以兼容性（重定向到新方法）"""
        self.enhanced_batch_import_with_feedback()

    def on_enhanced_import_completed(self, imported_count: int):
        """增强批量导入完成回调（增强账户状态同步）"""
        try:
            self.logger.info(f"增强批量导入完成 - 导入 {imported_count} 个账户")

            # 刷新账户树
            self.refresh_account_tree()

            # 刷新邮件列表
            self.refresh_email_list()

            # 🔄 立即更新批量选择功能状态
            self.logger.info("更新批量选择功能状态以反映新导入的账户")
            self.update_selective_sync_panel_info()

            # 更新状态栏
            self.status_label.setText(f"批量导入完成 - 成功导入 {imported_count} 个账户")

            # 如果导入了账户，提示用户可以使用批量选择功能
            if imported_count > 0:
                # 检查当前可用账户总数
                total_accounts = len(self.real_account_manager.get_enabled_accounts())
                self.logger.info(f"导入完成后，当前可用账户总数: {total_accounts}")

                if total_accounts > 1:
                    # 显示提示信息
                    # 创建消息文本
                    message_text = (f"成功导入 {imported_count} 个账户！\n\n" +
                                  f"当前共有 {total_accounts} 个可用账户。\n" +
                                  "您现在可以使用\"批量选择同步\"功能来选择特定账户进行同步。")

                    QTimer.singleShot(1000, lambda: QMessageBox.information(
                        self,
                        "导入成功",
                        message_text
                    ))

        except Exception as e:
            self.logger.error(f"处理增强批量导入完成事件失败: {e}")

    def batch_import_real_accounts(self):
        """批量导入真实账户"""
        try:
            from utils.batch_real_account_importer import BatchRealAccountImporter

            dialog = BatchRealAccountImporter(self, self.real_account_manager)
            if dialog.exec() == QDialog.Accepted:
                # 刷新账户树
                self.refresh_account_tree()
                self.update_status()

                # 启动新账户的同步
                self.real_account_manager.start_all_syncs()

                QMessageBox.information(
                    self,
                    "导入成功",
                    "微软账户批量导入成功！\n已自动开始邮件同步。"
                )

        except ImportError:
            QMessageBox.critical(
                self,
                "错误",
                "批量导入功能需要 batch_real_account_importer 模块"
            )
        except Exception as e:
            self.logger.error(f"批量导入真实账户失败: {e}")
            QMessageBox.critical(self, "错误", f"批量导入真实账户时出错：\n{str(e)}")

    def import_accounts(self):
        """批量导入账户"""
        try:
            # 保留原有的批量导入功能
            from utils.batch_account_importer import BatchAccountImporter
            importer = BatchAccountImporter(self, self.multi_account_manager)

            # 等待导入窗口关闭后刷新账户树
            QTimer.singleShot(1000, self.refresh_account_tree)

        except ImportError:
            QMessageBox.information(self, "提示", "批量导入功能需要batch_account_importer模块")
        except Exception as e:
            self.logger.error(f"启动批量导入失败: {e}")
            QMessageBox.warning(self, "错误", f"启动批量导入失败: {e}")

    def fetch_emails(self):
        """获取邮件"""
        try:
            if not self.current_account:
                QMessageBox.warning(self, "提示", "请先选择账户")
                return

            if not self.current_folder:
                QMessageBox.warning(self, "提示", "请先选择文件夹")
                return

            # 显示进度条
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            self.status_label.setText("正在获取邮件...")

            # 启动邮件获取线程
            self.start_fetch_thread()

        except Exception as e:
            self.logger.error(f"获取邮件失败: {e}")
            QMessageBox.warning(self, "错误", f"获取邮件失败: {e}")

    def start_fetch_thread(self):
        """启动邮件获取线程"""
        try:
            # 获取账户配置
            account_config = self.multi_account_manager.accounts.get(self.current_account)
            if not account_config:
                self.progress_bar.setVisible(False)
                QMessageBox.warning(self, "错误", "账户配置不存在")
                return

            self.logger.info(f"开始获取邮件 - 账户: {account_config.email}, 文件夹: {self.current_folder}")

            # 创建邮件客户端 - 使用正确的参数
            client = ProductionOptimizedClientV2(
                email=account_config.email,
                client_id=account_config.client_id,
                refresh_token=account_config.refresh_token
            )

            # 简单的同步获取（后续可以改为异步）
            self.progress_bar.setValue(25)

            # 连接到邮箱
            client.connect()
            self.progress_bar.setValue(50)

            # 选择文件夹
            client.select_folder(self.current_folder)
            self.progress_bar.setValue(75)

            # 获取邮件列表
            email_list = client.get_email_list(limit=50)  # 限制获取50封邮件

            # 保存到数据库
            saved_count = 0
            for email_content in email_list:
                try:
                    # 确保日期格式正确（增强时区支持）
                    date_sent = email_content.header.date
                    if isinstance(date_sent, str):
                        try:
                            from dateutil import parser
                            from datetime import timezone
                            date_sent = parser.parse(date_sent)
                            # 确保有时区信息
                            if date_sent.tzinfo is None:
                                date_sent = date_sent.replace(tzinfo=timezone.utc)
                        except Exception as e:
                            self.logger.warning(f"日期解析失败: {date_sent}, 使用当前时间")
                            date_sent = datetime.now(timezone.utc)
                    elif date_sent is None:
                        date_sent = datetime.now(timezone.utc)
                    else:
                        # 确保datetime对象有时区信息
                        from datetime import timezone
                        if hasattr(date_sent, 'tzinfo') and date_sent.tzinfo is None:
                            date_sent = date_sent.replace(tzinfo=timezone.utc)

                    email_record = EmailRecord(
                        account_id=self.current_account,
                        folder_name=self.current_folder,
                        message_id=email_content.header.message_id or f"generated_{datetime.now(timezone.utc).timestamp()}",
                        subject=email_content.header.subject or "(无主题)",
                        sender=email_content.header.sender or "(未知发件人)",
                        recipients=str(email_content.header.recipients) if email_content.header.recipients else "",
                        date_sent=date_sent,
                        size=email_content.header.size or 0,
                        flags=str(email_content.header.flags) if email_content.header.flags else "",
                        text_body=email_content.text_content or "",
                        html_body=email_content.html_content or "",
                        created_at=datetime.now(timezone.utc)
                    )

                    self.email_database.insert_email(email_record)
                    saved_count += 1

                except Exception as e:
                    self.logger.error(f"保存邮件失败: {e}")
                    continue

            self.progress_bar.setValue(100)

            # 断开连接
            client.disconnect()

            # 刷新邮件列表
            self.load_folder_emails()

            self.progress_bar.setVisible(False)
            self.status_label.setText(f"成功获取 {len(email_list)} 封邮件，保存 {saved_count} 封")
            self.logger.info(f"邮件获取完成 - 获取: {len(email_list)}, 保存: {saved_count}")

        except Exception as e:
            self.progress_bar.setVisible(False)
            error_msg = str(e)
            self.logger.error(f"邮件获取线程失败: {error_msg}")

            # 根据错误类型提供更友好的错误信息
            if "unexpected keyword argument" in error_msg:
                user_msg = "邮件客户端初始化失败，请检查账户配置"
            elif "Authentication failed" in error_msg:
                user_msg = "认证失败，请检查账户凭据是否正确"
            elif "Connection" in error_msg:
                user_msg = "网络连接失败，请检查网络设置"
            else:
                user_msg = f"邮件获取失败: {error_msg}"

            QMessageBox.warning(self, "错误", user_msg)
            self.status_label.setText("邮件获取失败")

    def sync_emails(self):
        """同步邮件"""
        QMessageBox.information(self, "提示", "邮件同步功能开发中...")

    def view_email_html(self):
        """HTML查看邮件"""
        try:
            if not self.selected_email:
                QMessageBox.warning(self, "提示", "请先选择邮件")
                return

            self.logger.info(f"HTML查看邮件: {self.selected_email.subject}")

            # 创建EmailHeader对象
            from production_optimized_v2 import EmailHeader
            email_header = EmailHeader(
                message_id=self.selected_email.message_id,
                subject=self.selected_email.subject,
                sender=self.selected_email.sender,
                recipients=self.selected_email.recipients.split(',') if self.selected_email.recipients else [],
                date=self.selected_email.date_sent,
                uid=hash(self.selected_email.message_id) % 1000000,  # 生成一个简单的UID
                size=self.selected_email.size,
                flags=self.selected_email.flags.split(',') if self.selected_email.flags else []
            )

            # 使用现有的HTML转换器
            email_content = EmailContent(
                header=email_header,
                text_body=self.selected_email.text_body or "",
                html_body=self.selected_email.html_body or "",
                attachments=[]
            )

            # 生成HTML文件名
            safe_subject = "".join(c for c in (self.selected_email.subject or "无主题")[:50] if c.isalnum() or c in (' ', '-', '_')).rstrip()
            filename = f"email_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{safe_subject}.html"

            # 生成HTML并在浏览器中打开
            html_file = self.html_converter.convert_email_to_html(
                email_content,
                filename=filename,
                open_in_browser=True
            )

            self.status_label.setText(f"已在浏览器中打开邮件: {safe_subject}")

        except Exception as e:
            self.logger.error(f"HTML查看失败: {e}")
            QMessageBox.warning(self, "错误", f"HTML查看失败: {e}")

    def view_email_list(self):
        """列表查看邮件"""
        try:
            if not self.current_emails:
                QMessageBox.warning(self, "提示", "当前没有邮件")
                return

            self.logger.info(f"列表查看邮件: {len(self.current_emails)} 封")

            # 转换为EmailContent列表
            email_contents = []
            for email_record in self.current_emails:
                # 创建EmailHeader对象
                from production_optimized_v2 import EmailHeader
                email_header = EmailHeader(
                    message_id=email_record.message_id,
                    subject=email_record.subject,
                    sender=email_record.sender,
                    recipients=email_record.recipients.split(',') if email_record.recipients else [],
                    date=email_record.date_sent,
                    uid=hash(email_record.message_id) % 1000000,  # 生成一个简单的UID
                    size=email_record.size,
                    flags=email_record.flags.split(',') if email_record.flags else []
                )

                email_content = EmailContent(
                    header=email_header,
                    text_body=email_record.text_body or "",
                    html_body=email_record.html_body or "",
                    attachments=[]
                )
                email_contents.append(email_content)

            # 生成HTML列表文件名
            folder_name = self.current_folder or "邮件"
            filename = f"email_list_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{folder_name}.html"

            # 生成HTML列表并在浏览器中打开
            html_file = self.html_converter.convert_emails_to_html_list(
                email_contents,
                list_filename=filename,
                open_in_browser=True
            )

            self.status_label.setText(f"已在浏览器中打开邮件列表: {len(self.current_emails)} 封邮件")

        except Exception as e:
            self.logger.error(f"列表查看失败: {e}")
            QMessageBox.warning(self, "错误", f"列表查看失败: {e}")

    def view_email_raw(self):
        """查看原始邮件"""
        try:
            if not self.selected_email:
                QMessageBox.warning(self, "提示", "请先选择邮件")
                return

            self.logger.info(f"查看原始邮件: {self.selected_email.subject}")

            # 显示原始内容对话框
            dialog = QDialog(self)
            dialog.setWindowTitle(f"原始邮件内容 - {self.selected_email.subject or '(无主题)'}")
            dialog.resize(700, 550)  # 减小对话框尺寸

            # 🎨 对话框样式已在外部CSS文件中定义
            # 如果使用内联样式，则应用对话框特定样式
            if not self.css_loaded:
                dialog.setStyleSheet("""
                    QDialog {
                        background-color: #f0f0f0;
                    }
                    QTextEdit {
                        background-color: #ffffff;
                        border: 1px solid #c0c0c0;
                        font-family: "Consolas", "Courier New", monospace;
                        font-size: 10pt;
                    }
                    QPushButton {
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 #ffffff, stop:1 #f0f0f0);
                        border: 1px solid #c0c0c0;
                        border-radius: 3px;
                        padding: 6px 12px;
                        font-size: 9pt;
                        min-width: 80px;
                    }
                    QPushButton:hover {
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 #e8f4fd, stop:1 #d0e7f8);
                        border-color: #0078d4;
                    }
                """)

            layout = QVBoxLayout(dialog)

            # 添加标签页
            tab_widget = QTabWidget()

            # 邮件头信息标签页
            header_tab = QWidget()
            header_layout = QVBoxLayout(header_tab)

            header_text = QTextEdit()
            header_text.setReadOnly(True)
            header_text.setMaximumHeight(200)

            # 格式化邮件头信息
            header_info = f"""消息ID: {self.selected_email.message_id or '(无)'}
主题: {self.selected_email.subject or '(无主题)'}
发件人: {self.selected_email.sender or '(未知)'}
收件人: {self.selected_email.recipients or '(无)'}
发送时间: {self.format_date(self.selected_email.date_sent, '%Y-%m-%d %H:%M:%S')}
邮件大小: {self.format_size(self.selected_email.size) if self.selected_email.size else '0B'}
标志: {self.selected_email.flags or '(无)'}
创建时间: {self.format_date(self.selected_email.created_at, '%Y-%m-%d %H:%M:%S')}
账户ID: {self.selected_email.account_id}
文件夹: {self.selected_email.folder_name}"""

            header_text.setPlainText(header_info)
            header_layout.addWidget(header_text)
            tab_widget.addTab(header_tab, "邮件头信息")

            # 文本内容标签页
            if self.selected_email.text_body:
                text_tab = QWidget()
                text_layout = QVBoxLayout(text_tab)

                text_content = QTextEdit()
                text_content.setReadOnly(True)
                text_content.setPlainText(self.selected_email.text_body)
                text_layout.addWidget(text_content)
                tab_widget.addTab(text_tab, "文本内容")

            # HTML内容标签页
            if self.selected_email.html_body:
                html_tab = QWidget()
                html_layout = QVBoxLayout(html_tab)

                html_content = QTextEdit()
                html_content.setReadOnly(True)
                html_content.setPlainText(self.selected_email.html_body)
                html_layout.addWidget(html_content)
                tab_widget.addTab(html_tab, "HTML内容")

            # 完整原始内容标签页
            raw_tab = QWidget()
            raw_layout = QVBoxLayout(raw_tab)

            raw_text = QTextEdit()
            raw_text.setReadOnly(True)

            # 显示完整原始内容
            raw_content = f"""=== 邮件头信息 ===
{header_info}

=== 文本内容 ===
{self.selected_email.text_body or '(无文本内容)'}

=== HTML内容 ===
{self.selected_email.html_body or '(无HTML内容)'}"""

            raw_text.setPlainText(raw_content)
            raw_layout.addWidget(raw_text)
            tab_widget.addTab(raw_tab, "完整内容")

            layout.addWidget(tab_widget)

            # 按钮区域
            button_layout = QHBoxLayout()

            # 复制按钮
            copy_btn = QPushButton("复制内容")
            copy_btn.clicked.connect(lambda: self.copy_to_clipboard(raw_content))
            button_layout.addWidget(copy_btn)

            # 保存按钮
            save_btn = QPushButton("保存到文件")
            save_btn.clicked.connect(lambda: self.save_raw_email(raw_content))
            button_layout.addWidget(save_btn)

            button_layout.addStretch()

            # 关闭按钮
            close_btn = QPushButton("关闭")
            close_btn.clicked.connect(dialog.close)
            button_layout.addWidget(close_btn)

            layout.addLayout(button_layout)

            dialog.exec()

        except Exception as e:
            self.logger.error(f"查看原始邮件失败: {e}")
            QMessageBox.warning(self, "错误", f"查看原始邮件失败: {e}")

    def copy_to_clipboard(self, content):
        """复制内容到剪贴板"""
        try:
            from PySide6.QtWidgets import QApplication
            clipboard = QApplication.clipboard()
            clipboard.setText(content)
            self.status_label.setText("内容已复制到剪贴板")
            self.logger.info("内容已复制到剪贴板")
        except Exception as e:
            self.logger.error(f"复制到剪贴板失败: {e}")
            QMessageBox.warning(self, "错误", f"复制失败: {e}")

    def save_raw_email(self, content):
        """保存原始邮件到文件"""
        try:
            if not self.selected_email:
                return

            # 生成文件名
            safe_subject = "".join(c for c in (self.selected_email.subject or "无主题")[:50] if c.isalnum() or c in (' ', '-', '_')).rstrip()
            default_filename = f"raw_email_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{safe_subject}.txt"

            # 打开文件保存对话框
            filename, _ = QFileDialog.getSaveFileName(
                self,
                "保存原始邮件",
                default_filename,
                "文本文件 (*.txt);;所有文件 (*.*)"
            )

            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(content)

                self.status_label.setText(f"邮件已保存到: {filename}")
                self.logger.info(f"原始邮件已保存到: {filename}")
                QMessageBox.information(self, "保存成功", f"邮件已保存到:\n{filename}")

        except Exception as e:
            self.logger.error(f"保存原始邮件失败: {e}")
            QMessageBox.warning(self, "错误", f"保存失败: {e}")

    def copy_email_content(self):
        """复制邮件内容到剪贴板"""
        try:
            if not self.selected_email:
                QMessageBox.warning(self, "提示", "请先选择邮件")
                return

            # 获取邮件内容
            content = self.selected_email.text_body or self.selected_email.html_body or ""
            if not content:
                QMessageBox.warning(self, "提示", "邮件内容为空")
                return

            self.copy_to_clipboard(content)

        except Exception as e:
            self.logger.error(f"复制邮件内容失败: {e}")
            QMessageBox.warning(self, "错误", f"复制失败: {e}")

    def save_email_content(self):
        """保存邮件内容到文件"""
        try:
            if not self.selected_email:
                QMessageBox.warning(self, "提示", "请先选择邮件")
                return

            # 获取邮件内容
            content = self.selected_email.text_body or self.selected_email.html_body or ""
            if not content:
                QMessageBox.warning(self, "提示", "邮件内容为空")
                return

            # 生成文件名
            safe_subject = "".join(c for c in (self.selected_email.subject or "无主题")[:50] if c.isalnum() or c in (' ', '-', '_')).rstrip()

            # 根据内容类型选择扩展名
            if self.selected_email.html_body and not self.selected_email.text_body:
                default_filename = f"email_content_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{safe_subject}.html"
                file_filter = "HTML文件 (*.html);;文本文件 (*.txt);;所有文件 (*.*)"
            else:
                default_filename = f"email_content_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{safe_subject}.txt"
                file_filter = "文本文件 (*.txt);;HTML文件 (*.html);;所有文件 (*.*)"

            # 打开文件保存对话框
            filename, _ = QFileDialog.getSaveFileName(
                self,
                "保存邮件内容",
                default_filename,
                file_filter
            )

            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(content)

                self.status_label.setText(f"邮件内容已保存到: {filename}")
                self.logger.info(f"邮件内容已保存到: {filename}")
                QMessageBox.information(self, "保存成功", f"邮件内容已保存到:\n{filename}")

        except Exception as e:
            self.logger.error(f"保存邮件内容失败: {e}")
            QMessageBox.warning(self, "错误", f"保存失败: {e}")

    def toggle_view_mode(self):
        """切换查看模式"""
        try:
            if not WEBENGINE_AVAILABLE:
                QMessageBox.information(self, "提示", "HTML查看器不可用，请安装WebEngine组件")
                return

            current_index = self.content_tabs.currentIndex()
            if current_index == 0:  # 当前是文本模式，切换到HTML
                self.content_tabs.setCurrentIndex(1)
                self.view_mode_btn.setText("文本模式")
                self.view_mode_btn.setToolTip("切换到文本查看模式")
                # 如果有选中的邮件，更新HTML显示
                if self.selected_email:
                    self.update_html_preview()
            else:  # 当前是HTML模式，切换到文本
                self.content_tabs.setCurrentIndex(0)
                self.view_mode_btn.setText("HTML模式")
                self.view_mode_btn.setToolTip("切换到HTML查看模式")

        except Exception as e:
            self.logger.error(f"切换查看模式失败: {e}")
            QMessageBox.warning(self, "错误", f"切换查看模式失败: {e}")

    def update_html_preview(self):
        """更新HTML预览"""
        try:
            if not WEBENGINE_AVAILABLE or not self.html_viewer or not self.selected_email:
                return

            # 获取邮件的HTML内容
            html_content = self.selected_email.html_body

            if html_content:
                # 创建完整的HTML文档
                full_html = self.create_preview_html(html_content)
                self.html_viewer.setHtml(full_html)
            else:
                # 如果没有HTML内容，显示文本内容
                text_content = self.selected_email.text_body or ""
                if text_content:
                    # 将文本转换为HTML格式
                    html_content = f"""
                    <html>
                    <head>
                        <meta charset="utf-8">
                        <style>
                            body {{
                                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                                font-size: 14px;
                                line-height: 1.6;
                                color: #333;
                                margin: 20px;
                                background-color: #fff;
                            }}
                            .email-content {{
                                white-space: pre-wrap;
                                word-wrap: break-word;
                            }}
                        </style>
                    </head>
                    <body>
                        <div class="email-content">{text_content.replace('<', '&lt;').replace('>', '&gt;')}</div>
                    </body>
                    </html>
                    """
                    self.html_viewer.setHtml(html_content)
                else:
                    self.html_viewer.setHtml("<html><body><p style='color: #666; text-align: center; margin-top: 50px;'>此邮件没有内容</p></body></html>")

        except Exception as e:
            self.logger.error(f"更新HTML预览失败: {e}")
            if self.html_viewer:
                self.html_viewer.setHtml(f"<html><body><p style='color: red;'>HTML预览失败: {e}</p></body></html>")

    def create_preview_html(self, html_content):
        """创建用于预览的HTML内容"""
        try:
            # 如果HTML内容已经是完整的文档，直接使用
            if html_content.strip().lower().startswith('<!doctype') or html_content.strip().lower().startswith('<html'):
                return html_content

            # 否则包装成完整的HTML文档
            full_html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <style>
                    body {{
                        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                        font-size: 14px;
                        line-height: 1.6;
                        color: #333;
                        margin: 20px;
                        background-color: #fff;
                        max-width: 100%;
                        overflow-x: auto;
                    }}
                    img {{
                        max-width: 100%;
                        height: auto;
                    }}
                    table {{
                        border-collapse: collapse;
                        width: 100%;
                        max-width: 100%;
                    }}
                    td, th {{
                        border: 1px solid #ddd;
                        padding: 8px;
                        text-align: left;
                    }}
                    .email-content {{
                        word-wrap: break-word;
                        overflow-wrap: break-word;
                    }}
                </style>
            </head>
            <body>
                <div class="email-content">
                    {html_content}
                </div>
            </body>
            </html>
            """
            return full_html

        except Exception as e:
            self.logger.error(f"创建预览HTML失败: {e}")
            return f"<html><body><p style='color: red;'>HTML处理失败: {e}</p></body></html>"

    def search_emails(self):
        """搜索邮件"""
        search_text = self.search_edit.text().strip()
        if not search_text:
            self.load_folder_emails()  # 重新加载所有邮件
            return

        try:
            # 简单的本地搜索
            filtered_emails = []
            for email in self.current_emails:
                if (search_text.lower() in (email.subject or "").lower() or
                    search_text.lower() in (email.sender or "").lower() or
                    search_text.lower() in (email.text_body or "").lower()):
                    filtered_emails.append(email)

            self.current_emails = filtered_emails
            self.update_email_table()
            self.status_label.setText(f"搜索到 {len(filtered_emails)} 封邮件")

        except Exception as e:
            self.logger.error(f"搜索邮件失败: {e}")
            QMessageBox.warning(self, "错误", f"搜索邮件失败: {e}")

    def show_advanced_search(self):
        """显示高级搜索对话框"""
        try:
            from ui.advanced_search_dialog import AdvancedSearchDialog

            dialog = AdvancedSearchDialog(self)
            dialog.search_requested.connect(self.perform_advanced_search)
            dialog.exec()

        except Exception as e:
            self.logger.error(f"显示高级搜索对话框失败: {e}")
            QMessageBox.warning(self, "错误", f"显示高级搜索对话框失败: {e}")

    def perform_advanced_search(self, criteria):
        """执行高级搜索"""
        try:
            self.logger.info(f"执行高级搜索: {criteria}")

            # 使用数据库搜索而不是内存搜索
            search_results = self.email_database.advanced_search(
                account_id=self.current_account,
                criteria=criteria
            )

            # 更新邮件列表
            self.current_emails = search_results
            self.update_email_table()

            # 更新状态
            search_desc = self.create_search_description(criteria)
            self.status_label.setText(f"高级搜索结果: {len(search_results)} 封邮件 - {search_desc}")

            # 清空预览
            self.selected_email = None
            self.preview_text.setPlainText("请选择邮件查看内容...")
            if WEBENGINE_AVAILABLE and self.html_viewer:
                self.html_viewer.setHtml("<html><body><p style='color: #666; text-align: center; margin-top: 50px;'>请选择邮件查看HTML内容...</p></body></html>")

        except Exception as e:
            self.logger.error(f"执行高级搜索失败: {e}")
            QMessageBox.warning(self, "错误", f"执行高级搜索失败: {e}")

    def create_search_description(self, criteria):
        """创建搜索描述"""
        parts = []

        if 'quick_search' in criteria:
            parts.append(f"关键词: {criteria['quick_search']}")
        if 'subject' in criteria:
            parts.append(f"主题: {criteria['subject']}")
        if 'sender' in criteria:
            parts.append(f"发件人: {criteria['sender']}")
        if 'time_range' in criteria and criteria['time_range'] != "不限制":
            parts.append(f"时间: {criteria['time_range']}")
        if 'read_status' in criteria:
            parts.append(f"状态: {criteria['read_status']}")

        return "; ".join(parts) if parts else "搜索条件"

    def on_search_text_changed(self, text):
        """搜索文本变化事件"""
        try:
            # 重启定时器
            self.search_timer.stop()

            if text.strip():
                # 如果有文本，启动定时器进行延迟搜索
                self.search_timer.start()
            else:
                # 如果文本为空，立即恢复显示所有邮件
                self.load_folder_emails()

        except Exception as e:
            self.logger.error(f"搜索文本变化处理失败: {e}")

    def perform_realtime_search(self):
        """执行实时搜索"""
        try:
            search_text = self.search_edit.text().strip()
            if not search_text:
                return

            # 使用简化的搜索条件进行快速搜索
            criteria = {
                'quick_search': search_text,
                'search_scope': '全部内容'
            }

            # 执行搜索
            search_results = self.email_database.advanced_search(
                account_id=self.current_account,
                criteria=criteria
            )

            # 更新邮件列表
            self.current_emails = search_results
            self.update_email_table()

            # 更新状态（不显示太详细的信息，避免干扰）
            self.status_label.setText(f"搜索: {len(search_results)} 封邮件")

        except Exception as e:
            self.logger.error(f"实时搜索失败: {e}")

    def compose_new_email(self):
        """撰写新邮件"""
        try:
            # 获取所有账户配置（模拟账户 + 微软账户）
            account_configs = {}

            # 添加模拟账户
            if hasattr(self, 'multi_account_manager') and self.multi_account_manager:
                for account_id, config in self.multi_account_manager.accounts.items():
                    account_configs[f"sim_{account_id}"] = {
                        'email': config.email,
                        'display_name': f"模拟 - {config.email}",
                        'type': 'sim',
                        'config': config
                    }

            # 添加微软账户
            if hasattr(self, 'real_account_manager') and self.real_account_manager:
                for account_id, config in self.real_account_manager.accounts.items():
                    if config.enabled:  # 只显示启用的微软账户
                        account_configs[f"real_{account_id}"] = {
                            'email': config.email,
                            'display_name': config.display_name or config.email,
                            'type': 'real',
                            'config': config
                        }

            dialog = EmailComposeDialog(
                parent=self,
                account_configs=account_configs
            )
            dialog.email_sent.connect(self.on_email_sent)
            dialog.exec()

        except Exception as e:
            self.logger.error(f"撰写新邮件失败: {e}")
            QMessageBox.warning(self, "错误", f"撰写新邮件失败: {e}")

    def reply_email(self):
        """回复邮件"""
        try:
            selected_emails = self.get_selected_emails()
            if not selected_emails:
                QMessageBox.warning(self, "提示", "请先选择要回复的邮件")
                return

            if len(selected_emails) > 1:
                QMessageBox.warning(self, "提示", "请只选择一封邮件进行回复")
                return

            email_record = selected_emails[0]

            from ui.email_compose_dialog import EmailComposeDialog

            # 获取账户配置
            account_configs = {}
            if hasattr(self, 'multi_account_manager') and self.multi_account_manager:
                account_configs = self.multi_account_manager.accounts

            dialog = EmailComposeDialog(
                parent=self,
                reply_to_email=email_record,
                account_configs=account_configs
            )
            dialog.email_sent.connect(self.on_email_sent)
            dialog.exec()

        except Exception as e:
            self.logger.error(f"回复邮件失败: {e}")
            QMessageBox.warning(self, "错误", f"回复邮件失败: {e}")

    def reply_all_email(self):
        """全部回复邮件"""
        try:
            # 暂时使用普通回复的逻辑
            # 后续可以扩展为包含所有收件人
            self.reply_email()

        except Exception as e:
            self.logger.error(f"全部回复邮件失败: {e}")
            QMessageBox.warning(self, "错误", f"全部回复邮件失败: {e}")

    def forward_email(self):
        """转发邮件"""
        try:
            selected_emails = self.get_selected_emails()
            if not selected_emails:
                QMessageBox.warning(self, "提示", "请先选择要转发的邮件")
                return

            if len(selected_emails) > 1:
                QMessageBox.warning(self, "提示", "请只选择一封邮件进行转发")
                return

            email_record = selected_emails[0]

            from ui.email_compose_dialog import EmailComposeDialog

            # 获取账户配置
            account_configs = {}
            if hasattr(self, 'multi_account_manager') and self.multi_account_manager:
                account_configs = self.multi_account_manager.accounts

            dialog = EmailComposeDialog(
                parent=self,
                forward_email=email_record,
                account_configs=account_configs
            )
            dialog.email_sent.connect(self.on_email_sent)
            dialog.exec()

        except Exception as e:
            self.logger.error(f"转发邮件失败: {e}")
            QMessageBox.warning(self, "错误", f"转发邮件失败: {e}")

    def on_email_sent(self, email_data):
        """邮件发送完成处理"""
        try:
            self.logger.info(f"邮件发送成功: {email_data['subject']}")

            # 更新状态栏
            self.status_label.setText(f"邮件已发送: {email_data['subject']}")

            # 如果设置了保存到已发送，可以在这里保存到数据库
            if email_data.get('save_sent', True):
                self.save_sent_email(email_data)

        except Exception as e:
            self.logger.error(f"邮件发送完成处理失败: {e}")

    def save_sent_email(self, email_data):
        """保存已发送邮件到数据库"""
        try:
            # 创建邮件记录
            from email_database import EmailRecord

            sent_email = EmailRecord(
                account_id=email_data['from_account'],
                folder_name="Sent",
                message_id=f"sent_{email_data['timestamp'].timestamp()}",
                subject=email_data['subject'],
                sender=email_data.get('from_email', ''),
                recipients=email_data['to'],
                date_sent=email_data['timestamp'],
                size=len(email_data['content'].encode('utf-8')),
                flags="\\Seen",
                text_body=email_data['content'],
                html_body=email_data.get('html_content', ''),
                created_at=email_data['timestamp']
            )

            # 保存到数据库
            if self.email_database.insert_email(sent_email):
                self.logger.info("已发送邮件已保存到数据库")
            else:
                self.logger.warning("保存已发送邮件到数据库失败")

        except Exception as e:
            self.logger.error(f"保存已发送邮件失败: {e}")

    # ==================== 菜单和工具栏事件 ====================

    # 菜单按钮处理函数已移除

    # 查看模式功能已移除

    def refresh_email_list(self):
        """刷新邮件列表"""
        try:
            # 检查当前账户是否有效
            if not self.current_account or not self.current_folder:
                self.logger.info("没有选中有效的账户或文件夹，清空邮件列表")
                self.email_table.setRowCount(0)
                self.preview_text.clear()
                if WEBENGINE_AVAILABLE and hasattr(self, 'html_viewer'):
                    self.html_viewer.setHtml("<html><body><p style='color: #666; text-align: center; margin-top: 50px;'>请选择邮件查看HTML内容...</p></body></html>")
                self.status_label.setText("请选择账户和文件夹")
                return

            # 检查账户是否仍然存在
            account_exists = (
                self.current_account in self.multi_account_manager.accounts or
                self.current_account in self.real_account_manager.accounts
            )

            if not account_exists:
                self.logger.warning(f"当前账户 {self.current_account} 不存在，清空邮件列表")
                self.current_account = None
                self.current_folder = None
                self.current_emails = []
                self.email_table.setRowCount(0)
                self.preview_text.clear()
                if WEBENGINE_AVAILABLE and hasattr(self, 'html_viewer'):
                    self.html_viewer.setHtml("<html><body><p style='color: #666; text-align: center; margin-top: 50px;'>请选择邮件查看HTML内容...</p></body></html>")
                self.status_label.setText("账户已删除，请重新选择")
                return

            # 根据账户类型加载邮件
            if self.current_account in self.real_account_manager.accounts:
                self.load_real_folder_emails()
            else:
                self.load_folder_emails()

        except Exception as e:
            self.logger.error(f"刷新邮件列表失败: {e}")
            self.email_table.setRowCount(0)
            self.preview_text.clear()
            if WEBENGINE_AVAILABLE and hasattr(self, 'html_viewer'):
                self.html_viewer.setHtml("<html><body><p style='color: #666; text-align: center; margin-top: 50px;'>请选择邮件查看HTML内容...</p></body></html>")
            self.status_label.setText(f"刷新失败: {e}")

    def sort_emails(self, sort_type):
        """排序邮件"""
        self.status_label.setText(f"按{sort_type}排序")

    def prev_page(self):
        """上一页"""
        pass  # 分页功能待实现

    def next_page(self):
        """下一页"""
        pass  # 分页功能待实现

    def select_all_emails(self):
        """全选邮件"""
        self.email_table.selectAll()
        self.status_label.setText(f"已选择 {self.email_table.rowCount()} 封邮件")

    def select_none_emails(self):
        """取消选择所有邮件"""
        self.email_table.clearSelection()
        self.status_label.setText("已取消所有选择")

    def show_email_context_menu(self, position):
        """显示邮件右键菜单"""
        try:
            # 检查是否点击在有效的行上
            item = self.email_table.itemAt(position)
            if not item:
                return

            # 获取选中的邮件数量
            selected_rows = set()
            for item in self.email_table.selectedItems():
                selected_rows.add(item.row())

            if not selected_rows:
                return

            # 创建右键菜单
            context_menu = QMenu(self)

            # 查看操作
            view_menu = context_menu.addMenu("📧 查看")
            view_menu.addAction("HTML查看", self.view_email_html)
            view_menu.addAction("原始查看", self.view_email_raw)
            view_menu.addAction("列表查看", self.view_email_list)

            context_menu.addSeparator()

            # 邮件操作
            email_menu = context_menu.addMenu("📤 邮件操作")
            email_menu.addAction("回复", self.reply_email)
            email_menu.addAction("全部回复", self.reply_all_email)
            email_menu.addAction("转发", self.forward_email)

            context_menu.addSeparator()

            # 标记操作
            mark_menu = context_menu.addMenu("🏷️ 标记")
            mark_menu.addAction("标记为已读", self.mark_emails_as_read)
            mark_menu.addAction("标记为未读", self.mark_emails_as_unread)

            context_menu.addSeparator()

            # 操作菜单
            context_menu.addAction("📋 复制内容", self.copy_email_content)
            context_menu.addAction("💾 保存内容", self.save_email_content)
            context_menu.addAction("📎 管理附件", self.manage_attachments)

            context_menu.addSeparator()

            # 移动和删除
            context_menu.addAction("📁 移动到...", self.move_emails)
            context_menu.addAction("🗑️ 删除", self.delete_emails)

            # 显示菜单
            context_menu.exec(self.email_table.mapToGlobal(position))

        except Exception as e:
            self.logger.error(f"显示右键菜单失败: {e}")

    def get_selected_emails(self):
        """获取选中的邮件"""
        try:
            selected_rows = set()
            for item in self.email_table.selectedItems():
                selected_rows.add(item.row())

            selected_emails = []
            for row in selected_rows:
                if row < len(self.current_emails):
                    selected_emails.append(self.current_emails[row])

            return selected_emails

        except Exception as e:
            self.logger.error(f"获取选中邮件失败: {e}")
            return []

    def mark_emails_as_read(self):
        """标记邮件为已读"""
        try:
            selected_emails = self.get_selected_emails()
            if not selected_emails:
                QMessageBox.warning(self, "提示", "请先选择邮件")
                return

            # 更新数据库中的标记状态
            updated_count = 0
            for email in selected_emails:
                # 更新flags字段，添加\\Seen标记
                current_flags = email.flags.split(',') if email.flags else []
                if '\\Seen' not in current_flags:
                    current_flags.append('\\Seen')
                    new_flags = ','.join(current_flags)

                    # 更新数据库
                    if self.email_database.update_email_flags(email.id, new_flags):
                        email.flags = new_flags
                        updated_count += 1

            # 刷新邮件列表显示
            self.update_email_table()
            self.status_label.setText(f"已将 {updated_count} 封邮件标记为已读")

        except Exception as e:
            self.logger.error(f"标记邮件为已读失败: {e}")
            QMessageBox.warning(self, "错误", f"标记邮件为已读失败: {e}")

    def mark_emails_as_unread(self):
        """标记邮件为未读"""
        try:
            selected_emails = self.get_selected_emails()
            if not selected_emails:
                QMessageBox.warning(self, "提示", "请先选择邮件")
                return

            # 更新数据库中的标记状态
            updated_count = 0
            for email in selected_emails:
                # 更新flags字段，移除\\Seen标记
                current_flags = email.flags.split(',') if email.flags else []
                if '\\Seen' in current_flags:
                    current_flags.remove('\\Seen')
                    new_flags = ','.join(current_flags)

                    # 更新数据库
                    if self.email_database.update_email_flags(email.id, new_flags):
                        email.flags = new_flags
                        updated_count += 1

            # 刷新邮件列表显示
            self.update_email_table()
            self.status_label.setText(f"已将 {updated_count} 封邮件标记为未读")

        except Exception as e:
            self.logger.error(f"标记邮件为未读失败: {e}")
            QMessageBox.warning(self, "错误", f"标记邮件为未读失败: {e}")

    def delete_emails(self):
        """删除邮件"""
        try:
            selected_emails = self.get_selected_emails()
            if not selected_emails:
                QMessageBox.warning(self, "提示", "请先选择邮件")
                return

            # 确认删除
            reply = QMessageBox.question(
                self,
                "确认删除",
                f"确定要删除选中的 {len(selected_emails)} 封邮件吗？\n\n此操作不可撤销！",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply != QMessageBox.StandardButton.Yes:
                return

            # 执行删除操作
            deleted_count = 0
            for email in selected_emails:
                # 软删除：标记为已删除而不是物理删除
                if self.email_database.soft_delete_email(email.id):
                    deleted_count += 1

            # 从当前邮件列表中移除已删除的邮件
            self.current_emails = [email for email in self.current_emails if email not in selected_emails]

            # 刷新邮件列表显示
            self.update_email_table()
            self.status_label.setText(f"已删除 {deleted_count} 封邮件")

            # 清空预览
            if self.selected_email in selected_emails:
                self.selected_email = None
                self.preview_text.setPlainText("请选择邮件查看内容...")
                if WEBENGINE_AVAILABLE and self.html_viewer:
                    self.html_viewer.setHtml("<html><body><p style='color: #666; text-align: center; margin-top: 50px;'>请选择邮件查看HTML内容...</p></body></html>")

        except Exception as e:
            self.logger.error(f"删除邮件失败: {e}")
            QMessageBox.warning(self, "错误", f"删除邮件失败: {e}")

    def move_emails(self):
        """移动邮件到其他文件夹"""
        try:
            selected_emails = self.get_selected_emails()
            if not selected_emails:
                QMessageBox.warning(self, "提示", "请先选择邮件")
                return

            if not self.current_account:
                QMessageBox.warning(self, "提示", "请先选择账户")
                return

            # 获取当前账户的所有文件夹
            folders = self.get_account_folders(self.current_account)
            if not folders:
                QMessageBox.warning(self, "提示", "无法获取文件夹列表")
                return

            # 移除当前文件夹
            if self.current_folder in folders:
                folders.remove(self.current_folder)

            if not folders:
                QMessageBox.information(self, "提示", "没有其他文件夹可以移动")
                return

            # 显示文件夹选择对话框
            folder, ok = QInputDialog.getItem(
                self,
                "选择目标文件夹",
                f"将 {len(selected_emails)} 封邮件移动到:",
                folders,
                0,
                False
            )

            if not ok or not folder:
                return

            # 执行移动操作
            moved_count = 0
            for email in selected_emails:
                # 更新邮件的文件夹字段
                if self.email_database.update_email_folder(email.id, folder):
                    email.folder_name = folder
                    moved_count += 1

            # 从当前邮件列表中移除已移动的邮件
            self.current_emails = [email for email in self.current_emails if email not in selected_emails]

            # 刷新邮件列表显示
            self.update_email_table()
            self.status_label.setText(f"已将 {moved_count} 封邮件移动到 {folder}")

            # 清空预览
            if self.selected_email in selected_emails:
                self.selected_email = None
                self.preview_text.setPlainText("请选择邮件查看内容...")
                if WEBENGINE_AVAILABLE and self.html_viewer:
                    self.html_viewer.setHtml("<html><body><p style='color: #666; text-align: center; margin-top: 50px;'>请选择邮件查看HTML内容...</p></body></html>")

        except Exception as e:
            self.logger.error(f"移动邮件失败: {e}")
            QMessageBox.warning(self, "错误", f"移动邮件失败: {e}")

    def get_account_folders(self, account_id):
        """获取账户的文件夹列表"""
        try:
            # 从数据库获取该账户的所有文件夹
            folders = self.email_database.get_folders_by_account(account_id)
            return folders
        except Exception as e:
            self.logger.error(f"获取账户文件夹失败: {e}")
            return []

    def manage_attachments(self):
        """管理附件"""
        try:
            selected_emails = self.get_selected_emails()
            if not selected_emails:
                QMessageBox.warning(self, "提示", "请先选择邮件")
                return

            if len(selected_emails) > 1:
                QMessageBox.warning(self, "提示", "请只选择一封邮件来管理附件")
                return

            email_record = selected_emails[0]

            # 检查邮件是否有附件
            if not self.has_attachments(email_record):
                QMessageBox.information(self, "提示", "此邮件没有附件")
                return

            # 打开附件管理对话框
            from ui.attachment_manager_dialog import AttachmentManagerDialog
            dialog = AttachmentManagerDialog(email_record, self)
            dialog.exec()

        except Exception as e:
            self.logger.error(f"管理附件失败: {e}")
            QMessageBox.warning(self, "错误", f"管理附件失败: {e}")

    def has_attachments(self, email_record):
        """检查邮件是否有附件"""
        try:
            # 检查邮件大小，大于10KB可能有附件
            if email_record.size and email_record.size > 10240:
                return True

            # 检查邮件内容中是否有附件标识
            if email_record.html_body and 'attachment' in email_record.html_body.lower():
                return True

            # 检查是否有附件字段（如果数据库中有的话）
            if hasattr(email_record, 'attachments') and email_record.attachments:
                return email_record.attachments != "[]"

            return False
        except Exception as e:
            self.logger.error(f"检查附件失败: {e}")
            return False

    def get_attachment_count(self, email_record):
        """获取附件数量"""
        try:
            # 这里应该解析邮件中的实际附件数量
            # 暂时返回模拟数量
            if self.has_attachments(email_record):
                # 根据邮件大小估算附件数量
                if email_record.size and email_record.size > 1024000:  # 1MB
                    return 3
                elif email_record.size and email_record.size > 512000:  # 512KB
                    return 2
                else:
                    return 1
            return 0
        except Exception as e:
            self.logger.error(f"获取附件数量失败: {e}")
            return 0

    def refresh_all(self):
        """刷新所有"""
        self.refresh_account_tree()
        self.load_folder_emails()
        self.status_label.setText("刷新完成")

    def on_email_sync_completed(self, account_id: str, new_email_count: int):
        """邮件同步完成回调（在工作线程中调用）"""
        try:
            self.logger.info(f"收到同步完成通知: {account_id}, 新邮件: {new_email_count}")

            # 发射信号到主线程
            self.email_sync_completed.emit(account_id, new_email_count)

        except Exception as e:
            self.logger.error(f"处理同步完成回调失败: {e}")

    def handle_email_sync_completed(self, account_id: str, new_email_count: int):
        """处理邮件同步完成信号（在主线程中执行）"""
        try:
            self.logger.info(f"在主线程中处理同步完成: {account_id}, 新邮件: {new_email_count}")

            # 🔴 如果有新邮件，更新红点显示
            if new_email_count > 0:
                unread_count = self.get_unread_email_count(account_id)
                self.update_account_red_dot(account_id, unread_count)

            # 刷新UI
            self.refresh_account_tree()
            self.update_status()

            # 如果当前选中的是刚同步的账户，刷新邮件列表
            if self.current_account == account_id:
                self.load_real_folder_emails()

        except Exception as e:
            self.logger.error(f"处理同步完成信号失败: {e}")

    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(self, "关于",
            "企业邮件管理系统 v2.0\n\n"
            "基于PySide6开发的专业邮件管理工具\n"
            "支持多账户批量邮件获取和管理\n\n"
            "© 2024 企业邮件管理系统")

    # ========================================
    # 🔴 新邮件红点提醒功能
    # ========================================

    def get_unread_email_count(self, account_id: str) -> int:
        """获取账户的未读邮件数量"""
        try:
            # 从数据库获取未读邮件数量（这里简化为获取最近7天的邮件数量）
            from datetime import datetime, timedelta, timezone

            # 获取最近同步时间
            sync_state = self.real_account_manager.database.get_account_sync_state(account_id)
            if not sync_state or not sync_state.last_incremental_sync_time:
                return 0

            # 计算自上次查看以来的新邮件数量
            last_viewed_time = getattr(self, f'last_viewed_{account_id}', sync_state.last_incremental_sync_time)

            # 获取该时间之后的邮件数量
            emails = self.email_database.get_emails_by_account(account_id, "INBOX", limit=1000)
            unread_count = 0

            for email in emails:
                if hasattr(email, 'created_at') and email.created_at:
                    email_time = email.created_at
                    if isinstance(email_time, str):
                        from dateutil import parser
                        email_time = parser.parse(email_time)

                    if email_time.tzinfo is None:
                        email_time = email_time.replace(tzinfo=timezone.utc)

                    if last_viewed_time.tzinfo is None:
                        last_viewed_time = last_viewed_time.replace(tzinfo=timezone.utc)

                    if email_time > last_viewed_time:
                        unread_count += 1

            return unread_count

        except Exception as e:
            self.logger.error(f"获取未读邮件数量失败: {e}")
            return 0

    def mark_account_as_viewed(self, account_id: str):
        """标记账户为已查看，清除红点"""
        try:
            from datetime import datetime, timezone
            setattr(self, f'last_viewed_{account_id}', datetime.now(timezone.utc))

            # 更新红点显示
            self.update_account_red_dot(account_id, 0)

        except Exception as e:
            self.logger.error(f"标记账户已查看失败: {e}")

    def update_account_red_dot(self, account_id: str, unread_count: int):
        """更新账户的红点显示"""
        try:
            if account_id not in self.account_tree_items:
                return

            account_item = self.account_tree_items[account_id]
            if not account_item:
                return

            # 获取账户配置
            account_config = self.real_account_manager.accounts.get(account_id)
            if not account_config:
                return

            # 更新显示文本
            status_icon = "🟢" if account_config.enabled else "🔴"
            if unread_count > 0:
                account_text = f"{status_icon} {account_config.email} 🔴{unread_count}"
            else:
                account_text = f"{status_icon} {account_config.email}"

            account_item.setText(0, account_text)

        except Exception as e:
            self.logger.error(f"更新账户红点失败: {e}")

    # ========================================
    # 🎯 批量选择邮箱收件功能
    # ========================================

    def show_selective_fetch_dialog(self):
        """显示批量选择邮箱收件对话框（增强实时账户检测）"""
        try:
            # 🔄 实时更新账户状态和按钮状态
            self.logger.info("准备显示批量选择对话框，先更新账户状态")
            self.update_selective_sync_panel_info()

            # 🔍 实时检测可用账户
            enabled_accounts = self.real_account_manager.get_enabled_accounts()
            account_count = len(enabled_accounts)

            self.logger.info(f"实时检测到可用账户数量: {account_count}")

            # 检查是否有可用账户
            if account_count == 0:
                self.logger.warning("没有可用的邮箱账户进行批量同步")
                QMessageBox.information(
                    self,
                    "提示",
                    "当前没有可用的邮箱账户进行批量同步。\n\n请先在账户管理中添加并启用邮箱账户。"
                )
                return

            from ui.selective_fetch_dialog import SelectiveFetchDialog

            self.logger.info(f"显示批量选择邮箱收件对话框，可用账户: {account_count} 个")

            # 🔄 创建对话框时传递最新的账户管理器状态
            dialog = SelectiveFetchDialog(self.real_account_manager, self)

            # 🔄 强制刷新对话框中的账户数据
            dialog.refresh_accounts_data()

            result = dialog.exec()
            self.logger.info(f"对话框结果: {result}, QDialog.Accepted: {QDialog.Accepted}")

            if result == QDialog.Accepted:
                selected_accounts = dialog.get_selected_accounts()
                self.logger.info(f"获取到选中的账户: {selected_accounts}")

                if selected_accounts:
                    self.logger.info(f"开始启动选择性收件，账户数量: {len(selected_accounts)}")
                    self.start_selective_fetch(selected_accounts)
                else:
                    self.logger.warning("对话框已接受但没有选中的账户")
                    QMessageBox.information(self, "提示", "没有选择任何账户进行同步。")
            else:
                self.logger.info("用户取消了批量选择对话框")

        except ImportError as e:
            self.logger.error(f"导入批量选择对话框模块失败: {e}")
            QMessageBox.critical(self, "错误", f"批量选择功能不可用：\n{str(e)}")
        except Exception as e:
            self.logger.error(f"显示批量选择对话框失败: {e}")
            QMessageBox.critical(self, "错误", f"显示批量选择对话框失败：\n{str(e)}")

        except ImportError as e:
            self.logger.error(f"导入批量选择对话框失败: {e}")
            QMessageBox.warning(self, "错误", "批量选择功能暂不可用，请使用立即收件功能。")
        except Exception as e:
            self.logger.error(f"显示批量选择对话框失败: {e}")
            QMessageBox.warning(self, "错误", f"显示批量选择对话框失败: {e}")

    def start_selective_fetch(self, selected_account_ids: List[str]):
        """开始选择性收件"""
        try:
            self.logger.info(f"开始选择性收件，接收到的账户ID列表: {selected_account_ids}")

            if self.is_fetching:
                self.logger.warning("当前正在收件中，无法开始新的收件")
                QMessageBox.information(self, "提示", "正在收件中，请稍候...")
                return

            if not selected_account_ids:
                self.logger.warning("没有选择任何账户")
                QMessageBox.information(self, "提示", "没有选择任何账户。")
                return

            self.logger.info(f"准备启动选择性收件，账户数量: {len(selected_account_ids)}")
            # 开始收件
            self.start_manual_fetch_selective(selected_account_ids)

        except Exception as e:
            self.logger.error(f"选择性收件失败: {e}")
            QMessageBox.warning(self, "错误", f"选择性收件失败: {e}")
            self.finish_fetch()

    # ========================================
    # 📬 收件控制功能实现
    # ========================================

    def fetch_emails_now(self):
        """立即收件功能（增强版）"""
        try:
            if self.is_fetching:
                QMessageBox.information(self, "提示", "正在收件中，请稍候...")
                return

            # 检查是否有启用的账户
            enabled_accounts = self.real_account_manager.get_enabled_accounts()
            if not enabled_accounts:
                QMessageBox.information(self, "提示", "没有启用的邮件账户需要同步。\n请先在账户管理中添加并启用账户。")
                return

            # 显示确认对话框
            reply = QMessageBox.question(self, "确认收件",
                f"即将同步 {len(enabled_accounts)} 个启用账户的邮件。\n\n"
                "这可能需要几分钟时间，是否继续？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.Yes)

            if reply != QMessageBox.StandardButton.Yes:
                return

            # 开始收件
            self.start_manual_fetch()

        except Exception as e:
            self.logger.error(f"立即收件失败: {e}")
            QMessageBox.warning(self, "错误", f"立即收件失败: {e}")
            # 确保状态重置
            self.finish_fetch()

    def fetch_current_account(self):
        """同步当前选中的账户"""
        try:
            if self.is_fetching:
                QMessageBox.information(self, "提示", "正在收件中，请稍候...")
                return

            # 检查是否选中了真实账户
            if not self.current_account or self.current_account_type != 'real':
                QMessageBox.information(self, "提示",
                    "请先在账户树中选择一个微软邮箱账户。\n"
                    "只有微软邮箱账户支持邮件同步功能。")
                return

            # 检查账户是否存在且启用
            account_config = self.real_account_manager.accounts.get(self.current_account)
            if not account_config:
                QMessageBox.warning(self, "错误", "选中的账户不存在，请刷新账户列表。")
                return

            if not account_config.enabled:
                QMessageBox.information(self, "提示",
                    f"账户 {account_config.email} 已禁用。\n"
                    "请在账户管理中启用该账户后再进行同步。")
                return

            # 显示确认对话框
            reply = QMessageBox.question(self, "确认收件",
                f"即将同步账户: {account_config.email}\n\n"
                "这可能需要几分钟时间，是否继续？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.Yes)

            if reply != QMessageBox.StandardButton.Yes:
                return

            # 开始单账户收件
            self.start_single_account_fetch(self.current_account)

        except Exception as e:
            self.logger.error(f"同步当前账户失败: {e}")
            QMessageBox.warning(self, "错误", f"同步当前账户失败: {e}")
            self.finish_fetch()

    def start_single_account_fetch(self, account_id: str):
        """开始单账户收件"""
        try:
            self.is_fetching = True
            self.fetch_now_btn.setEnabled(False)

            # 获取账户信息
            account_config = self.real_account_manager.accounts.get(account_id)
            if not account_config:
                raise Exception("账户配置不存在")

            self.fetch_status_label.setText(f"📥 收件中: {account_config.email}")
            self.fetch_status_label.setStyleSheet("""
                QLabel {
                    padding: 4px 8px;
                    background-color: #2196F3;
                    color: white;
                    border: 1px solid #1976D2;
                    border-radius: 3px;
                    font-size: 11px;
                    font-weight: bold;
                }
            """)

            # 记录开始时间
            from datetime import datetime, timezone
            start_time = datetime.now(timezone.utc)
            self.last_fetch_time = start_time

            # 创建进度对话框
            self.fetch_progress_dialog = QProgressDialog(f"正在同步 {account_config.email}...", "取消", 0, 100, self)
            self.fetch_progress_dialog.setWindowTitle("单账户邮件同步")
            self.fetch_progress_dialog.setModal(True)
            self.fetch_progress_dialog.setValue(0)
            self.fetch_progress_dialog.show()

            # 启动单账户收件线程
            class SingleAccountFetchThread(QThread):
                progress_updated = Signal(int, str, str)  # 进度, 状态消息, 同步类型
                fetch_completed = Signal(int, int, dict)  # 成功数量, 总数量, 统计信息

                def __init__(self, account_manager, logger, account_id):
                    super().__init__()
                    self.account_manager = account_manager
                    self.logger = logger
                    self.account_id = account_id
                    self.should_stop = False

                def run(self):
                    try:
                        # 获取账户配置
                        account_config = self.account_manager.accounts.get(self.account_id)
                        if not account_config:
                            self.fetch_completed.emit(0, 1, {})
                            return

                        # 获取同步状态以确定同步类型
                        sync_state = self.account_manager.database.get_account_sync_state(self.account_id)
                        sync_type = "增量同步"
                        if sync_state and sync_state.is_first_sync:
                            sync_type = "首次同步"

                        # 更新进度
                        self.progress_updated.emit(25, f"开始{sync_type}: {account_config.email}", sync_type)

                        # 执行同步
                        synced_emails = self.account_manager.sync_account_emails_smart(
                            self.account_id, "INBOX"
                        )

                        # 统计结果
                        sync_stats = {
                            'first_sync': 1 if sync_type == "首次同步" else 0,
                            'incremental': 1 if sync_type == "增量同步" else 0,
                            'full_sync': 0,
                            'total_emails': len(synced_emails)
                        }

                        # 完成进度
                        self.progress_updated.emit(100, f"{sync_type}完成", sync_type)
                        self.fetch_completed.emit(1, 1, sync_stats)

                        self.logger.info(f"单账户同步完成: {account_config.email}, 新邮件: {len(synced_emails)}")

                    except Exception as e:
                        self.logger.error(f"单账户收件线程失败: {e}")
                        self.fetch_completed.emit(0, 1, {})

                def stop(self):
                    self.should_stop = True

            # 创建并启动线程
            self.fetch_thread = SingleAccountFetchThread(self.real_account_manager, self.logger, account_id)
            self.fetch_thread.progress_updated.connect(self.update_fetch_progress)
            self.fetch_thread.fetch_completed.connect(self.on_single_account_fetch_completed)
            self.fetch_thread.start()

            # 连接取消按钮
            self.fetch_progress_dialog.canceled.connect(self.cancel_fetch)

        except Exception as e:
            self.logger.error(f"启动单账户收件失败: {e}")
            self.finish_fetch()

    def start_manual_fetch(self):
        """开始手动收件"""
        try:
            self.is_fetching = True
            self.fetch_now_btn.setEnabled(False)
            self.fetch_status_label.setText("📥 收件中...")
            self.fetch_status_label.setStyleSheet("""
                QLabel {
                    padding: 4px 8px;
                    background-color: #4CAF50;
                    color: white;
                    border: 1px solid #45a049;
                    border-radius: 3px;
                    font-size: 11px;
                    font-weight: bold;
                }
            """)

            # 记录开始时间
            from datetime import datetime
            start_time = datetime.now()
            self.last_fetch_time = start_time

            # 创建进度对话框
            self.fetch_progress_dialog = QProgressDialog("正在同步邮件...", "取消", 0, 100, self)
            self.fetch_progress_dialog.setWindowTitle("邮件同步")
            self.fetch_progress_dialog.setModal(True)
            self.fetch_progress_dialog.setValue(0)
            self.fetch_progress_dialog.show()

            # 启动收件线程
            class FetchThread(QThread):
                progress_updated = Signal(int, str, str)  # 进度, 状态消息, 同步类型
                fetch_completed = Signal(int, int, dict)  # 成功数量, 总数量, 统计信息

                def __init__(self, account_manager, logger):
                    super().__init__()
                    self.account_manager = account_manager
                    self.logger = logger
                    self.should_stop = False

                def run(self):
                    try:
                        enabled_accounts = self.account_manager.get_enabled_accounts()
                        total_accounts = len(enabled_accounts)
                        success_count = 0
                        sync_stats = {
                            'first_sync': 0,
                            'incremental': 0,
                            'full_sync': 0,
                            'total_emails': 0
                        }

                        for i, account_config in enumerate(enabled_accounts):
                            if self.should_stop:
                                break

                            try:
                                # 获取同步状态以确定同步类型
                                sync_state = self.account_manager.database.get_account_sync_state(account_config.account_id)
                                sync_type = "增量同步"
                                if sync_state and sync_state.is_first_sync:
                                    sync_type = "首次同步"
                                elif sync_state and sync_state.last_full_sync_time:
                                    from datetime import datetime, timezone
                                    # 安全的时区处理，防止时区错误
                                    try:
                                        current_time = datetime.now(timezone.utc)
                                        last_full_time = sync_state.last_full_sync_time

                                        # 确保两个时间对象都有时区信息
                                        if last_full_time.tzinfo is None:
                                            last_full_time = last_full_time.replace(tzinfo=timezone.utc)
                                            self.logger.warning(f"修复账户 {account_config.account_id} 的时区信息")

                                        days_since_last_full = (current_time - last_full_time).days
                                        if days_since_last_full >= sync_state.force_full_sync_days:
                                            sync_type = "全量同步"
                                    except Exception as time_error:
                                        self.logger.error(f"时间计算失败: {time_error}")
                                        # 时间计算失败时，默认使用增量同步
                                        sync_type = "增量同步"

                                # 更新进度
                                progress = int((i / total_accounts) * 100)
                                self.progress_updated.emit(
                                    progress,
                                    f"正在{sync_type}: {account_config.email}",
                                    sync_type
                                )

                                # 同步账户邮件（使用智能同步）
                                emails = self.account_manager.sync_account_emails_smart(account_config.account_id)
                                success_count += 1

                                # 统计信息
                                if sync_type == "首次同步":
                                    sync_stats['first_sync'] += 1
                                elif sync_type == "全量同步":
                                    sync_stats['full_sync'] += 1
                                else:
                                    sync_stats['incremental'] += 1

                                sync_stats['total_emails'] += len(emails)

                                self.logger.info(f"{sync_type}完成: {account_config.email}, 新邮件: {len(emails)}")

                            except Exception as e:
                                self.logger.error(f"同步账户 {account_config.email} 失败: {e}")
                                continue

                        # 完成
                        self.fetch_completed.emit(success_count, total_accounts, sync_stats)

                    except Exception as e:
                        self.logger.error(f"收件线程异常: {e}")
                        self.fetch_completed.emit(0, 0, {})

                def stop(self):
                    self.should_stop = True

            # 创建并启动线程
            self.fetch_thread = FetchThread(self.real_account_manager, self.logger)
            self.fetch_thread.progress_updated.connect(self.update_fetch_progress)
            self.fetch_thread.fetch_completed.connect(self.on_fetch_completed)
            self.fetch_thread.start()

            # 连接取消按钮
            self.fetch_progress_dialog.canceled.connect(self.cancel_fetch)

        except Exception as e:
            self.logger.error(f"启动手动收件失败: {e}")
            self.finish_fetch()

    def start_manual_fetch_selective(self, selected_account_ids: List[str]):
        """开始选择性手动收件"""
        try:
            self.is_fetching = True
            self.fetch_now_btn.setEnabled(False)
            self.fetch_status_label.setText("📥 选择性收件中...")
            self.fetch_status_label.setStyleSheet("""
                QLabel {
                    padding: 4px 8px;
                    background-color: #FF9800;
                    color: white;
                    border: 1px solid #F57C00;
                    border-radius: 3px;
                    font-size: 11px;
                    font-weight: bold;
                }
            """)

            # 记录开始时间
            from datetime import datetime, timezone
            start_time = datetime.now(timezone.utc)
            self.last_fetch_time = start_time

            # 创建进度对话框
            self.fetch_progress_dialog = QProgressDialog("正在同步选中的邮箱...", "取消", 0, 100, self)
            self.fetch_progress_dialog.setWindowTitle("选择性邮件同步")
            self.fetch_progress_dialog.setModal(True)
            self.fetch_progress_dialog.setValue(0)
            self.fetch_progress_dialog.show()

            # 启动选择性收件线程
            class SelectiveFetchThread(QThread):
                progress_updated = Signal(int, str, str)  # 进度, 状态消息, 同步类型
                fetch_completed = Signal(int, int, dict)  # 成功数量, 总数量, 统计信息

                def __init__(self, account_manager, logger, selected_account_ids):
                    super().__init__()
                    self.account_manager = account_manager
                    self.logger = logger
                    self.selected_account_ids = selected_account_ids
                    self.should_stop = False

                def run(self):
                    try:
                        self.logger.info(f"选择性收件线程开始运行，目标账户ID: {self.selected_account_ids}")

                        # 获取选中的账户配置
                        all_accounts = self.account_manager.get_enabled_accounts()
                        self.logger.info(f"获取到所有启用账户数量: {len(all_accounts)}")

                        selected_accounts = [acc for acc in all_accounts if acc.account_id in self.selected_account_ids]
                        self.logger.info(f"匹配到的选中账户数量: {len(selected_accounts)}")

                        if not selected_accounts:
                            self.logger.error("没有找到匹配的启用账户")
                            self.fetch_completed.emit(0, 0, {})
                            return

                        total_accounts = len(selected_accounts)
                        success_count = 0
                        sync_stats = {
                            'first_sync': 0,
                            'incremental': 0,
                            'full_sync': 0,
                            'total_emails': 0
                        }

                        for i, account_config in enumerate(selected_accounts):
                            if self.should_stop:
                                break

                            try:
                                # 获取同步状态以确定同步类型
                                sync_state = self.account_manager.database.get_account_sync_state(account_config.account_id)
                                sync_type = "增量同步"
                                if sync_state and sync_state.is_first_sync:
                                    sync_type = "首次同步"

                                # 更新进度
                                progress = int((i / total_accounts) * 100)
                                self.progress_updated.emit(
                                    progress,
                                    f"正在{sync_type}: {account_config.email}",
                                    sync_type
                                )

                                # 执行同步
                                synced_emails = self.account_manager.sync_account_emails_smart(
                                    account_config.account_id, "INBOX"
                                )

                                # 统计结果
                                if sync_type == "首次同步":
                                    sync_stats['first_sync'] += 1
                                elif sync_type == "全量同步":
                                    sync_stats['full_sync'] += 1
                                else:
                                    sync_stats['incremental'] += 1

                                sync_stats['total_emails'] += len(synced_emails)
                                success_count += 1

                                self.logger.info(f"选择性同步完成: {account_config.email}, 新邮件: {len(synced_emails)}")

                            except Exception as e:
                                self.logger.error(f"选择性同步账户失败: {account_config.email}, 错误: {e}")
                                continue

                        # 完成进度
                        self.progress_updated.emit(100, "同步完成", "")
                        self.fetch_completed.emit(success_count, total_accounts, sync_stats)

                    except Exception as e:
                        self.logger.error(f"选择性收件线程失败: {e}")
                        self.fetch_completed.emit(0, len(self.selected_account_ids), {})

                def stop(self):
                    self.should_stop = True

            # 创建并启动线程
            self.fetch_thread = SelectiveFetchThread(self.real_account_manager, self.logger, selected_account_ids)
            self.fetch_thread.progress_updated.connect(self.update_fetch_progress)
            self.fetch_thread.fetch_completed.connect(self.on_selective_fetch_completed)
            self.fetch_thread.start()

            # 连接取消按钮
            self.fetch_progress_dialog.canceled.connect(self.cancel_fetch)

        except Exception as e:
            self.logger.error(f"启动选择性手动收件失败: {e}")
            self.finish_fetch()

    def update_fetch_progress(self, progress: int, message: str, sync_type: str = ""):
        """更新收件进度（增强版）"""
        try:
            if hasattr(self, 'fetch_progress_dialog') and self.fetch_progress_dialog:
                self.fetch_progress_dialog.setValue(progress)

                # 根据同步类型显示不同的图标和信息
                if "首次同步" in sync_type:
                    enhanced_message = f"🔄 {message}\n💡 首次同步将获取最新邮件"
                elif "全量同步" in sync_type:
                    enhanced_message = f"📥 {message}\n🔄 执行完整同步以确保数据完整性"
                elif "增量同步" in sync_type:
                    enhanced_message = f"⚡ {message}\n🚀 仅同步新邮件，速度更快"
                else:
                    enhanced_message = message

                self.fetch_progress_dialog.setLabelText(enhanced_message)
        except Exception as e:
            self.logger.error(f"更新收件进度失败: {e}")

    def on_fetch_completed(self, success_count: int, total_count: int, sync_stats: dict):
        """收件完成回调（增强版）"""
        try:
            # 关闭进度对话框
            if hasattr(self, 'fetch_progress_dialog') and self.fetch_progress_dialog:
                self.fetch_progress_dialog.close()
                self.fetch_progress_dialog = None

            # 构建详细的结果信息
            result_message = f"📬 邮件同步完成！\n\n"
            result_message += f"✅ 成功同步: {success_count}/{total_count} 个账户\n"

            if sync_stats:
                result_message += f"📊 同步统计:\n"
                if sync_stats.get('first_sync', 0) > 0:
                    result_message += f"  🔄 首次同步: {sync_stats['first_sync']} 个账户\n"
                if sync_stats.get('incremental', 0) > 0:
                    result_message += f"  ⚡ 增量同步: {sync_stats['incremental']} 个账户\n"
                if sync_stats.get('full_sync', 0) > 0:
                    result_message += f"  📥 全量同步: {sync_stats['full_sync']} 个账户\n"
                result_message += f"  📧 新邮件总数: {sync_stats.get('total_emails', 0)} 封"

            # 显示结果
            if success_count == total_count:
                QMessageBox.information(self, "同步完成", result_message)
            else:
                result_message = f"⚠️ 邮件同步完成，但有部分失败。\n\n成功: {success_count}/{total_count} 个账户"
                QMessageBox.warning(self, "同步完成", result_message)

            # 刷新界面
            self.refresh_account_tree()
            self.update_status()

            # 如果当前选中的是真实账户，刷新邮件列表
            if self.current_account and self.current_account in self.real_account_manager.accounts:
                self.load_real_folder_emails()

            self.finish_fetch()

        except Exception as e:
            self.logger.error(f"处理收件完成回调失败: {e}")
            self.finish_fetch()

    def on_selective_fetch_completed(self, success_count: int, total_count: int, sync_stats: dict):
        """处理选择性收件完成回调"""
        try:
            self.logger.info(f"选择性收件完成: 成功 {success_count}/{total_count} 个账户")

            # 构建结果消息
            result_message = f"🎯 选择性邮件同步完成！\n\n"
            result_message += f"✅ 成功同步: {success_count}/{total_count} 个账户\n"

            if sync_stats:
                result_message += f"\n📊 同步统计:\n"
                if sync_stats.get('first_sync', 0) > 0:
                    result_message += f"   🔄 首次同步: {sync_stats['first_sync']} 个账户\n"
                if sync_stats.get('incremental', 0) > 0:
                    result_message += f"   ⚡ 增量同步: {sync_stats['incremental']} 个账户\n"
                if sync_stats.get('full_sync', 0) > 0:
                    result_message += f"   📥 全量同步: {sync_stats['full_sync']} 个账户\n"
                result_message += f"   📧 总邮件数: {sync_stats.get('total_emails', 0)} 封\n"

            # 显示结果
            if success_count == total_count:
                QMessageBox.information(self, "选择性同步完成", result_message)
            else:
                result_message = f"⚠️ 选择性邮件同步完成，但有部分失败。\n\n成功: {success_count}/{total_count} 个账户"
                QMessageBox.warning(self, "选择性同步完成", result_message)

            # 刷新界面
            self.refresh_account_tree()
            self.update_status()

            # 如果当前选中的是真实账户，刷新邮件列表
            if self.current_account and self.current_account in self.real_account_manager.accounts:
                self.load_real_folder_emails()

            self.finish_fetch()

        except Exception as e:
            self.logger.error(f"处理选择性收件完成回调失败: {e}")
            self.finish_fetch()

    def on_single_account_fetch_completed(self, success_count: int, total_count: int, sync_stats: dict):
        """处理单账户收件完成回调"""
        try:
            account_config = self.real_account_manager.accounts.get(self.current_account)
            account_email = account_config.email if account_config else "未知账户"

            self.logger.info(f"单账户收件完成: {account_email}, 成功: {success_count}")

            # 构建结果消息
            if success_count > 0:
                result_message = f"📧 账户同步完成！\n\n"
                result_message += f"✅ 账户: {account_email}\n"

                if sync_stats:
                    if sync_stats.get('first_sync', 0) > 0:
                        result_message += f"🔄 同步类型: 首次同步\n"
                    elif sync_stats.get('incremental', 0) > 0:
                        result_message += f"⚡ 同步类型: 增量同步\n"
                    result_message += f"📧 新邮件数: {sync_stats.get('total_emails', 0)} 封"

                QMessageBox.information(self, "同步完成", result_message)
            else:
                QMessageBox.warning(self, "同步失败", f"账户 {account_email} 同步失败，请检查网络连接和账户配置。")

            # 刷新界面
            self.refresh_account_tree()
            self.update_status()

            # 刷新当前账户的邮件列表
            if self.current_account and self.current_folder:
                self.load_real_folder_emails()

            self.finish_fetch()

        except Exception as e:
            self.logger.error(f"处理单账户收件完成回调失败: {e}")
            self.finish_fetch()

    def cancel_fetch(self):
        """取消收件"""
        try:
            if hasattr(self, 'fetch_thread') and self.fetch_thread:
                self.fetch_thread.stop()
                self.fetch_thread.wait(3000)  # 等待3秒

            self.finish_fetch()
            QMessageBox.information(self, "已取消", "邮件同步已取消")

        except Exception as e:
            self.logger.error(f"取消收件失败: {e}")
            self.finish_fetch()

    def finish_fetch(self):
        """完成收件，恢复UI状态"""
        try:
            self.is_fetching = False
            self.fetch_now_btn.setEnabled(True)

            # 更新状态显示（智能显示）
            if self.last_fetch_time:
                from datetime import datetime
                time_str = self.last_fetch_time.strftime("%H:%M:%S")
                # 显示更详细的状态信息
                self.fetch_status_label.setText(f"📭 上次同步: {time_str}")
                self.fetch_status_label.setToolTip(f"上次同步时间: {self.last_fetch_time.strftime('%Y-%m-%d %H:%M:%S')}\n使用了智能增量同步技术")
            else:
                self.fetch_status_label.setText("📭 待机")
                self.fetch_status_label.setToolTip("点击立即收件开始同步邮件")

            self.fetch_status_label.setStyleSheet("""
                QLabel {
                    padding: 4px 8px;
                    background-color: #f0f0f0;
                    border: 1px solid #ddd;
                    border-radius: 3px;
                    font-size: 11px;
                }
            """)

            # 清理线程
            if hasattr(self, 'fetch_thread'):
                self.fetch_thread = None

        except Exception as e:
            self.logger.error(f"完成收件状态恢复失败: {e}")

    # ========================================
    # ⏰ 自动收件功能实现
    # ========================================

    def toggle_auto_fetch(self):
        """切换自动收件功能"""
        try:
            if self.auto_fetch_enabled:
                # 关闭自动收件
                self.stop_auto_fetch()
            else:
                # 开启自动收件
                self.start_auto_fetch()

        except Exception as e:
            self.logger.error(f"切换自动收件功能失败: {e}")
            QMessageBox.warning(self, "错误", f"切换自动收件功能失败: {e}")

    def start_auto_fetch(self):
        """开启自动收件"""
        try:
            # 显示设置对话框
            interval, ok = self.show_auto_fetch_settings()
            if not ok:
                return

            self.auto_fetch_interval = interval
            self.auto_fetch_enabled = True

            # 启动定时器
            interval_ms = self.auto_fetch_interval * 60 * 1000  # 转换为毫秒
            self.auto_fetch_timer.start(interval_ms)

            # 更新按钮状态
            self.auto_fetch_btn.setChecked(True)
            self.auto_fetch_btn.setText("⏰ 自动收件 (开启)")
            self.auto_fetch_btn.setToolTip(f"自动收件已开启，间隔: {self.auto_fetch_interval} 分钟\n点击关闭自动收件")

            # 更新状态显示
            self.fetch_status_label.setText(f"⏰ 自动: {self.auto_fetch_interval}分钟")
            self.fetch_status_label.setStyleSheet("""
                QLabel {
                    padding: 4px 8px;
                    background-color: #FF9800;
                    color: white;
                    border: 1px solid #F57C00;
                    border-radius: 3px;
                    font-size: 11px;
                    font-weight: bold;
                }
            """)

            self.logger.info(f"自动收件已开启，间隔: {self.auto_fetch_interval} 分钟")
            QMessageBox.information(self, "自动收件", f"自动收件已开启！\n收件间隔: {self.auto_fetch_interval} 分钟")

        except Exception as e:
            self.logger.error(f"开启自动收件失败: {e}")
            QMessageBox.warning(self, "错误", f"开启自动收件失败: {e}")

    def stop_auto_fetch(self):
        """关闭自动收件"""
        try:
            self.auto_fetch_enabled = False

            # 停止定时器
            if self.auto_fetch_timer and self.auto_fetch_timer.isActive():
                self.auto_fetch_timer.stop()

            # 更新按钮状态
            self.auto_fetch_btn.setChecked(False)
            self.auto_fetch_btn.setText("⏰ 自动收件")
            self.auto_fetch_btn.setToolTip("配置自动收件间隔和设置")

            # 更新状态显示
            if self.last_fetch_time:
                from datetime import datetime
                time_str = self.last_fetch_time.strftime("%H:%M:%S")
                self.fetch_status_label.setText(f"📭 上次: {time_str}")
            else:
                self.fetch_status_label.setText("📭 待机")

            self.fetch_status_label.setStyleSheet("""
                QLabel {
                    padding: 4px 8px;
                    background-color: #f0f0f0;
                    border: 1px solid #ddd;
                    border-radius: 3px;
                    font-size: 11px;
                }
            """)

            self.logger.info("自动收件已关闭")
            QMessageBox.information(self, "自动收件", "自动收件已关闭")

        except Exception as e:
            self.logger.error(f"关闭自动收件失败: {e}")

    def show_auto_fetch_settings(self):
        """显示自动收件设置对话框"""
        try:
            dialog = QDialog(self)
            dialog.setWindowTitle("自动收件设置")
            dialog.setModal(True)
            dialog.resize(350, 200)

            layout = QVBoxLayout(dialog)

            # 设置组
            settings_group = QGroupBox("收件间隔设置")
            settings_layout = QFormLayout(settings_group)

            # 间隔设置
            interval_spin = QSpinBox()
            interval_spin.setRange(1, 60)
            interval_spin.setValue(self.auto_fetch_interval)
            interval_spin.setSuffix(" 分钟")
            interval_spin.setToolTip("设置自动收件的时间间隔")
            settings_layout.addRow("收件间隔:", interval_spin)

            layout.addWidget(settings_group)

            # 说明文本
            info_label = QLabel("💡 提示：\n• 建议间隔不少于5分钟，避免频繁请求\n• 自动收件将同步所有启用的邮件账户\n• 可随时点击按钮关闭自动收件")
            info_label.setStyleSheet("""
                QLabel {
                    background-color: #f8f9fa;
                    border: 1px solid #dee2e6;
                    border-radius: 4px;
                    padding: 10px;
                    color: #495057;
                }
            """)
            layout.addWidget(info_label)

            # 按钮
            button_layout = QHBoxLayout()

            ok_btn = QPushButton("确定")
            ok_btn.setStyleSheet("""
                QPushButton {
                    background-color: #4CAF50;
                    color: white;
                    border: none;
                    padding: 8px 20px;
                    border-radius: 4px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #45a049;
                }
            """)
            ok_btn.clicked.connect(dialog.accept)

            cancel_btn = QPushButton("取消")
            cancel_btn.setStyleSheet("""
                QPushButton {
                    background-color: #6c757d;
                    color: white;
                    border: none;
                    padding: 8px 20px;
                    border-radius: 4px;
                }
                QPushButton:hover {
                    background-color: #5a6268;
                }
            """)
            cancel_btn.clicked.connect(dialog.reject)

            button_layout.addStretch()
            button_layout.addWidget(ok_btn)
            button_layout.addWidget(cancel_btn)

            layout.addLayout(button_layout)

            # 显示对话框
            result = dialog.exec()
            if result == QDialog.Accepted:
                return interval_spin.value(), True
            else:
                return self.auto_fetch_interval, False

        except Exception as e:
            self.logger.error(f"显示自动收件设置对话框失败: {e}")
            return self.auto_fetch_interval, False

    def auto_fetch_emails(self):
        """自动收件定时器回调"""
        try:
            if not self.auto_fetch_enabled:
                return

            if self.is_fetching:
                self.logger.info("正在手动收件中，跳过本次自动收件")
                return

            # 检查是否有启用的账户
            enabled_accounts = self.real_account_manager.get_enabled_accounts()
            if not enabled_accounts:
                self.logger.info("没有启用的账户，跳过自动收件")
                return

            self.logger.info("开始自动收件...")

            # 更新状态显示
            self.fetch_status_label.setText("📥 自动收件中...")
            self.fetch_status_label.setStyleSheet("""
                QLabel {
                    padding: 4px 8px;
                    background-color: #4CAF50;
                    color: white;
                    border: 1px solid #45a049;
                    border-radius: 3px;
                    font-size: 11px;
                    font-weight: bold;
                }
            """)

            # 执行同步（静默模式，不显示进度对话框）
            self.perform_silent_sync()

        except Exception as e:
            self.logger.error(f"自动收件失败: {e}")

    def perform_silent_sync(self):
        """执行静默同步"""
        try:
            class SilentSyncThread(QThread):
                sync_completed = Signal(int, int)  # 成功数量, 总数量

                def __init__(self, account_manager, logger):
                    super().__init__()
                    self.account_manager = account_manager
                    self.logger = logger

                def run(self):
                    try:
                        enabled_accounts = self.account_manager.get_enabled_accounts()
                        total_accounts = len(enabled_accounts)
                        success_count = 0

                        for account_config in enabled_accounts:
                            try:
                                # 使用智能同步账户邮件
                                emails = self.account_manager.sync_account_emails_smart(account_config.account_id)
                                success_count += 1
                                self.logger.info(f"自动同步完成: {account_config.email}, 新邮件: {len(emails)}")

                            except Exception as e:
                                self.logger.error(f"自动同步账户 {account_config.email} 失败: {e}")
                                continue

                        # 完成
                        self.sync_completed.emit(success_count, total_accounts)

                    except Exception as e:
                        self.logger.error(f"静默同步线程异常: {e}")
                        self.sync_completed.emit(0, 0)

            # 创建并启动线程
            self.silent_sync_thread = SilentSyncThread(self.real_account_manager, self.logger)
            self.silent_sync_thread.sync_completed.connect(self.on_silent_sync_completed)
            self.silent_sync_thread.start()

        except Exception as e:
            self.logger.error(f"执行静默同步失败: {e}")
            self.restore_auto_fetch_status()

    def on_silent_sync_completed(self, success_count: int, total_count: int):
        """静默同步完成回调"""
        try:
            from datetime import datetime
            self.last_fetch_time = datetime.now()

            # 刷新界面
            self.refresh_account_tree()
            self.update_status()

            # 如果当前选中的是真实账户，刷新邮件列表
            if self.current_account and self.current_account in self.real_account_manager.accounts:
                self.load_real_folder_emails()

            # 恢复状态显示
            self.restore_auto_fetch_status()

            self.logger.info(f"自动收件完成: 成功 {success_count}/{total_count} 个账户")

        except Exception as e:
            self.logger.error(f"处理静默同步完成回调失败: {e}")
            self.restore_auto_fetch_status()

    def restore_auto_fetch_status(self):
        """恢复自动收件状态显示"""
        try:
            if self.auto_fetch_enabled:
                self.fetch_status_label.setText(f"⏰ 自动: {self.auto_fetch_interval}分钟")
                self.fetch_status_label.setStyleSheet("""
                    QLabel {
                        padding: 4px 8px;
                        background-color: #FF9800;
                        color: white;
                        border: 1px solid #F57C00;
                        border-radius: 3px;
                        font-size: 11px;
                        font-weight: bold;
                    }
                """)
            else:
                if self.last_fetch_time:
                    from datetime import datetime
                    time_str = self.last_fetch_time.strftime("%H:%M:%S")
                    self.fetch_status_label.setText(f"📭 上次: {time_str}")
                else:
                    self.fetch_status_label.setText("📭 待机")

                self.fetch_status_label.setStyleSheet("""
                    QLabel {
                        padding: 4px 8px;
                        background-color: #f0f0f0;
                        border: 1px solid #ddd;
                        border-radius: 3px;
                        font-size: 11px;
                    }
                """)
        except Exception as e:
            self.logger.error(f"恢复自动收件状态显示失败: {e}")

    def _check_license_status(self):
        """定期检查许可证状态"""
        try:
            if not self.license_manager.is_licensed():
                # 许可证失效，显示警告并要求重新验证
                reply = QMessageBox.warning(
                    self,
                    "许可证过期",
                    "您的许可证已过期或失效，需要重新验证。\n\n点击'确定'重新验证，点击'取消'退出程序。",
                    QMessageBox.StandardButton.Ok | QMessageBox.StandardButton.Cancel
                )

                if reply == QMessageBox.StandardButton.Ok:
                    # 显示许可证验证对话框
                    license_dialog = LicenseDialog(self, self.license_manager)
                    result = license_dialog.exec()

                    if result != QDialog.DialogCode.Accepted or not self.license_manager.is_licensed():
                        QMessageBox.critical(self, "许可证验证失败", "许可证验证失败，程序将退出。")
                        self.close()
                else:
                    self.close()

        except Exception as e:
            self.logger.error(f"检查许可证状态失败: {e}")

    def closeEvent(self, event):
        """窗口关闭事件处理"""
        try:
            # 停止许可证检查定时器
            if hasattr(self, 'license_check_timer'):
                self.license_check_timer.stop()

            # 停止自动收件定时器
            if self.auto_fetch_timer:
                self.auto_fetch_timer.stop()

            # 清理资源
            if hasattr(self, 'license_manager'):
                self.license_manager.cleanup_expired_licenses()

        except Exception as e:
            print(f"关闭程序时清理资源失败: {e}")

        event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用程序信息
    app.setApplicationName("微软ou批量管理")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("Microsoft OU Management Solutions")

    # 许可证验证
    try:
        license_manager = LicenseManager()

        # 检查是否已有有效许可证
        if not license_manager.is_licensed():
            # 显示许可证验证对话框
            license_dialog = LicenseDialog(None, license_manager)
            result = license_dialog.exec()

            if result != QDialog.DialogCode.Accepted:
                print("许可证验证失败或用户取消，程序退出")
                sys.exit(0)

            # 再次检查许可证状态
            if not license_manager.is_licensed():
                QMessageBox.critical(None, "许可证错误", "许可证验证失败，无法启动程序")
                sys.exit(0)

        print("✅ 许可证验证通过，程序启动中...")

    except Exception as e:
        print(f"许可证验证过程中发生错误: {e}")
        QMessageBox.critical(None, "启动错误", f"许可证验证失败: {str(e)}")
        sys.exit(0)

    # 显示启动提示对话框
    try:
        from ui.startup_notice_dialog import StartupNoticeDialog

        if StartupNoticeDialog.should_show_notice():
            startup_dialog = StartupNoticeDialog()

            # 连接注意事项信号
            def show_notice_from_startup():
                try:
                    from ui.notice_panel_dialog import NoticePanelDialog
                    notice_panel = NoticePanelDialog(startup_dialog)
                    notice_panel.show()
                except Exception as e:
                    print(f"显示注意事项面板失败: {e}")

            startup_dialog.show_notice_requested.connect(show_notice_from_startup)

            result = startup_dialog.exec()
            if result != QDialog.DialogCode.Accepted:
                print("用户选择退出程序")
                sys.exit(0)
    except Exception as e:
        print(f"启动提示对话框出错: {e}")
        # 如果启动提示出错，继续启动程序

    # 创建主窗口
    window = EnterpriseEmailManager()
    window.show()

    # 运行应用程序
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
