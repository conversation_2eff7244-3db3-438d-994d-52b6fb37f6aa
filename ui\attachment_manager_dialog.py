#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
附件管理对话框
提供附件查看、下载、预览等功能
"""

import sys
import os
import mimetypes
from pathlib import Path
from typing import List, Dict, Any, Optional

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QListWidget, QListWidgetItem, QTextEdit,
    QGroupBox, QSplitter, QProgressBar, QFileDialog, QMessageBox,
    QTableWidget, QTableWidgetItem, QHeaderView, QAbstractItemView,
    QWidget, QFrame, QScrollArea
)
from PySide6.QtCore import Qt, Signal, QThread, QTimer, QSize
from PySide6.QtGui import QFont, QPixmap, QIcon, QColor

class AttachmentDownloadThread(QThread):
    """附件下载线程"""
    
    progress_updated = Signal(int)  # 进度更新信号
    download_finished = Signal(str, bool)  # 下载完成信号 (文件路径, 是否成功)
    
    def __init__(self, attachment_data, save_path):
        super().__init__()
        self.attachment_data = attachment_data
        self.save_path = save_path
    
    def run(self):
        """执行下载"""
        try:
            # 模拟下载过程
            import time
            for i in range(101):
                time.sleep(0.01)  # 模拟下载延迟
                self.progress_updated.emit(i)
            
            # 这里应该是实际的附件保存逻辑
            # 暂时创建一个空文件作为示例
            with open(self.save_path, 'wb') as f:
                f.write(self.attachment_data.get('content', b''))
            
            self.download_finished.emit(self.save_path, True)
            
        except Exception as e:
            self.download_finished.emit(str(e), False)

class AttachmentManagerDialog(QDialog):
    """附件管理对话框"""
    
    def __init__(self, email_record, parent=None):
        super().__init__(parent)
        self.email_record = email_record
        self.attachments = self.parse_attachments()
        
        self.setWindowTitle(f"附件管理 - {email_record.subject or '(无主题)'}")
        self.setModal(True)
        self.resize(800, 600)
        
        self.download_thread = None
        
        self.setup_ui()
        self.load_attachments()
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 邮件信息区域
        info_group = QGroupBox("邮件信息")
        info_layout = QGridLayout(info_group)
        
        info_layout.addWidget(QLabel("主题:"), 0, 0)
        info_layout.addWidget(QLabel(self.email_record.subject or "(无主题)"), 0, 1)
        
        info_layout.addWidget(QLabel("发件人:"), 1, 0)
        info_layout.addWidget(QLabel(self.email_record.sender or "(未知)"), 1, 1)
        
        info_layout.addWidget(QLabel("附件数量:"), 2, 0)
        info_layout.addWidget(QLabel(str(len(self.attachments))), 2, 1)
        
        layout.addWidget(info_group)
        
        # 主要内容区域
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧：附件列表
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        
        # 附件列表标题
        list_label = QLabel("附件列表")
        list_label.setFont(QFont("", 10, QFont.Weight.Bold))
        left_layout.addWidget(list_label)
        
        # 附件表格
        self.attachment_table = QTableWidget()
        self.attachment_table.setColumnCount(4)
        self.attachment_table.setHorizontalHeaderLabels(["文件名", "类型", "大小", "操作"])
        
        # 设置表格属性
        self.attachment_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.attachment_table.setAlternatingRowColors(True)
        self.attachment_table.horizontalHeader().setStretchLastSection(True)
        self.attachment_table.verticalHeader().setVisible(False)
        
        # 连接信号
        self.attachment_table.itemSelectionChanged.connect(self.on_attachment_selected)
        
        left_layout.addWidget(self.attachment_table)
        
        # 批量操作按钮
        batch_layout = QHBoxLayout()
        
        self.download_all_btn = QPushButton("下载全部")
        self.download_all_btn.clicked.connect(self.download_all_attachments)
        batch_layout.addWidget(self.download_all_btn)
        
        self.save_all_btn = QPushButton("保存全部")
        self.save_all_btn.clicked.connect(self.save_all_attachments)
        batch_layout.addWidget(self.save_all_btn)
        
        batch_layout.addStretch()
        
        left_layout.addLayout(batch_layout)
        
        main_splitter.addWidget(left_widget)
        
        # 右侧：附件详情和预览
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        
        # 附件详情
        details_group = QGroupBox("附件详情")
        details_layout = QVBoxLayout(details_group)
        
        self.details_text = QTextEdit()
        self.details_text.setReadOnly(True)
        self.details_text.setMaximumHeight(150)
        details_layout.addWidget(self.details_text)
        
        right_layout.addWidget(details_group)
        
        # 预览区域
        preview_group = QGroupBox("预览")
        preview_layout = QVBoxLayout(preview_group)
        
        self.preview_area = QScrollArea()
        self.preview_widget = QLabel("请选择附件查看预览")
        self.preview_widget.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.preview_widget.setStyleSheet("QLabel { background-color: #f5f5f5; border: 1px solid #ddd; }")
        self.preview_widget.setMinimumHeight(200)
        self.preview_area.setWidget(self.preview_widget)
        self.preview_area.setWidgetResizable(True)
        
        preview_layout.addWidget(self.preview_area)
        
        right_layout.addWidget(preview_group)
        
        # 单个附件操作按钮
        single_layout = QHBoxLayout()
        
        self.download_btn = QPushButton("下载")
        self.download_btn.clicked.connect(self.download_selected_attachment)
        self.download_btn.setEnabled(False)
        single_layout.addWidget(self.download_btn)
        
        self.save_btn = QPushButton("另存为")
        self.save_btn.clicked.connect(self.save_selected_attachment)
        self.save_btn.setEnabled(False)
        single_layout.addWidget(self.save_btn)
        
        self.open_btn = QPushButton("打开")
        self.open_btn.clicked.connect(self.open_selected_attachment)
        self.open_btn.setEnabled(False)
        single_layout.addWidget(self.open_btn)
        
        single_layout.addStretch()
        
        right_layout.addLayout(single_layout)
        
        main_splitter.addWidget(right_widget)
        main_splitter.setSizes([400, 400])
        
        layout.addWidget(main_splitter)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.refresh_attachments)
        button_layout.addWidget(self.refresh_btn)
        
        button_layout.addStretch()
        
        self.close_btn = QPushButton("关闭")
        self.close_btn.clicked.connect(self.accept)
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)
    
    def parse_attachments(self):
        """解析邮件附件"""
        try:
            # 这里应该从邮件记录中解析附件信息
            # 暂时返回模拟数据
            attachments = [
                {
                    'filename': 'document.pdf',
                    'content_type': 'application/pdf',
                    'size': 1024000,
                    'content': b'PDF content...',
                    'is_inline': False
                },
                {
                    'filename': 'image.jpg',
                    'content_type': 'image/jpeg',
                    'size': 512000,
                    'content': b'JPEG content...',
                    'is_inline': False
                },
                {
                    'filename': 'data.xlsx',
                    'content_type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    'size': 256000,
                    'content': b'Excel content...',
                    'is_inline': False
                }
            ]
            
            return attachments
            
        except Exception as e:
            print(f"解析附件失败: {e}")
            return []
    
    def load_attachments(self):
        """加载附件到表格"""
        try:
            self.attachment_table.setRowCount(len(self.attachments))
            
            for row, attachment in enumerate(self.attachments):
                # 文件名
                filename_item = QTableWidgetItem(attachment['filename'])
                self.attachment_table.setItem(row, 0, filename_item)
                
                # 文件类型
                file_type = self.get_file_type_description(attachment['content_type'])
                type_item = QTableWidgetItem(file_type)
                self.attachment_table.setItem(row, 1, type_item)
                
                # 文件大小
                size_str = self.format_file_size(attachment['size'])
                size_item = QTableWidgetItem(size_str)
                self.attachment_table.setItem(row, 2, size_item)
                
                # 操作按钮
                action_widget = QWidget()
                action_layout = QHBoxLayout(action_widget)
                action_layout.setContentsMargins(2, 2, 2, 2)
                
                download_btn = QPushButton("⬇️")
                download_btn.setMaximumSize(30, 25)
                download_btn.setToolTip("下载附件")
                download_btn.clicked.connect(lambda checked, r=row: self.download_attachment_by_row(r))
                action_layout.addWidget(download_btn)
                
                preview_btn = QPushButton("👁️")
                preview_btn.setMaximumSize(30, 25)
                preview_btn.setToolTip("预览附件")
                preview_btn.clicked.connect(lambda checked, r=row: self.preview_attachment_by_row(r))
                action_layout.addWidget(preview_btn)
                
                action_layout.addStretch()
                
                self.attachment_table.setCellWidget(row, 3, action_widget)
            
            # 调整列宽
            header = self.attachment_table.horizontalHeader()
            header.resizeSection(0, 200)  # 文件名
            header.resizeSection(1, 120)  # 类型
            header.resizeSection(2, 80)   # 大小
            header.resizeSection(3, 100)  # 操作
            
        except Exception as e:
            QMessageBox.warning(self, "错误", f"加载附件失败: {e}")
    
    def on_attachment_selected(self):
        """附件选择事件"""
        try:
            current_row = self.attachment_table.currentRow()
            if current_row >= 0 and current_row < len(self.attachments):
                attachment = self.attachments[current_row]
                
                # 更新详情
                self.update_attachment_details(attachment)
                
                # 更新预览
                self.update_attachment_preview(attachment)
                
                # 启用按钮
                self.download_btn.setEnabled(True)
                self.save_btn.setEnabled(True)
                self.open_btn.setEnabled(True)
            else:
                # 禁用按钮
                self.download_btn.setEnabled(False)
                self.save_btn.setEnabled(False)
                self.open_btn.setEnabled(False)
                
        except Exception as e:
            print(f"附件选择处理失败: {e}")
    
    def update_attachment_details(self, attachment):
        """更新附件详情"""
        try:
            details = f"""文件名: {attachment['filename']}
类型: {attachment['content_type']}
大小: {self.format_file_size(attachment['size'])}
是否内嵌: {'是' if attachment.get('is_inline', False) else '否'}

描述: {self.get_file_type_description(attachment['content_type'])}
"""
            self.details_text.setPlainText(details)
            
        except Exception as e:
            self.details_text.setPlainText(f"详情加载失败: {e}")
    
    def update_attachment_preview(self, attachment):
        """更新附件预览"""
        try:
            content_type = attachment['content_type']
            filename = attachment['filename']
            
            if content_type.startswith('image/'):
                # 图片预览
                self.preview_widget.setText("图片预览功能开发中...")
                self.preview_widget.setStyleSheet("QLabel { background-color: #e8f4fd; border: 1px solid #0078d4; color: #0078d4; }")
            elif content_type == 'text/plain':
                # 文本预览
                self.preview_widget.setText("文本预览功能开发中...")
                self.preview_widget.setStyleSheet("QLabel { background-color: #f0f8e8; border: 1px solid #4caf50; color: #4caf50; }")
            elif 'pdf' in content_type:
                # PDF预览
                self.preview_widget.setText("PDF预览功能开发中...")
                self.preview_widget.setStyleSheet("QLabel { background-color: #fff3e0; border: 1px solid #ff9800; color: #ff9800; }")
            else:
                # 其他文件类型
                self.preview_widget.setText(f"无法预览此类型文件\n{filename}\n{self.get_file_type_description(content_type)}")
                self.preview_widget.setStyleSheet("QLabel { background-color: #f5f5f5; border: 1px solid #ddd; color: #666; }")
                
        except Exception as e:
            self.preview_widget.setText(f"预览失败: {e}")
            self.preview_widget.setStyleSheet("QLabel { background-color: #ffebee; border: 1px solid #f44336; color: #f44336; }")
    
    def download_attachment_by_row(self, row):
        """按行下载附件"""
        try:
            if row >= 0 and row < len(self.attachments):
                attachment = self.attachments[row]
                self.download_attachment(attachment)
        except Exception as e:
            QMessageBox.warning(self, "错误", f"下载附件失败: {e}")
    
    def preview_attachment_by_row(self, row):
        """按行预览附件"""
        try:
            if row >= 0 and row < len(self.attachments):
                # 选中该行
                self.attachment_table.selectRow(row)
        except Exception as e:
            QMessageBox.warning(self, "错误", f"预览附件失败: {e}")
    
    def download_selected_attachment(self):
        """下载选中的附件"""
        try:
            current_row = self.attachment_table.currentRow()
            if current_row >= 0:
                attachment = self.attachments[current_row]
                self.download_attachment(attachment)
        except Exception as e:
            QMessageBox.warning(self, "错误", f"下载附件失败: {e}")
    
    def save_selected_attachment(self):
        """另存为选中的附件"""
        try:
            current_row = self.attachment_table.currentRow()
            if current_row >= 0:
                attachment = self.attachments[current_row]
                self.save_attachment_as(attachment)
        except Exception as e:
            QMessageBox.warning(self, "错误", f"保存附件失败: {e}")
    
    def open_selected_attachment(self):
        """打开选中的附件"""
        try:
            current_row = self.attachment_table.currentRow()
            if current_row >= 0:
                attachment = self.attachments[current_row]
                self.open_attachment(attachment)
        except Exception as e:
            QMessageBox.warning(self, "错误", f"打开附件失败: {e}")
    
    def download_attachment(self, attachment):
        """下载附件到默认下载文件夹"""
        try:
            # 获取默认下载路径
            downloads_path = Path.home() / "Downloads"
            downloads_path.mkdir(exist_ok=True)
            
            file_path = downloads_path / attachment['filename']
            
            # 如果文件已存在，添加数字后缀
            counter = 1
            original_path = file_path
            while file_path.exists():
                stem = original_path.stem
                suffix = original_path.suffix
                file_path = downloads_path / f"{stem}_{counter}{suffix}"
                counter += 1
            
            self.start_download(attachment, str(file_path))
            
        except Exception as e:
            QMessageBox.warning(self, "错误", f"下载附件失败: {e}")
    
    def save_attachment_as(self, attachment):
        """另存为附件"""
        try:
            filename, _ = QFileDialog.getSaveFileName(
                self,
                "保存附件",
                attachment['filename'],
                "所有文件 (*.*)"
            )
            
            if filename:
                self.start_download(attachment, filename)
                
        except Exception as e:
            QMessageBox.warning(self, "错误", f"保存附件失败: {e}")
    
    def open_attachment(self, attachment):
        """打开附件"""
        try:
            # 先下载到临时文件夹
            import tempfile
            temp_dir = Path(tempfile.gettempdir()) / "email_attachments"
            temp_dir.mkdir(exist_ok=True)
            
            temp_file = temp_dir / attachment['filename']
            
            # 保存临时文件
            with open(temp_file, 'wb') as f:
                f.write(attachment.get('content', b''))
            
            # 使用系统默认程序打开
            import subprocess
            import platform
            
            if platform.system() == 'Windows':
                os.startfile(str(temp_file))
            elif platform.system() == 'Darwin':  # macOS
                subprocess.run(['open', str(temp_file)])
            else:  # Linux
                subprocess.run(['xdg-open', str(temp_file)])
                
            QMessageBox.information(self, "提示", f"已使用默认程序打开: {attachment['filename']}")
            
        except Exception as e:
            QMessageBox.warning(self, "错误", f"打开附件失败: {e}")
    
    def start_download(self, attachment, save_path):
        """开始下载"""
        try:
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            
            # 创建下载线程
            self.download_thread = AttachmentDownloadThread(attachment, save_path)
            self.download_thread.progress_updated.connect(self.progress_bar.setValue)
            self.download_thread.download_finished.connect(self.on_download_finished)
            self.download_thread.start()
            
        except Exception as e:
            self.progress_bar.setVisible(False)
            QMessageBox.warning(self, "错误", f"开始下载失败: {e}")
    
    def on_download_finished(self, result, success):
        """下载完成处理"""
        try:
            self.progress_bar.setVisible(False)
            
            if success:
                QMessageBox.information(self, "下载完成", f"文件已保存到:\n{result}")
            else:
                QMessageBox.warning(self, "下载失败", f"下载失败:\n{result}")
                
        except Exception as e:
            QMessageBox.warning(self, "错误", f"下载完成处理失败: {e}")
    
    def download_all_attachments(self):
        """下载所有附件"""
        try:
            if not self.attachments:
                QMessageBox.information(self, "提示", "没有附件可下载")
                return
            
            # 选择保存文件夹
            folder = QFileDialog.getExistingDirectory(self, "选择保存文件夹")
            if folder:
                for attachment in self.attachments:
                    file_path = Path(folder) / attachment['filename']
                    # 这里应该实现批量下载逻辑
                    pass
                
                QMessageBox.information(self, "提示", f"开始下载 {len(self.attachments)} 个附件到:\n{folder}")
                
        except Exception as e:
            QMessageBox.warning(self, "错误", f"批量下载失败: {e}")
    
    def save_all_attachments(self):
        """保存所有附件"""
        self.download_all_attachments()
    
    def refresh_attachments(self):
        """刷新附件列表"""
        try:
            self.attachments = self.parse_attachments()
            self.load_attachments()
            QMessageBox.information(self, "提示", "附件列表已刷新")
        except Exception as e:
            QMessageBox.warning(self, "错误", f"刷新失败: {e}")
    
    def get_file_type_description(self, content_type):
        """获取文件类型描述"""
        type_map = {
            'application/pdf': 'PDF文档',
            'image/jpeg': 'JPEG图片',
            'image/png': 'PNG图片',
            'image/gif': 'GIF图片',
            'text/plain': '文本文件',
            'text/html': 'HTML文件',
            'application/vnd.ms-excel': 'Excel文档',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'Excel文档',
            'application/msword': 'Word文档',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Word文档',
            'application/zip': 'ZIP压缩包',
            'application/x-rar-compressed': 'RAR压缩包',
        }
        
        return type_map.get(content_type, content_type)
    
    def format_file_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0B"
        
        size_names = ["B", "KB", "MB", "GB"]
        import math
        i = int(math.floor(math.log(size_bytes, 1024)))
        p = math.pow(1024, i)
        s = round(size_bytes / p, 2)
        return f"{s} {size_names[i]}"

if __name__ == "__main__":
    from PySide6.QtWidgets import QApplication
    from email_database import EmailRecord
    from datetime import datetime
    
    app = QApplication(sys.argv)
    
    # 创建模拟邮件记录
    email_record = EmailRecord(
        id=1,
        account_id="test",
        folder_name="INBOX",
        message_id="test",
        subject="测试邮件附件",
        sender="<EMAIL>",
        recipients="<EMAIL>",
        date_sent=datetime.now(),
        size=1024,
        flags="",
        text_body="测试邮件内容",
        html_body="",
        created_at=datetime.now()
    )
    
    dialog = AttachmentManagerDialog(email_record)
    dialog.show()
    
    sys.exit(app.exec())
