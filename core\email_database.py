#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件数据库设计模块
提供邮件数据的存储、查询和管理功能
"""

import sqlite3
import json
import time
import hashlib
import logging
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from datetime import datetime, timezone
from pathlib import Path
import threading

@dataclass
class EmailRecord:
    """邮件记录"""
    id: Optional[int] = None
    account_id: str = ""
    folder_name: str = ""
    message_id: str = ""
    uid: int = 0
    subject: str = ""
    sender: str = ""
    recipients: str = ""  # JSON格式
    date_sent: Optional[datetime] = None
    date_received: Optional[datetime] = None
    size: int = 0
    flags: str = ""  # JSON格式
    text_body: Optional[str] = None
    html_body: Optional[str] = None
    attachments: str = ""  # JSON格式
    content_hash: str = ""
    raw_content: Optional[bytes] = None
    is_deleted: bool = False
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

@dataclass
class FolderRecord:
    """文件夹记录"""
    id: Optional[int] = None
    account_id: str = ""
    folder_name: str = ""
    display_name: str = ""
    folder_type: str = ""
    total_messages: int = 0
    unread_messages: int = 0
    last_sync_uid: int = 0
    last_sync_modseq: int = 0
    last_sync_time: datetime = None
    created_at: datetime = None
    updated_at: datetime = None

@dataclass
class AccountRecord:
    """账户记录"""
    id: Optional[int] = None
    account_id: str = ""
    email: str = ""
    display_name: str = ""
    server: str = ""
    port: int = 993
    last_sync_time: datetime = None
    total_emails: int = 0
    total_size: int = 0
    is_active: bool = True
    created_at: datetime = None
    updated_at: datetime = None

@dataclass
class AccountSyncState:
    """账户同步状态"""
    id: Optional[int] = None
    account_id: str = ""
    is_first_sync: bool = True
    last_full_sync_time: Optional[datetime] = None
    last_incremental_sync_time: Optional[datetime] = None
    max_uid_synced: int = 0
    total_emails_synced: int = 0
    sync_strategy: str = "auto"
    first_sync_limit: int = 100
    incremental_days_back: int = 7
    force_full_sync_days: int = 30
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

class EmailDatabase:
    """邮件数据库管理器"""
    
    def __init__(self, db_path: str = "email_storage.db"):
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        self.lock = threading.RLock()
        
        # 创建数据库和表
        self._initialize_database()
    
    def _initialize_database(self):
        """初始化数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("PRAGMA foreign_keys = ON")
                conn.execute("PRAGMA journal_mode = WAL")
                conn.execute("PRAGMA synchronous = NORMAL")
                
                # 创建账户表
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS accounts (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        account_id TEXT UNIQUE NOT NULL,
                        email TEXT NOT NULL,
                        display_name TEXT,
                        server TEXT,
                        port INTEGER DEFAULT 993,
                        last_sync_time TIMESTAMP,
                        total_emails INTEGER DEFAULT 0,
                        total_size INTEGER DEFAULT 0,
                        is_active BOOLEAN DEFAULT 1,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 创建文件夹表
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS folders (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        account_id TEXT NOT NULL,
                        folder_name TEXT NOT NULL,
                        display_name TEXT,
                        folder_type TEXT,
                        total_messages INTEGER DEFAULT 0,
                        unread_messages INTEGER DEFAULT 0,
                        last_sync_uid INTEGER DEFAULT 0,
                        last_sync_modseq INTEGER DEFAULT 0,
                        last_sync_time TIMESTAMP,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (account_id) REFERENCES accounts (account_id),
                        UNIQUE (account_id, folder_name)
                    )
                """)
                
                # 创建邮件表
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS emails (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        account_id TEXT NOT NULL,
                        folder_name TEXT NOT NULL,
                        message_id TEXT NOT NULL,
                        uid INTEGER NOT NULL,
                        subject TEXT,
                        sender TEXT,
                        recipients TEXT,  -- JSON
                        date_sent TIMESTAMP,
                        date_received TIMESTAMP,
                        size INTEGER DEFAULT 0,
                        flags TEXT,  -- JSON
                        text_body TEXT,
                        html_body TEXT,
                        attachments TEXT,  -- JSON
                        content_hash TEXT,
                        raw_content BLOB,
                        is_deleted BOOLEAN DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (account_id) REFERENCES accounts (account_id),
                        UNIQUE (account_id, folder_name, uid)
                    )
                """)

                # 创建账户同步状态表
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS account_sync_states (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        account_id TEXT UNIQUE NOT NULL,
                        is_first_sync BOOLEAN DEFAULT 1,
                        last_full_sync_time TIMESTAMP,
                        last_incremental_sync_time TIMESTAMP,
                        max_uid_synced INTEGER DEFAULT 0,
                        total_emails_synced INTEGER DEFAULT 0,
                        sync_strategy TEXT DEFAULT 'auto',
                        first_sync_limit INTEGER DEFAULT 100,
                        incremental_days_back INTEGER DEFAULT 7,
                        force_full_sync_days INTEGER DEFAULT 30,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (account_id) REFERENCES accounts (account_id)
                    )
                """)

                # 创建索引
                self._create_indexes(conn)
                
                conn.commit()
                self.logger.info("数据库初始化完成")
                
        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")
            raise
    
    def _create_indexes(self, conn):
        """创建索引"""
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_emails_account_folder ON emails (account_id, folder_name)",
            "CREATE INDEX IF NOT EXISTS idx_emails_uid ON emails (uid)",
            "CREATE INDEX IF NOT EXISTS idx_emails_message_id ON emails (message_id)",
            "CREATE INDEX IF NOT EXISTS idx_emails_sender ON emails (sender)",
            "CREATE INDEX IF NOT EXISTS idx_emails_date_sent ON emails (date_sent)",
            "CREATE INDEX IF NOT EXISTS idx_emails_subject ON emails (subject)",
            "CREATE INDEX IF NOT EXISTS idx_emails_content_hash ON emails (content_hash)",
            "CREATE INDEX IF NOT EXISTS idx_folders_account ON folders (account_id)",
            "CREATE INDEX IF NOT EXISTS idx_accounts_email ON accounts (email)",
        ]
        
        for index_sql in indexes:
            try:
                conn.execute(index_sql)
            except Exception as e:
                self.logger.warning(f"创建索引失败: {e}")
    
    def insert_account(self, account: AccountRecord) -> bool:
        """插入账户记录"""
        try:
            with self.lock:
                with sqlite3.connect(self.db_path) as conn:
                    account.created_at = datetime.now(timezone.utc)
                    account.updated_at = account.created_at
                    
                    conn.execute("""
                        INSERT OR REPLACE INTO accounts 
                        (account_id, email, display_name, server, port, last_sync_time, 
                         total_emails, total_size, is_active, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        account.account_id, account.email, account.display_name,
                        account.server, account.port, account.last_sync_time,
                        account.total_emails, account.total_size, account.is_active,
                        account.created_at, account.updated_at
                    ))
                    
                    conn.commit()
                    return True
                    
        except Exception as e:
            self.logger.error(f"插入账户记录失败: {e}")
            return False
    
    def insert_folder(self, folder: FolderRecord) -> bool:
        """插入文件夹记录"""
        try:
            with self.lock:
                with sqlite3.connect(self.db_path) as conn:
                    folder.created_at = datetime.now(timezone.utc)
                    folder.updated_at = folder.created_at
                    
                    conn.execute("""
                        INSERT OR REPLACE INTO folders 
                        (account_id, folder_name, display_name, folder_type, total_messages,
                         unread_messages, last_sync_uid, last_sync_modseq, last_sync_time,
                         created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        folder.account_id, folder.folder_name, folder.display_name,
                        folder.folder_type, folder.total_messages, folder.unread_messages,
                        folder.last_sync_uid, folder.last_sync_modseq, folder.last_sync_time,
                        folder.created_at, folder.updated_at
                    ))
                    
                    conn.commit()
                    return True
                    
        except Exception as e:
            self.logger.error(f"插入文件夹记录失败: {e}")
            return False
    
    def insert_email(self, email: EmailRecord) -> bool:
        """插入邮件记录"""
        try:
            with self.lock:
                with sqlite3.connect(self.db_path) as conn:
                    email.created_at = datetime.now(timezone.utc)
                    email.updated_at = email.created_at
                    
                    conn.execute("""
                        INSERT OR REPLACE INTO emails 
                        (account_id, folder_name, message_id, uid, subject, sender, recipients,
                         date_sent, date_received, size, flags, text_body, html_body, 
                         attachments, content_hash, raw_content, is_deleted, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        email.account_id, email.folder_name, email.message_id, email.uid,
                        email.subject, email.sender, email.recipients, email.date_sent,
                        email.date_received, email.size, email.flags, email.text_body,
                        email.html_body, email.attachments, email.content_hash,
                        email.raw_content, email.is_deleted, email.created_at, email.updated_at
                    ))
                    
                    conn.commit()
                    return True
                    
        except Exception as e:
            self.logger.error(f"插入邮件记录失败: {e}")
            return False

    def save_email(self, email: EmailRecord) -> Optional[int]:
        """保存邮件记录并返回ID"""
        try:
            with self.lock:
                with sqlite3.connect(self.db_path) as conn:
                    email.created_at = datetime.now(timezone.utc)
                    email.updated_at = email.created_at

                    cursor = conn.execute("""
                        INSERT OR REPLACE INTO emails
                        (account_id, folder_name, message_id, uid, subject, sender, recipients,
                         date_sent, date_received, size, flags, text_body, html_body,
                         attachments, content_hash, raw_content, is_deleted, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        email.account_id, email.folder_name, email.message_id, email.uid,
                        email.subject, email.sender, email.recipients, email.date_sent,
                        email.date_received, email.size, email.flags, email.text_body,
                        email.html_body, email.attachments, email.content_hash,
                        email.raw_content, email.is_deleted, email.created_at, email.updated_at
                    ))

                    email_id = cursor.lastrowid
                    conn.commit()
                    return email_id

        except Exception as e:
            self.logger.error(f"保存邮件记录失败: {e}")
            return None
    
    def batch_insert_emails(self, emails: List[EmailRecord]) -> int:
        """批量插入邮件记录"""
        try:
            with self.lock:
                with sqlite3.connect(self.db_path) as conn:
                    current_time = datetime.now(timezone.utc)
                    
                    email_data = []
                    for email in emails:
                        email.created_at = current_time
                        email.updated_at = current_time
                        
                        email_data.append((
                            email.account_id, email.folder_name, email.message_id, email.uid,
                            email.subject, email.sender, email.recipients, email.date_sent,
                            email.date_received, email.size, email.flags, email.text_body,
                            email.html_body, email.attachments, email.content_hash,
                            email.raw_content, email.is_deleted, email.created_at, email.updated_at
                        ))
                    
                    conn.executemany("""
                        INSERT OR REPLACE INTO emails 
                        (account_id, folder_name, message_id, uid, subject, sender, recipients,
                         date_sent, date_received, size, flags, text_body, html_body, 
                         attachments, content_hash, raw_content, is_deleted, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, email_data)
                    
                    conn.commit()
                    return len(emails)
                    
        except Exception as e:
            self.logger.error(f"批量插入邮件记录失败: {e}")
            return 0
    
    def get_emails_by_folder(self, account_id: str, folder_name: str, 
                           limit: int = 100, offset: int = 0) -> List[EmailRecord]:
        """根据文件夹获取邮件"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                
                cursor = conn.execute("""
                    SELECT * FROM emails 
                    WHERE account_id = ? AND folder_name = ? AND is_deleted = 0
                    ORDER BY date_sent DESC
                    LIMIT ? OFFSET ?
                """, (account_id, folder_name, limit, offset))
                
                emails = []
                for row in cursor.fetchall():
                    email = EmailRecord(**dict(row))
                    emails.append(email)
                
                return emails
                
        except Exception as e:
            self.logger.error(f"获取文件夹邮件失败: {e}")
            return []

    def get_emails_by_account(self, account_id: str, folder_name: str = None,
                             limit: int = 100, offset: int = 0) -> List[EmailRecord]:
        """根据账户获取邮件"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row

                if folder_name:
                    # 获取指定文件夹的邮件
                    cursor = conn.execute("""
                        SELECT * FROM emails
                        WHERE account_id = ? AND folder_name = ? AND is_deleted = 0
                        ORDER BY date_sent DESC
                        LIMIT ? OFFSET ?
                    """, (account_id, folder_name, limit, offset))
                else:
                    # 获取账户下所有邮件
                    cursor = conn.execute("""
                        SELECT * FROM emails
                        WHERE account_id = ? AND is_deleted = 0
                        ORDER BY date_sent DESC
                        LIMIT ? OFFSET ?
                    """, (account_id, limit, offset))

                emails = []
                for row in cursor.fetchall():
                    email = EmailRecord(**dict(row))
                    emails.append(email)

                return emails

        except Exception as e:
            self.logger.error(f"获取账户邮件失败: {e}")
            return []

    def get_email_by_message_id(self, account_id: str, message_id: str) -> Optional[EmailRecord]:
        """根据消息ID获取邮件"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row

                cursor = conn.execute("""
                    SELECT * FROM emails
                    WHERE account_id = ? AND message_id = ? AND is_deleted = 0
                    LIMIT 1
                """, (account_id, message_id))

                row = cursor.fetchone()
                if row:
                    return EmailRecord(**dict(row))
                else:
                    return None

        except Exception as e:
            self.logger.error(f"根据消息ID获取邮件失败: {e}")
            return None
    
    def get_last_sync_info(self, account_id: str, folder_name: str) -> Tuple[int, int]:
        """获取最后同步信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT last_sync_uid, last_sync_modseq FROM folders
                    WHERE account_id = ? AND folder_name = ?
                """, (account_id, folder_name))
                
                row = cursor.fetchone()
                if row:
                    return row[0] or 0, row[1] or 0
                else:
                    return 0, 0
                    
        except Exception as e:
            self.logger.error(f"获取同步信息失败: {e}")
            return 0, 0
    
    def update_sync_info(self, account_id: str, folder_name: str, 
                        last_uid: int, last_modseq: int = 0) -> bool:
        """更新同步信息"""
        try:
            with self.lock:
                with sqlite3.connect(self.db_path) as conn:
                    conn.execute("""
                        UPDATE folders 
                        SET last_sync_uid = ?, last_sync_modseq = ?, 
                            last_sync_time = ?, updated_at = ?
                        WHERE account_id = ? AND folder_name = ?
                    """, (
                        last_uid, last_modseq, datetime.now(timezone.utc),
                        datetime.now(timezone.utc), account_id, folder_name
                    ))
                    
                    conn.commit()
                    return True
                    
        except Exception as e:
            self.logger.error(f"更新同步信息失败: {e}")
            return False

    # ========================================
    # 📈 智能增量同步相关方法
    # ========================================

    def get_account_sync_state(self, account_id: str) -> Optional[AccountSyncState]:
        """获取账户同步状态"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT id, account_id, is_first_sync, last_full_sync_time,
                           last_incremental_sync_time, max_uid_synced, total_emails_synced,
                           sync_strategy, first_sync_limit, incremental_days_back,
                           force_full_sync_days, created_at, updated_at
                    FROM account_sync_states
                    WHERE account_id = ?
                """, (account_id,))

                row = cursor.fetchone()
                if row:
                    # 安全的时区处理
                    def safe_parse_datetime(dt_str):
                        if not dt_str:
                            return None
                        dt = datetime.fromisoformat(dt_str)
                        # 如果没有时区信息，假设为UTC
                        if dt.tzinfo is None:
                            dt = dt.replace(tzinfo=timezone.utc)
                        return dt

                    return AccountSyncState(
                        id=row[0],
                        account_id=row[1],
                        is_first_sync=bool(row[2]),
                        last_full_sync_time=safe_parse_datetime(row[3]),
                        last_incremental_sync_time=safe_parse_datetime(row[4]),
                        max_uid_synced=row[5],
                        total_emails_synced=row[6],
                        sync_strategy=row[7],
                        first_sync_limit=row[8],
                        incremental_days_back=row[9],
                        force_full_sync_days=row[10],
                        created_at=safe_parse_datetime(row[11]),
                        updated_at=safe_parse_datetime(row[12])
                    )
                else:
                    # 如果不存在，创建默认状态
                    return self._create_default_sync_state(account_id)

        except Exception as e:
            self.logger.error(f"获取账户同步状态失败: {e}")
            return None

    def _create_default_sync_state(self, account_id: str) -> AccountSyncState:
        """创建默认同步状态"""
        try:
            sync_state = AccountSyncState(
                account_id=account_id,
                is_first_sync=True,
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc)
            )

            # 保存到数据库
            if self.save_account_sync_state(sync_state):
                return sync_state
            else:
                return None

        except Exception as e:
            self.logger.error(f"创建默认同步状态失败: {e}")
            return None

    def save_account_sync_state(self, sync_state: AccountSyncState) -> bool:
        """保存账户同步状态"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                if sync_state.id:
                    # 更新现有记录
                    conn.execute("""
                        UPDATE account_sync_states
                        SET is_first_sync = ?, last_full_sync_time = ?,
                            last_incremental_sync_time = ?, max_uid_synced = ?,
                            total_emails_synced = ?, sync_strategy = ?,
                            first_sync_limit = ?, incremental_days_back = ?,
                            force_full_sync_days = ?, updated_at = ?
                        WHERE id = ?
                    """, (
                        sync_state.is_first_sync,
                        sync_state.last_full_sync_time.isoformat() if sync_state.last_full_sync_time else None,
                        sync_state.last_incremental_sync_time.isoformat() if sync_state.last_incremental_sync_time else None,
                        sync_state.max_uid_synced,
                        sync_state.total_emails_synced,
                        sync_state.sync_strategy,
                        sync_state.first_sync_limit,
                        sync_state.incremental_days_back,
                        sync_state.force_full_sync_days,
                        datetime.now(timezone.utc).isoformat(),
                        sync_state.id
                    ))
                else:
                    # 插入新记录
                    conn.execute("""
                        INSERT OR REPLACE INTO account_sync_states
                        (account_id, is_first_sync, last_full_sync_time,
                         last_incremental_sync_time, max_uid_synced, total_emails_synced,
                         sync_strategy, first_sync_limit, incremental_days_back,
                         force_full_sync_days, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        sync_state.account_id,
                        sync_state.is_first_sync,
                        sync_state.last_full_sync_time.isoformat() if sync_state.last_full_sync_time else None,
                        sync_state.last_incremental_sync_time.isoformat() if sync_state.last_incremental_sync_time else None,
                        sync_state.max_uid_synced,
                        sync_state.total_emails_synced,
                        sync_state.sync_strategy,
                        sync_state.first_sync_limit,
                        sync_state.incremental_days_back,
                        sync_state.force_full_sync_days,
                        sync_state.created_at.isoformat() if sync_state.created_at else datetime.now(timezone.utc).isoformat(),
                        datetime.now(timezone.utc).isoformat()
                    ))

                conn.commit()
                return True

        except Exception as e:
            self.logger.error(f"保存账户同步状态失败: {e}")
            return False

    def update_sync_state_after_sync(self, account_id: str, sync_type: str,
                                   new_emails_count: int, max_uid: int = 0) -> bool:
        """同步完成后更新状态"""
        try:
            sync_state = self.get_account_sync_state(account_id)
            if not sync_state:
                return False

            current_time = datetime.now(timezone.utc)

            # 更新基础信息
            sync_state.total_emails_synced += new_emails_count
            sync_state.updated_at = current_time

            if max_uid > sync_state.max_uid_synced:
                sync_state.max_uid_synced = max_uid

            # 根据同步类型更新相应字段
            if sync_type == "first_sync":
                sync_state.is_first_sync = False
                sync_state.last_full_sync_time = current_time
                sync_state.last_incremental_sync_time = current_time
            elif sync_type == "incremental":
                sync_state.last_incremental_sync_time = current_time
            elif sync_type == "full_sync":
                sync_state.last_full_sync_time = current_time
                sync_state.last_incremental_sync_time = current_time

            return self.save_account_sync_state(sync_state)

        except Exception as e:
            self.logger.error(f"更新同步状态失败: {e}")
            return False
    
    def search_emails(self, account_id: str, query: str, 
                     folder_name: str = None, limit: int = 100) -> List[EmailRecord]:
        """搜索邮件"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                
                sql = """
                    SELECT * FROM emails 
                    WHERE account_id = ? AND is_deleted = 0
                    AND (subject LIKE ? OR sender LIKE ? OR text_body LIKE ?)
                """
                params = [account_id, f"%{query}%", f"%{query}%", f"%{query}%"]
                
                if folder_name:
                    sql += " AND folder_name = ?"
                    params.append(folder_name)
                
                sql += " ORDER BY date_sent DESC LIMIT ?"
                params.append(limit)
                
                cursor = conn.execute(sql, params)
                
                emails = []
                for row in cursor.fetchall():
                    email = EmailRecord(**dict(row))
                    emails.append(email)
                
                return emails
                
        except Exception as e:
            self.logger.error(f"搜索邮件失败: {e}")
            return []
    
    def get_database_statistics(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                stats = {}
                
                # 账户统计
                cursor = conn.execute("SELECT COUNT(*) FROM accounts WHERE is_active = 1")
                stats['active_accounts'] = cursor.fetchone()[0]
                
                # 文件夹统计
                cursor = conn.execute("SELECT COUNT(*) FROM folders")
                stats['total_folders'] = cursor.fetchone()[0]
                
                # 邮件统计
                cursor = conn.execute("SELECT COUNT(*) FROM emails WHERE is_deleted = 0")
                stats['total_emails'] = cursor.fetchone()[0]
                
                cursor = conn.execute("SELECT SUM(size) FROM emails WHERE is_deleted = 0")
                stats['total_size'] = cursor.fetchone()[0] or 0
                
                # 数据库文件大小
                db_path = Path(self.db_path)
                if db_path.exists():
                    stats['db_file_size'] = db_path.stat().st_size
                else:
                    stats['db_file_size'] = 0
                
                return stats
                
        except Exception as e:
            self.logger.error(f"获取数据库统计失败: {e}")
            return {}

def main():
    """测试函数"""
    print("🗄️ 邮件数据库测试")
    print("=" * 50)
    
    # 创建数据库
    db = EmailDatabase("test_email.db")
    
    # 测试账户插入
    account = AccountRecord(
        account_id="test_account_1",
        email="<EMAIL>",
        display_name="Test User",
        server="imap.example.com"
    )
    
    success = db.insert_account(account)
    print(f"插入账户: {'成功' if success else '失败'}")
    
    # 测试文件夹插入
    folder = FolderRecord(
        account_id="test_account_1",
        folder_name="INBOX",
        display_name="收件箱",
        folder_type="inbox"
    )
    
    success = db.insert_folder(folder)
    print(f"插入文件夹: {'成功' if success else '失败'}")
    
    # 获取统计信息
    stats = db.get_database_statistics()
    print(f"\n数据库统计:")
    for key, value in stats.items():
        print(f"  {key}: {value}")

# 在EmailDatabase类中添加新的邮件操作方法
def add_email_operations_to_database():
    """为EmailDatabase类添加邮件操作方法"""

    def update_email_flags(self, email_id: int, flags: str) -> bool:
        """更新邮件标记"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    UPDATE emails SET flags = ?, updated_at = ?
                    WHERE id = ?
                """, (flags, datetime.now(timezone.utc), email_id))

                return cursor.rowcount > 0

        except Exception as e:
            self.logger.error(f"更新邮件标记失败: {e}")
            return False

    def soft_delete_email(self, email_id: int) -> bool:
        """软删除邮件（标记为已删除）"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    UPDATE emails SET is_deleted = 1, updated_at = ?
                    WHERE id = ?
                """, (datetime.now(timezone.utc), email_id))

                return cursor.rowcount > 0

        except Exception as e:
            self.logger.error(f"软删除邮件失败: {e}")
            return False

    def delete_emails_by_account(self, account_id: str) -> int:
        """删除指定账户的所有邮件数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # 物理删除该账户的所有邮件
                cursor = conn.execute("""
                    DELETE FROM emails WHERE account_id = ?
                """, (account_id,))

                deleted_count = cursor.rowcount
                conn.commit()

                self.logger.info(f"删除账户 {account_id} 的 {deleted_count} 封邮件")
                return deleted_count

        except Exception as e:
            self.logger.error(f"删除账户邮件失败: {e}")
            return 0

    def count_emails_by_account(self, account_id: str) -> int:
        """统计指定账户的邮件数量"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT COUNT(*) FROM emails
                    WHERE account_id = ? AND (is_deleted IS NULL OR is_deleted = 0)
                """, (account_id,))

                count = cursor.fetchone()[0]
                return count

        except Exception as e:
            self.logger.error(f"统计账户邮件失败: {e}")
            return 0

    def update_email_folder(self, email_id: int, folder_name: str) -> bool:
        """更新邮件文件夹"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    UPDATE emails SET folder_name = ?, updated_at = ?
                    WHERE id = ?
                """, (folder_name, datetime.now(timezone.utc), email_id))

                return cursor.rowcount > 0

        except Exception as e:
            self.logger.error(f"更新邮件文件夹失败: {e}")
            return False

    def get_folders_by_account(self, account_id: str) -> List[str]:
        """获取账户的所有文件夹"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT DISTINCT folder_name FROM emails
                    WHERE account_id = ? AND is_deleted = 0
                    ORDER BY folder_name
                """, (account_id,))

                folders = [row[0] for row in cursor.fetchall()]
                return folders

        except Exception as e:
            self.logger.error(f"获取账户文件夹失败: {e}")
            return []

    def get_folder_email_count(self, account_id: str, folder_name: str) -> int:
        """获取文件夹中的邮件数量"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT COUNT(*) FROM emails
                    WHERE account_id = ? AND folder_name = ? AND is_deleted = 0
                """, (account_id, folder_name))

                count = cursor.fetchone()[0]
                return count

        except Exception as e:
            self.logger.error(f"获取文件夹邮件数量失败: {e}")
            return 0

    def advanced_search(self, account_id: str, criteria: dict) -> List[EmailRecord]:
        """高级搜索邮件"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row

                # 构建查询条件
                where_conditions = ["account_id = ?", "is_deleted = 0"]
                params = [account_id]

                # 基本搜索
                if 'quick_search' in criteria:
                    search_text = criteria['quick_search']
                    scope = criteria.get('search_scope', '全部内容')

                    if scope == '全部内容':
                        where_conditions.append(
                            "(subject LIKE ? COLLATE NOCASE OR sender LIKE ? COLLATE NOCASE OR text_body LIKE ? COLLATE NOCASE OR html_body LIKE ? COLLATE NOCASE)"
                        )
                        search_param = f"%{search_text}%"
                        params.extend([search_param, search_param, search_param, search_param])
                    elif scope == '仅主题':
                        where_conditions.append("subject LIKE ? COLLATE NOCASE")
                        params.append(f"%{search_text}%")
                    elif scope == '仅发件人':
                        where_conditions.append("sender LIKE ? COLLATE NOCASE")
                        params.append(f"%{search_text}%")
                    elif scope == '仅内容':
                        where_conditions.append("(text_body LIKE ? COLLATE NOCASE OR html_body LIKE ? COLLATE NOCASE)")
                        search_param = f"%{search_text}%"
                        params.extend([search_param, search_param])

                # 高级搜索条件
                if 'subject' in criteria:
                    where_conditions.append("subject LIKE ? COLLATE NOCASE")
                    params.append(f"%{criteria['subject']}%")

                if 'sender' in criteria:
                    where_conditions.append("sender LIKE ? COLLATE NOCASE")
                    params.append(f"%{criteria['sender']}%")

                if 'recipient' in criteria:
                    where_conditions.append("recipients LIKE ? COLLATE NOCASE")
                    params.append(f"%{criteria['recipient']}%")

                if 'content' in criteria:
                    where_conditions.append("(text_body LIKE ? COLLATE NOCASE OR html_body LIKE ? COLLATE NOCASE)")
                    content_param = f"%{criteria['content']}%"
                    params.extend([content_param, content_param])

                # 阅读状态
                if 'read_status' in criteria:
                    if criteria['read_status'] == '仅未读':
                        where_conditions.append("(flags IS NULL OR flags NOT LIKE '%\\Seen%')")
                    elif criteria['read_status'] == '仅已读':
                        where_conditions.append("flags LIKE '%\\Seen%'")

                # 时间范围
                if 'time_range' in criteria:
                    time_range = criteria['time_range']
                    if time_range == '今天':
                        where_conditions.append("DATE(date_sent) = DATE('now')")
                    elif time_range == '昨天':
                        where_conditions.append("DATE(date_sent) = DATE('now', '-1 day')")
                    elif time_range == '本周':
                        where_conditions.append("date_sent >= DATE('now', 'weekday 0', '-7 days')")
                    elif time_range == '本月':
                        where_conditions.append("date_sent >= DATE('now', 'start of month')")
                    elif time_range == '自定义':
                        if 'start_date' in criteria:
                            where_conditions.append("DATE(date_sent) >= ?")
                            params.append(criteria['start_date'].strftime('%Y-%m-%d'))
                        if 'end_date' in criteria:
                            where_conditions.append("DATE(date_sent) <= ?")
                            params.append(criteria['end_date'].strftime('%Y-%m-%d'))

                # 邮件大小
                if 'size_min' in criteria and criteria['size_min'] > 0:
                    where_conditions.append("size >= ?")
                    params.append(criteria['size_min'] * 1024)  # 转换为字节

                if 'size_max' in criteria and criteria['size_max'] < 999999:
                    where_conditions.append("size <= ?")
                    params.append(criteria['size_max'] * 1024)  # 转换为字节

                # 构建完整查询
                query = f"""
                    SELECT * FROM emails
                    WHERE {' AND '.join(where_conditions)}
                    ORDER BY date_sent DESC
                    LIMIT 1000
                """

                cursor = conn.execute(query, params)

                emails = []
                for row in cursor.fetchall():
                    email = EmailRecord(**dict(row))
                    emails.append(email)

                return emails

        except Exception as e:
            self.logger.error(f"高级搜索失败: {e}")
            return []

    # 动态添加方法到EmailDatabase类
    EmailDatabase.update_email_flags = update_email_flags
    EmailDatabase.soft_delete_email = soft_delete_email
    EmailDatabase.update_email_folder = update_email_folder
    EmailDatabase.get_folders_by_account = get_folders_by_account
    EmailDatabase.get_folder_email_count = get_folder_email_count
    EmailDatabase.advanced_search = advanced_search

# 执行方法添加
add_email_operations_to_database()

def add_delete_by_account_method():
    """添加按账户删除邮件的方法"""
    def delete_emails_by_account(self, account_id: str) -> int:
        """删除指定账户的所有邮件数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # 物理删除该账户的所有邮件
                cursor = conn.execute("""
                    DELETE FROM emails WHERE account_id = ?
                """, (account_id,))

                deleted_count = cursor.rowcount
                conn.commit()

                self.logger.info(f"删除账户 {account_id} 的 {deleted_count} 封邮件")
                return deleted_count

        except Exception as e:
            self.logger.error(f"删除账户邮件失败: {e}")
            return 0

    # 动态添加方法到EmailDatabase类
    EmailDatabase.delete_emails_by_account = delete_emails_by_account

# 执行方法添加
add_delete_by_account_method()

def add_count_by_account_method():
    """添加按账户统计邮件的方法"""
    def count_emails_by_account(self, account_id: str) -> int:
        """统计指定账户的邮件数量"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT COUNT(*) FROM emails
                    WHERE account_id = ? AND (is_deleted IS NULL OR is_deleted = 0)
                """, (account_id,))

                count = cursor.fetchone()[0]
                return count

        except Exception as e:
            self.logger.error(f"统计账户邮件失败: {e}")
            return 0

    # 动态添加方法到EmailDatabase类
    EmailDatabase.count_emails_by_account = count_emails_by_account

# 执行方法添加
add_count_by_account_method()

if __name__ == "__main__":
    main()
