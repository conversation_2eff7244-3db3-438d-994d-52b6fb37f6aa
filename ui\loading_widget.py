"""
加载状态管理组件
提供统一的加载动画、进度提示和按钮控制功能
"""

import logging
from typing import List, Optional, Callable
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QProgressBar, QFrame, QGraphicsOpacityEffect
)
from PySide6.QtCore import (
    QTimer, QPropertyAnimation, QEasingCurve, Signal,
    QRect, QPoint, QSize, Qt
)
from PySide6.QtGui import QMovie, QPixmap, QPainter, QColor, QFont


class LoadingSpinner(QLabel):
    """旋转加载动画组件"""
    
    def __init__(self, size: int = 32, parent=None):
        super().__init__(parent)
        self.size = size
        self.angle = 0
        self.timer = QTimer()
        self.timer.timeout.connect(self.rotate)
        self.setFixedSize(size, size)
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
    def start_animation(self):
        """开始旋转动画"""
        self.timer.start(50)  # 每50ms旋转一次
        
    def stop_animation(self):
        """停止旋转动画"""
        self.timer.stop()
        self.angle = 0
        self.update()
        
    def rotate(self):
        """旋转动画"""
        self.angle = (self.angle + 10) % 360
        self.update()
        
    def paintEvent(self, event):
        """绘制旋转的加载图标"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 设置画笔
        painter.setPen(QColor(52, 152, 219))  # 蓝色
        painter.setBrush(QColor(52, 152, 219, 100))  # 半透明蓝色
        
        # 移动到中心点
        center = QPoint(self.size // 2, self.size // 2)
        painter.translate(center)
        painter.rotate(self.angle)
        
        # 绘制旋转的圆弧
        radius = self.size // 3
        for i in range(8):
            alpha = 255 - (i * 30)
            painter.setPen(QColor(52, 152, 219, alpha))
            painter.drawLine(0, -radius, 0, -radius + 6)
            painter.rotate(45)


class LoadingOverlay(QWidget):
    """加载遮罩层组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        # 设置为覆盖整个父组件
        self.setStyleSheet("""
            LoadingOverlay {
                background-color: rgba(255, 255, 255, 200);
                border-radius: 8px;
            }
        """)
        # 确保遮罩层能够覆盖整个父组件
        if parent:
            self.setGeometry(parent.rect())
        self.setup_ui()
        self.hide()
        
    def setup_ui(self):
        """设置UI"""
        # 创建主布局，确保内容居中
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 创建居中容器
        center_widget = QWidget()
        center_widget.setFixedSize(300, 200)  # 固定大小的居中容器
        center_widget.setStyleSheet("""
            QWidget {
                background-color: rgba(255, 255, 255, 230);
                border-radius: 12px;
                border: 1px solid rgba(200, 200, 200, 100);
            }
        """)

        # 将居中容器添加到主布局的中心
        main_layout.addStretch()
        main_layout.addWidget(center_widget, alignment=Qt.AlignmentFlag.AlignCenter)
        main_layout.addStretch()

        # 在居中容器内创建内容布局
        content_layout = QVBoxLayout(center_widget)
        content_layout.setSpacing(15)
        content_layout.setContentsMargins(30, 30, 30, 30)

        # 加载动画
        self.spinner = LoadingSpinner(48)
        content_layout.addWidget(self.spinner, alignment=Qt.AlignmentFlag.AlignCenter)

        # 状态文本
        self.status_label = QLabel("正在处理...")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.status_label.setWordWrap(True)  # 允许文本换行
        self.status_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                font-size: 14px;
                font-weight: bold;
                background: transparent;
                padding: 5px;
            }
        """)
        content_layout.addWidget(self.status_label)

        # 进度条（可选）
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 0)  # 无限进度条
        self.progress_bar.setFixedHeight(20)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                text-align: center;
                background: transparent;
                font-size: 12px;
                color: #2c3e50;
            }
            QProgressBar::chunk {
                background-color: #3498db;
                border-radius: 8px;
            }
        """)
        self.progress_bar.hide()
        content_layout.addWidget(self.progress_bar)
        
    def show_loading(self, message: str = "正在处理...", show_progress: bool = False):
        """显示加载状态"""
        self.status_label.setText(message)
        self.progress_bar.setVisible(show_progress)
        self.spinner.start_animation()
        self.show()
        self.raise_()
        
    def hide_loading(self):
        """隐藏加载状态"""
        self.spinner.stop_animation()
        self.hide()
        
    def update_message(self, message: str):
        """更新状态消息"""
        self.status_label.setText(message)
        
    def set_progress(self, value: int, maximum: int = 100):
        """设置进度值"""
        if not self.progress_bar.isVisible():
            self.progress_bar.show()
        self.progress_bar.setRange(0, maximum)
        self.progress_bar.setValue(value)


class LoadingManager:
    """加载状态管理器"""

    def __init__(self, parent_widget: QWidget):
        self.parent_widget = parent_widget
        self.overlay = LoadingOverlay(parent_widget)
        self.success_animation = SuccessAnimation(parent_widget)
        self.error_animation = ErrorAnimation(parent_widget)
        self.disabled_buttons: List[QPushButton] = []
        self.original_button_states: dict = {}

        # 监听父组件大小变化
        parent_widget.resizeEvent = self._on_parent_resize
        
    def _on_parent_resize(self, event):
        """父组件大小变化时调整遮罩层大小"""
        if hasattr(self.parent_widget, 'resizeEvent'):
            # 调用原始的resizeEvent
            original_resize = getattr(self.parent_widget.__class__, 'resizeEvent', None)
            if original_resize:
                original_resize(self.parent_widget, event)

        # 调整遮罩层大小和位置，确保覆盖整个父组件
        self._update_overlay_geometry()

    def _update_overlay_geometry(self):
        """更新遮罩层几何形状"""
        if self.parent_widget:
            # 获取父组件的客户区域
            parent_rect = self.parent_widget.rect()

            # 设置遮罩层覆盖整个父组件
            self.overlay.setGeometry(parent_rect)
            self.success_animation.setGeometry(parent_rect)
            self.error_animation.setGeometry(parent_rect)

            # 确保遮罩层在最顶层
            self.overlay.raise_()
            self.success_animation.raise_()
            self.error_animation.raise_()
        
    def show_loading(self, message: str = "正在处理...",
                    buttons_to_disable: Optional[List[QPushButton]] = None,
                    show_progress: bool = False):
        """显示加载状态"""
        # 禁用指定按钮
        if buttons_to_disable:
            self.disabled_buttons = buttons_to_disable
            self.original_button_states = {}
            for button in buttons_to_disable:
                self.original_button_states[button] = button.isEnabled()
                button.setEnabled(False)

        # 更新遮罩层几何形状
        self._update_overlay_geometry()

        # 显示加载遮罩
        self.overlay.show_loading(message, show_progress)

    def hide_loading(self):
        """隐藏加载状态"""
        try:
            # 恢复按钮状态
            for button in self.disabled_buttons:
                if button in self.original_button_states:
                    button.setEnabled(self.original_button_states[button])

            self.disabled_buttons.clear()
            self.original_button_states.clear()

            # 隐藏加载遮罩
            self.overlay.hide_loading()
        except Exception:
            # 忽略隐藏时的异常
            pass

    def update_message(self, message: str):
        """更新加载消息"""
        self.overlay.update_message(message)

    def set_progress(self, value: int, maximum: int = 100):
        """设置进度"""
        self.overlay.set_progress(value, maximum)

    def show_success(self, message: str = "操作成功！", duration: int = 2000):
        """显示成功反馈"""
        self.hide_loading()
        self.success_animation.setGeometry(self.parent_widget.rect())
        self.success_animation.show_success(message, duration)

    def show_error(self, message: str = "操作失败！", duration: int = 3000):
        """显示错误反馈"""
        self.hide_loading()
        self.error_animation.setGeometry(self.parent_widget.rect())
        self.error_animation.show_error(message, duration)


class ProgressStepManager:
    """进度步骤管理器"""

    def __init__(self, loading_manager: LoadingManager):
        self.loading_manager = loading_manager
        self.steps = []
        self.current_step = 0
        self.is_running = False

    def set_steps(self, steps: List[str]):
        """设置进度步骤"""
        self.steps = steps
        self.current_step = 0

    def start_progress(self, buttons_to_disable: Optional[List[QPushButton]] = None):
        """开始进度"""
        if not self.steps:
            return

        self.is_running = True
        self.current_step = 0
        message = f"步骤 {self.current_step + 1}/{len(self.steps)}: {self.steps[self.current_step]}"
        self.loading_manager.show_loading(
            message,
            buttons_to_disable,
            show_progress=True
        )
        self.loading_manager.set_progress(0, len(self.steps))

    def next_step(self) -> bool:
        """进入下一步"""
        if not self.is_running:
            return False

        if self.current_step < len(self.steps) - 1:
            self.current_step += 1
            message = f"步骤 {self.current_step + 1}/{len(self.steps)}: {self.steps[self.current_step]}"
            self.loading_manager.update_message(message)
            self.loading_manager.set_progress(self.current_step, len(self.steps))
            return True
        return False

    def complete_progress(self, success_message: str = "操作完成！"):
        """完成进度"""
        if not self.is_running:
            return

        self.is_running = False
        self.loading_manager.show_success(success_message)

    def cancel_progress(self):
        """取消进度"""
        self.is_running = False
        self._safe_hide_loading()

    def error_progress(self, error_message: str = "操作失败！"):
        """错误处理"""
        if not self.is_running:
            return

        self.is_running = False
        self.loading_manager.show_error(error_message)

    def _safe_hide_loading(self):
        """安全隐藏加载状态"""
        try:
            self.is_running = False
            self.loading_manager.hide_loading()
        except Exception:
            # 忽略隐藏时的异常
            pass


class SuccessAnimation(QWidget):
    """成功动画组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setStyleSheet("""
            QWidget {
                background-color: rgba(46, 204, 113, 230);
                border-radius: 8px;
            }
        """)
        self.setup_ui()
        self.hide()

    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(30, 30, 30, 30)

        # 成功图标
        self.success_label = QLabel("✅")
        self.success_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.success_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 48px;
                background: transparent;
            }
        """)
        layout.addWidget(self.success_label)

        # 成功消息
        self.message_label = QLabel("操作成功！")
        self.message_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.message_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 16px;
                font-weight: bold;
                background: transparent;
            }
        """)
        layout.addWidget(self.message_label)

    def show_success(self, message: str = "操作成功！", duration: int = 2000):
        """显示成功动画"""
        self.message_label.setText(message)
        self.show()
        self.raise_()

        # 自动隐藏
        QTimer.singleShot(duration, self.hide)


class ErrorAnimation(QWidget):
    """错误动画组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setStyleSheet("""
            QWidget {
                background-color: rgba(231, 76, 60, 230);
                border-radius: 8px;
            }
        """)
        self.setup_ui()
        self.hide()

    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(30, 30, 30, 30)

        # 错误图标
        self.error_label = QLabel("❌")
        self.error_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.error_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 48px;
                background: transparent;
            }
        """)
        layout.addWidget(self.error_label)

        # 错误消息
        self.message_label = QLabel("操作失败！")
        self.message_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.message_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 16px;
                font-weight: bold;
                background: transparent;
            }
        """)
        layout.addWidget(self.message_label)

    def show_error(self, message: str = "操作失败！", duration: int = 3000):
        """显示错误动画"""
        self.message_label.setText(message)
        self.show()
        self.raise_()

        # 自动隐藏
        QTimer.singleShot(duration, self.hide)
