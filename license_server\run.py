#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
许可证服务器启动脚本
"""

import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

from app import create_app

if __name__ == '__main__':
    # 获取环境配置
    config_name = os.environ.get('FLASK_ENV', 'development')
    
    # 创建应用
    app = create_app(config_name)
    
    # 启动服务器
    app.run(
        host=app.config['HOST'],
        port=app.config['PORT'],
        debug=app.config['DEBUG']
    )
