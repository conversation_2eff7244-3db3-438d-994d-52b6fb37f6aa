#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件撰写对话框
提供邮件撰写、回复、转发等功能
"""

import sys
import os
import mimetypes
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QGridLayout,
    QLabel, QLineEdit, QPushButton, QTextEdit, QComboBox, QCheckBox,
    QGroupBox, QSplitter, QProgressBar, QFileDialog, QMessageBox,
    QListWidget, QListWidgetItem, QTabWidget, QWidget, QFrame,
    QScrollArea, QToolBar, QToolButton, QMenu
)
from PySide6.QtCore import Qt, Signal, QThread, QTimer, QSize
from PySide6.QtGui import QFont, QPixmap, QIcon, QColor, QAction

# 尝试导入富文本编辑器
try:
    from PySide6.QtWebEngineWidgets import QWebEngineView
    RICH_EDITOR_AVAILABLE = True
except ImportError:
    RICH_EDITOR_AVAILABLE = False

class EmailSendThread(QThread):
    """邮件发送线程"""
    
    progress_updated = Signal(int)  # 进度更新信号
    send_finished = Signal(bool, str)  # 发送完成信号 (是否成功, 消息)
    
    def __init__(self, email_data, account_config):
        super().__init__()
        self.email_data = email_data
        self.account_config = account_config
    
    def run(self):
        """执行邮件发送"""
        try:
            from email_sender import MockEmailSender

            self.progress_updated.emit(10)

            # 创建邮件发送器
            sender = MockEmailSender()

            self.progress_updated.emit(30)

            # 发送邮件
            success = sender.send_email(self.email_data, self.account_config)

            self.progress_updated.emit(80)

            if success:
                self.progress_updated.emit(100)
                self.send_finished.emit(True, "邮件发送成功")
            else:
                self.send_finished.emit(False, "邮件发送失败")

        except Exception as e:
            self.send_finished.emit(False, f"发送失败: {e}")

class EmailComposeDialog(QDialog):
    """邮件撰写对话框"""
    
    # 邮件发送信号
    email_sent = Signal(dict)  # 发送邮件数据
    
    def __init__(self, parent=None, reply_to_email=None, forward_email=None, account_configs=None):
        super().__init__(parent)
        self.reply_to_email = reply_to_email
        self.forward_email = forward_email
        self.account_configs = account_configs or {}
        self.attachments = []
        self.send_thread = None
        
        # 设置对话框属性
        self.setWindowTitle(self.get_window_title())
        self.setModal(True)
        self.resize(900, 700)
        
        self.setup_ui()
        self.setup_connections()

        # 设置默认HTML格式
        self.on_format_changed("HTML")

        # 如果是回复或转发，填充内容
        if reply_to_email:
            self.fill_reply_content()
        elif forward_email:
            self.fill_forward_content()
    
    def get_window_title(self):
        """获取窗口标题"""
        if self.reply_to_email:
            return f"回复邮件 - {self.reply_to_email.subject or '(无主题)'}"
        elif self.forward_email:
            return f"转发邮件 - {self.forward_email.subject or '(无主题)'}"
        else:
            return "撰写新邮件"
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 工具栏
        self.create_toolbar(layout)
        
        # 邮件头部信息
        self.create_header_section(layout)
        
        # 主要内容区域
        self.create_content_section(layout)
        
        # 附件区域
        self.create_attachment_section(layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 底部按钮
        self.create_button_section(layout)
    
    def create_toolbar(self, parent_layout):
        """创建工具栏"""
        toolbar = QToolBar()
        toolbar.setToolButtonStyle(Qt.ToolButtonStyle.ToolButtonTextBesideIcon)
        
        # 发送按钮
        send_action = QAction("📤 发送", self)
        send_action.setShortcut("Ctrl+Return")
        send_action.triggered.connect(self.send_email)
        toolbar.addAction(send_action)
        
        toolbar.addSeparator()
        
        # 格式化按钮
        bold_action = QAction("𝐁", self)  # 使用粗体Unicode字符
        bold_action.setToolTip("粗体 (Ctrl+B)")
        bold_action.setShortcut("Ctrl+B")
        bold_action.triggered.connect(self.format_bold)
        toolbar.addAction(bold_action)

        italic_action = QAction("𝐼", self)  # 使用斜体Unicode字符
        italic_action.setToolTip("斜体 (Ctrl+I)")
        italic_action.setShortcut("Ctrl+I")
        italic_action.triggered.connect(self.format_italic)
        toolbar.addAction(italic_action)

        underline_action = QAction("U̲", self)  # 使用下划线Unicode字符
        underline_action.setToolTip("下划线 (Ctrl+U)")
        underline_action.setShortcut("Ctrl+U")
        underline_action.triggered.connect(self.format_underline)
        toolbar.addAction(underline_action)
        
        toolbar.addSeparator()

        # 字体大小选择
        self.font_size_combo = QComboBox()
        self.font_size_combo.addItems(["8", "9", "10", "11", "12", "14", "16", "18", "20", "24", "28", "32"])
        self.font_size_combo.setCurrentText("12")
        self.font_size_combo.setToolTip("字体大小")
        self.font_size_combo.currentTextChanged.connect(self.change_font_size)
        toolbar.addWidget(self.font_size_combo)

        # 字体颜色按钮
        color_action = QAction("🎨", self)
        color_action.setToolTip("字体颜色")
        color_action.triggered.connect(self.change_font_color)
        toolbar.addAction(color_action)

        toolbar.addSeparator()

        # 附件按钮
        attach_action = QAction("📎 附件", self)
        attach_action.triggered.connect(self.add_attachment)
        toolbar.addAction(attach_action)
        
        # 保存草稿按钮
        draft_action = QAction("💾 草稿", self)
        draft_action.triggered.connect(self.save_draft)
        toolbar.addAction(draft_action)
        
        parent_layout.addWidget(toolbar)
    
    def create_header_section(self, parent_layout):
        """创建邮件头部区域"""
        header_group = QGroupBox("邮件信息")
        header_layout = QFormLayout(header_group)
        
        # 发件账户选择
        self.from_combo = QComboBox()
        self.from_combo.setEditable(False)
        self.populate_from_accounts()
        header_layout.addRow("发件人:", self.from_combo)
        
        # 收件人
        self.to_edit = QLineEdit()
        self.to_edit.setPlaceholderText("输入收件人邮箱地址，多个地址用分号分隔")
        header_layout.addRow("收件人:", self.to_edit)
        
        # 抄送和密送
        cc_layout = QHBoxLayout()
        
        self.cc_edit = QLineEdit()
        self.cc_edit.setPlaceholderText("抄送地址")
        cc_layout.addWidget(QLabel("抄送:"))
        cc_layout.addWidget(self.cc_edit)
        
        self.bcc_edit = QLineEdit()
        self.bcc_edit.setPlaceholderText("密送地址")
        cc_layout.addWidget(QLabel("密送:"))
        cc_layout.addWidget(self.bcc_edit)
        
        header_layout.addRow(cc_layout)
        
        # 主题
        self.subject_edit = QLineEdit()
        self.subject_edit.setPlaceholderText("邮件主题")
        header_layout.addRow("主题:", self.subject_edit)
        
        # 优先级
        priority_layout = QHBoxLayout()
        
        self.priority_combo = QComboBox()
        self.priority_combo.addItems(["普通", "高", "低"])
        priority_layout.addWidget(self.priority_combo)
        
        # 选项
        self.request_receipt_cb = QCheckBox("请求阅读回执")
        priority_layout.addWidget(self.request_receipt_cb)
        
        self.save_sent_cb = QCheckBox("保存到已发送")
        self.save_sent_cb.setChecked(True)
        priority_layout.addWidget(self.save_sent_cb)
        
        priority_layout.addStretch()
        
        header_layout.addRow("选项:", priority_layout)
        
        parent_layout.addWidget(header_group)
    
    def create_content_section(self, parent_layout):
        """创建内容编辑区域"""
        content_group = QGroupBox("邮件内容")
        content_layout = QVBoxLayout(content_group)
        
        # 内容格式选择
        format_layout = QHBoxLayout()
        
        self.format_combo = QComboBox()
        self.format_combo.addItems(["HTML", "纯文本"])  # HTML放在第一位作为默认选项
        self.format_combo.currentTextChanged.connect(self.on_format_changed)
        format_layout.addWidget(QLabel("格式:"))
        format_layout.addWidget(self.format_combo)
        
        format_layout.addStretch()
        
        content_layout.addLayout(format_layout)
        
        # 内容编辑器
        self.content_tabs = QTabWidget()
        
        # 文本编辑标签页
        self.text_edit = QTextEdit()
        self.text_edit.setPlaceholderText("在此输入邮件内容...")
        self.text_edit.setAcceptRichText(True)  # 默认启用富文本编辑
        self.content_tabs.addTab(self.text_edit, "编辑")
        
        # HTML预览标签页（如果可用）
        if RICH_EDITOR_AVAILABLE:
            self.html_preview = QWebEngineView()
            self.content_tabs.addTab(self.html_preview, "预览")
        
        content_layout.addWidget(self.content_tabs)
        
        parent_layout.addWidget(content_group)
    
    def create_attachment_section(self, parent_layout):
        """创建附件区域"""
        self.attachment_group = QGroupBox("附件")
        attachment_layout = QVBoxLayout(self.attachment_group)
        
        # 附件列表
        self.attachment_list = QListWidget()
        self.attachment_list.setMaximumHeight(100)
        attachment_layout.addWidget(self.attachment_list)
        
        # 附件操作按钮
        attachment_btn_layout = QHBoxLayout()
        
        add_attachment_btn = QPushButton("添加附件")
        add_attachment_btn.clicked.connect(self.add_attachment)
        attachment_btn_layout.addWidget(add_attachment_btn)
        
        remove_attachment_btn = QPushButton("移除选中")
        remove_attachment_btn.clicked.connect(self.remove_attachment)
        attachment_btn_layout.addWidget(remove_attachment_btn)
        
        attachment_btn_layout.addStretch()
        
        attachment_layout.addLayout(attachment_btn_layout)
        
        # 初始隐藏附件区域
        self.attachment_group.setVisible(False)
        
        parent_layout.addWidget(self.attachment_group)
    
    def create_button_section(self, parent_layout):
        """创建底部按钮区域"""
        button_layout = QHBoxLayout()
        
        # 发送按钮
        self.send_btn = QPushButton("发送")
        self.send_btn.setDefault(True)
        self.send_btn.clicked.connect(self.send_email)
        button_layout.addWidget(self.send_btn)
        
        # 保存草稿按钮
        self.draft_btn = QPushButton("保存草稿")
        self.draft_btn.clicked.connect(self.save_draft)
        button_layout.addWidget(self.draft_btn)
        
        button_layout.addStretch()
        
        # 取消按钮
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        parent_layout.addLayout(button_layout)
    
    def setup_connections(self):
        """设置信号连接"""
        # 内容变化时更新预览
        self.text_edit.textChanged.connect(self.update_preview)
        
        # 附件列表双击移除
        self.attachment_list.itemDoubleClicked.connect(self.remove_attachment)
    
    def populate_from_accounts(self):
        """填充发件人账户列表"""
        try:
            self.from_combo.clear()
            
            if self.account_configs:
                for account_id, config in self.account_configs.items():
                    display_text = f"{config.email} ({config.display_name or account_id})"
                    self.from_combo.addItem(display_text, account_id)
            else:
                self.from_combo.addItem("默认账户", "default")
                
        except Exception as e:
            print(f"填充账户列表失败: {e}")
    
    def fill_reply_content(self):
        """填充回复内容"""
        try:
            if not self.reply_to_email:
                return
            
            # 设置收件人为原发件人
            self.to_edit.setText(self.reply_to_email.sender or "")
            
            # 设置主题
            subject = self.reply_to_email.subject or ""
            if not subject.startswith("Re:"):
                subject = f"Re: {subject}"
            self.subject_edit.setText(subject)
            
            # 设置回复内容
            original_content = self.reply_to_email.text_body or ""
            reply_content = f"\n\n--- 原始邮件 ---\n"
            reply_content += f"发件人: {self.reply_to_email.sender}\n"
            reply_content += f"时间: {self.reply_to_email.date_sent}\n"
            reply_content += f"主题: {self.reply_to_email.subject}\n\n"
            reply_content += original_content
            
            self.text_edit.setPlainText(reply_content)
            
            # 将光标移到开头
            cursor = self.text_edit.textCursor()
            cursor.movePosition(cursor.MoveOperation.Start)
            self.text_edit.setTextCursor(cursor)
            
        except Exception as e:
            print(f"填充回复内容失败: {e}")
    
    def fill_forward_content(self):
        """填充转发内容"""
        try:
            if not self.forward_email:
                return
            
            # 设置主题
            subject = self.forward_email.subject or ""
            if not subject.startswith("Fwd:"):
                subject = f"Fwd: {subject}"
            self.subject_edit.setText(subject)
            
            # 设置转发内容
            forward_content = f"\n\n--- 转发邮件 ---\n"
            forward_content += f"发件人: {self.forward_email.sender}\n"
            forward_content += f"收件人: {self.forward_email.recipients}\n"
            forward_content += f"时间: {self.forward_email.date_sent}\n"
            forward_content += f"主题: {self.forward_email.subject}\n\n"
            forward_content += self.forward_email.text_body or ""
            
            self.text_edit.setPlainText(forward_content)
            
            # 将光标移到开头
            cursor = self.text_edit.textCursor()
            cursor.movePosition(cursor.MoveOperation.Start)
            self.text_edit.setTextCursor(cursor)
            
        except Exception as e:
            print(f"填充转发内容失败: {e}")
    
    def on_format_changed(self, format_type):
        """格式类型变化处理"""
        try:
            if format_type == "HTML":
                # 启用HTML格式
                self.text_edit.setAcceptRichText(True)
                if RICH_EDITOR_AVAILABLE:
                    self.update_preview()
            else:
                # 纯文本格式
                self.text_edit.setAcceptRichText(False)
                
        except Exception as e:
            print(f"格式变化处理失败: {e}")
    
    def update_preview(self):
        """更新HTML预览"""
        try:
            if not RICH_EDITOR_AVAILABLE:
                return

            if self.format_combo.currentText() == "HTML":
                content = self.text_edit.toHtml()
                # 添加基本的CSS样式使预览更美观
                styled_content = f"""
                <html>
                <head>
                    <style>
                        body {{
                            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                            font-size: 12pt;
                            line-height: 1.6;
                            margin: 20px;
                            background-color: white;
                        }}
                    </style>
                </head>
                <body>
                    {content}
                </body>
                </html>
                """
                self.html_preview.setHtml(styled_content)

        except Exception as e:
            print(f"更新预览失败: {e}")
    
    def format_bold(self):
        """设置粗体格式"""
        try:
            if self.format_combo.currentText() == "HTML":
                cursor = self.text_edit.textCursor()
                format = cursor.charFormat()
                current_weight = format.fontWeight()
                new_weight = QFont.Weight.Normal if current_weight == QFont.Weight.Bold else QFont.Weight.Bold
                format.setFontWeight(new_weight)
                cursor.setCharFormat(format)
                self.text_edit.setTextCursor(cursor)
        except Exception as e:
            print(f"设置粗体失败: {e}")

    def format_italic(self):
        """设置斜体格式"""
        try:
            if self.format_combo.currentText() == "HTML":
                cursor = self.text_edit.textCursor()
                format = cursor.charFormat()
                format.setFontItalic(not format.fontItalic())
                cursor.setCharFormat(format)
                self.text_edit.setTextCursor(cursor)
        except Exception as e:
            print(f"设置斜体失败: {e}")

    def format_underline(self):
        """设置下划线格式"""
        try:
            if self.format_combo.currentText() == "HTML":
                cursor = self.text_edit.textCursor()
                format = cursor.charFormat()
                format.setFontUnderline(not format.fontUnderline())
                cursor.setCharFormat(format)
                self.text_edit.setTextCursor(cursor)
        except Exception as e:
            print(f"设置下划线失败: {e}")

    def change_font_size(self, size_text):
        """改变字体大小"""
        try:
            if self.format_combo.currentText() == "HTML":
                size = int(size_text)
                cursor = self.text_edit.textCursor()
                format = cursor.charFormat()
                format.setFontPointSize(size)
                cursor.setCharFormat(format)
                self.text_edit.setTextCursor(cursor)
        except Exception as e:
            print(f"设置字体大小失败: {e}")

    def change_font_color(self):
        """改变字体颜色"""
        try:
            if self.format_combo.currentText() == "HTML":
                from PySide6.QtWidgets import QColorDialog

                color = QColorDialog.getColor(QColor("black"), self, "选择字体颜色")
                if color.isValid():
                    cursor = self.text_edit.textCursor()
                    format = cursor.charFormat()
                    format.setForeground(color)
                    cursor.setCharFormat(format)
                    self.text_edit.setTextCursor(cursor)
        except Exception as e:
            print(f"设置字体颜色失败: {e}")

    def add_attachment(self):
        """添加附件"""
        try:
            files, _ = QFileDialog.getOpenFileNames(
                self,
                "选择附件",
                "",
                "所有文件 (*.*)"
            )
            
            for file_path in files:
                if file_path and Path(file_path).exists():
                    # 添加到附件列表
                    file_name = Path(file_path).name
                    file_size = Path(file_path).stat().st_size
                    
                    attachment_info = {
                        'path': file_path,
                        'name': file_name,
                        'size': file_size
                    }
                    
                    self.attachments.append(attachment_info)
                    
                    # 添加到UI列表
                    size_str = self.format_file_size(file_size)
                    item_text = f"{file_name} ({size_str})"
                    
                    item = QListWidgetItem(item_text)
                    item.setData(Qt.ItemDataRole.UserRole, attachment_info)
                    self.attachment_list.addItem(item)
            
            # 显示附件区域
            if self.attachments:
                self.attachment_group.setVisible(True)
                
        except Exception as e:
            QMessageBox.warning(self, "错误", f"添加附件失败: {e}")
    
    def remove_attachment(self):
        """移除选中的附件"""
        try:
            current_item = self.attachment_list.currentItem()
            if current_item:
                # 从附件列表中移除
                attachment_info = current_item.data(Qt.ItemDataRole.UserRole)
                if attachment_info in self.attachments:
                    self.attachments.remove(attachment_info)
                
                # 从UI列表中移除
                row = self.attachment_list.row(current_item)
                self.attachment_list.takeItem(row)
                
                # 如果没有附件了，隐藏附件区域
                if not self.attachments:
                    self.attachment_group.setVisible(False)
                    
        except Exception as e:
            QMessageBox.warning(self, "错误", f"移除附件失败: {e}")
    
    def get_email_data(self):
        """获取邮件数据"""
        try:
            # 获取选中的发件账户
            from_account = self.from_combo.currentData()
            
            email_data = {
                'from_account': from_account,
                'to': self.to_edit.text().strip(),
                'cc': self.cc_edit.text().strip(),
                'bcc': self.bcc_edit.text().strip(),
                'subject': self.subject_edit.text().strip(),
                'content': self.text_edit.toPlainText(),
                'html_content': self.text_edit.toHtml() if self.format_combo.currentText() == "HTML" else "",
                'format': self.format_combo.currentText(),
                'priority': self.priority_combo.currentText(),
                'request_receipt': self.request_receipt_cb.isChecked(),
                'save_sent': self.save_sent_cb.isChecked(),
                'attachments': self.attachments.copy(),
                'timestamp': datetime.now()
            }
            
            return email_data
            
        except Exception as e:
            print(f"获取邮件数据失败: {e}")
            return None
    
    def validate_email_data(self, email_data):
        """验证邮件数据"""
        errors = []
        
        if not email_data['to']:
            errors.append("请输入收件人地址")
        
        if not email_data['subject']:
            errors.append("请输入邮件主题")
        
        if not email_data['content'].strip():
            errors.append("请输入邮件内容")
        
        # 验证邮箱地址格式
        import re
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        
        for addr in email_data['to'].split(';'):
            addr = addr.strip()
            if addr and not re.match(email_pattern, addr):
                errors.append(f"收件人地址格式错误: {addr}")
        
        return errors
    
    def send_email(self):
        """发送邮件"""
        try:
            # 获取邮件数据
            email_data = self.get_email_data()
            if not email_data:
                QMessageBox.warning(self, "错误", "获取邮件数据失败")
                return
            
            # 验证邮件数据
            errors = self.validate_email_data(email_data)
            if errors:
                QMessageBox.warning(self, "验证失败", "\n".join(errors))
                return
            
            # 确认发送
            reply = QMessageBox.question(
                self,
                "确认发送",
                f"确定要发送邮件给 {email_data['to']} 吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.Yes
            )
            
            if reply != QMessageBox.StandardButton.Yes:
                return
            
            # 开始发送
            self.start_send_process(email_data)
            
        except Exception as e:
            QMessageBox.warning(self, "错误", f"发送邮件失败: {e}")
    
    def start_send_process(self, email_data):
        """开始发送过程"""
        try:
            # 禁用发送按钮
            self.send_btn.setEnabled(False)
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            
            # 获取账户配置
            account_config = self.account_configs.get(email_data['from_account'])
            
            # 创建发送线程
            self.send_thread = EmailSendThread(email_data, account_config)
            self.send_thread.progress_updated.connect(self.progress_bar.setValue)
            self.send_thread.send_finished.connect(self.on_send_finished)
            self.send_thread.start()
            
        except Exception as e:
            self.progress_bar.setVisible(False)
            self.send_btn.setEnabled(True)
            QMessageBox.warning(self, "错误", f"启动发送失败: {e}")
    
    def on_send_finished(self, success, message):
        """发送完成处理"""
        try:
            self.progress_bar.setVisible(False)
            self.send_btn.setEnabled(True)
            
            if success:
                QMessageBox.information(self, "发送成功", message)
                
                # 发送信号
                email_data = self.get_email_data()
                if email_data:
                    self.email_sent.emit(email_data)
                
                # 关闭对话框
                self.accept()
            else:
                QMessageBox.warning(self, "发送失败", message)
                
        except Exception as e:
            QMessageBox.warning(self, "错误", f"发送完成处理失败: {e}")
    
    def save_draft(self):
        """保存草稿"""
        try:
            email_data = self.get_email_data()
            if not email_data:
                QMessageBox.warning(self, "错误", "获取邮件数据失败")
                return
            
            # 这里应该保存到草稿文件夹
            # 暂时显示提示
            QMessageBox.information(self, "草稿已保存", "邮件已保存到草稿箱")
            
        except Exception as e:
            QMessageBox.warning(self, "错误", f"保存草稿失败: {e}")
    
    def format_file_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0B"
        
        size_names = ["B", "KB", "MB", "GB"]
        import math
        i = int(math.floor(math.log(size_bytes, 1024)))
        p = math.pow(1024, i)
        s = round(size_bytes / p, 2)
        return f"{s} {size_names[i]}"

if __name__ == "__main__":
    from PySide6.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    
    # 模拟账户配置
    account_configs = {
        "account1": type('Config', (), {
            'email': '<EMAIL>',
            'display_name': '用户账户'
        })(),
        "account2": type('Config', (), {
            'email': '<EMAIL>',
            'display_name': '工作账户'
        })()
    }
    
    dialog = EmailComposeDialog(account_configs=account_configs)
    dialog.show()
    
    sys.exit(app.exec())
