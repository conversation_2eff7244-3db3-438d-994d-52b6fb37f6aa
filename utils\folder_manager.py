#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生产级文件夹管理器
处理 IMAP 文件夹结构、编码和缓存
"""

import base64
import json
import time
import re
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

class FolderType(Enum):
    """文件夹类型枚举"""
    INBOX = "inbox"
    SENT = "sent"
    DRAFTS = "drafts"
    TRASH = "trash"
    JUNK = "junk"
    ARCHIVE = "archive"
    CUSTOM = "custom"
    NOTES = "notes"
    OUTBOX = "outbox"

@dataclass
class FolderInfo:
    """文件夹信息数据类"""
    name: str                    # 原始名称
    display_name: str           # 显示名称（解码后）
    folder_type: FolderType     # 文件夹类型
    flags: List[str]            # IMAP 标志
    has_children: bool          # 是否有子文件夹
    selectable: bool            # 是否可选择
    message_count: int = 0      # 邮件数量
    unread_count: int = 0       # 未读数量
    last_updated: float = 0     # 最后更新时间

class ModifiedUTF7Decoder:
    """Modified UTF-7 解码器（IMAP 标准）"""
    
    @staticmethod
    def decode(encoded_name: str) -> str:
        """
        解码 IMAP Modified UTF-7 编码的文件夹名
        例: &W1hoYw- -> 存档
        """
        if not encoded_name.startswith('&') or not encoded_name.endswith('-'):
            return encoded_name
        
        try:
            encoded_part = encoded_name[1:-1]
            
            if not encoded_part:  # &- 表示 &
                return '&'
            
            # Modified UTF-7 特殊处理
            encoded_part = encoded_part.replace(',', '/')
            
            # 添加 Base64 填充
            while len(encoded_part) % 4:
                encoded_part += '='
            
            # Base64 解码
            decoded_bytes = base64.b64decode(encoded_part)
            
            # UTF-16BE 解码
            decoded_text = decoded_bytes.decode('utf-16be')
            
            return decoded_text
            
        except Exception as e:
            print(f"Modified UTF-7 解码失败 {encoded_name}: {e}")
            return encoded_name
    
    @staticmethod
    def encode(text: str) -> str:
        """
        编码文本为 IMAP Modified UTF-7 格式
        """
        if text == '&':
            return '&-'
        
        # 检查是否需要编码
        if all(ord(c) < 128 and c.isprintable() and c not in '&' for c in text):
            return text
        
        try:
            # UTF-16BE 编码
            utf16_bytes = text.encode('utf-16be')
            
            # Base64 编码
            b64_encoded = base64.b64encode(utf16_bytes).decode('ascii')
            
            # Modified UTF-7 特殊处理
            b64_encoded = b64_encoded.replace('/', ',').rstrip('=')
            
            return f'&{b64_encoded}-'
            
        except Exception as e:
            print(f"Modified UTF-7 编码失败 {text}: {e}")
            return text

class FolderManager:
    """文件夹管理器"""
    
    def __init__(self, cache_ttl: int = 300):  # 5分钟缓存
        self.cache_ttl = cache_ttl
        self.folder_cache: Dict[str, Dict[str, FolderInfo]] = {}  # account_id -> folders
        self.cache_timestamps: Dict[str, float] = {}
        self.decoder = ModifiedUTF7Decoder()
        
        # 文件夹类型映射
        self.folder_type_mapping = {
            '\\Inbox': FolderType.INBOX,
            '\\Sent': FolderType.SENT,
            '\\Drafts': FolderType.DRAFTS,
            '\\Trash': FolderType.TRASH,
            '\\Junk': FolderType.JUNK,
            '\\All': FolderType.ARCHIVE,
            '\\Archive': FolderType.ARCHIVE,
        }
        
        # 中文文件夹名映射
        self.chinese_folder_mapping = {
            '存档': FolderType.ARCHIVE,
            '草稿': FolderType.DRAFTS,
            '已发送': FolderType.SENT,
            '垃圾邮件': FolderType.JUNK,
            '收件箱': FolderType.INBOX,
            '已删除': FolderType.TRASH,
        }
    
    def parse_folder_line(self, folder_line: str) -> Optional[FolderInfo]:
        """
        解析 IMAP LIST 响应行
        例: * LIST (\HasNoChildren \Drafts) "/" "Drafts"
        """
        try:
            # 正则表达式解析 LIST 响应
            pattern = r'\* LIST \(([^)]*)\) "([^"]*)" "([^"]*)"'
            match = re.match(pattern, folder_line)
            
            if not match:
                return None
            
            flags_str, delimiter, folder_name = match.groups()
            flags = [flag.strip() for flag in flags_str.split() if flag.strip()]
            
            # 解码文件夹名
            display_name = self.decoder.decode(folder_name)
            
            # 确定文件夹类型
            folder_type = self._determine_folder_type(flags, display_name)
            
            # 解析标志
            has_children = '\\HasChildren' in flags
            selectable = '\\Noselect' not in flags
            
            return FolderInfo(
                name=folder_name,
                display_name=display_name,
                folder_type=folder_type,
                flags=flags,
                has_children=has_children,
                selectable=selectable,
                last_updated=time.time()
            )
            
        except Exception as e:
            print(f"解析文件夹行失败: {e}")
            return None
    
    def _determine_folder_type(self, flags: List[str], display_name: str) -> FolderType:
        """确定文件夹类型"""
        # 首先检查 IMAP 标志
        for flag in flags:
            if flag in self.folder_type_mapping:
                return self.folder_type_mapping[flag]
        
        # 检查中文文件夹名
        if display_name in self.chinese_folder_mapping:
            return self.chinese_folder_mapping[display_name]
        
        # 检查英文文件夹名（不区分大小写）
        name_lower = display_name.lower()
        name_mappings = {
            'inbox': FolderType.INBOX,
            'sent': FolderType.SENT,
            'drafts': FolderType.DRAFTS,
            'trash': FolderType.TRASH,
            'deleted': FolderType.TRASH,
            'junk': FolderType.JUNK,
            'spam': FolderType.JUNK,
            'archive': FolderType.ARCHIVE,
            'notes': FolderType.NOTES,
            'outbox': FolderType.OUTBOX,
        }
        
        return name_mappings.get(name_lower, FolderType.CUSTOM)
    
    def update_folder_cache(self, account_id: str, folder_lines: List[str]) -> Dict[str, FolderInfo]:
        """更新文件夹缓存"""
        folders = {}
        
        for line in folder_lines:
            folder_info = self.parse_folder_line(line)
            if folder_info:
                folders[folder_info.name] = folder_info
        
        # 更新缓存
        self.folder_cache[account_id] = folders
        self.cache_timestamps[account_id] = time.time()
        
        return folders
    
    def get_folders(self, account_id: str) -> Optional[Dict[str, FolderInfo]]:
        """获取文件夹列表（优先从缓存）"""
        # 检查缓存是否有效
        if account_id in self.cache_timestamps:
            cache_age = time.time() - self.cache_timestamps[account_id]
            if cache_age < self.cache_ttl:
                return self.folder_cache.get(account_id)
        
        return None
    
    def get_folder_by_type(self, account_id: str, folder_type: FolderType) -> Optional[FolderInfo]:
        """根据类型获取文件夹"""
        folders = self.get_folders(account_id)
        if not folders:
            return None
        
        for folder in folders.values():
            if folder.folder_type == folder_type:
                return folder
        
        return None
    
    def get_special_folders(self, account_id: str) -> Dict[FolderType, FolderInfo]:
        """获取特殊文件夹映射"""
        folders = self.get_folders(account_id)
        if not folders:
            return {}
        
        special_folders = {}
        for folder in folders.values():
            if folder.folder_type != FolderType.CUSTOM:
                special_folders[folder.folder_type] = folder
        
        return special_folders
    
    def clear_cache(self, account_id: Optional[str] = None):
        """清除缓存"""
        if account_id:
            self.folder_cache.pop(account_id, None)
            self.cache_timestamps.pop(account_id, None)
        else:
            self.folder_cache.clear()
            self.cache_timestamps.clear()
    
    def export_folder_structure(self, account_id: str) -> Dict:
        """导出文件夹结构（用于调试和备份）"""
        folders = self.get_folders(account_id)
        if not folders:
            return {}
        
        export_data = {
            'account_id': account_id,
            'timestamp': time.time(),
            'folders': {}
        }
        
        for name, folder in folders.items():
            export_data['folders'][name] = {
                'name': folder.name,
                'display_name': folder.display_name,
                'type': folder.folder_type.value,
                'flags': folder.flags,
                'has_children': folder.has_children,
                'selectable': folder.selectable,
                'message_count': folder.message_count,
                'unread_count': folder.unread_count
            }
        
        return export_data

def test_folder_manager():
    """测试文件夹管理器"""
    print("🧪 测试文件夹管理器")
    print("=" * 50)
    
    # 创建管理器
    manager = FolderManager()
    
    # 测试 Modified UTF-7 解码
    test_cases = [
        "&W1hoYw-",  # 存档
        "Drafts",    # 草稿
        "Inbox",     # 收件箱
        "&-",        # &
    ]
    
    print("🔤 Modified UTF-7 解码测试:")
    for case in test_cases:
        decoded = manager.decoder.decode(case)
        print(f"   {case} -> {decoded}")
    
    # 测试文件夹解析
    sample_folders = [
        '* LIST (\\HasNoChildren \\Drafts) "/" "Drafts"',
        '* LIST (\\HasNoChildren) "/" "&W1hoYw-"',
        '* LIST (\\HasNoChildren) "/" "Outbox"',
        '* LIST (\\HasNoChildren \\Junk) "/" "Junk"',
        '* LIST (\\HasNoChildren) "/" "Inbox"',
        '* LIST (\\HasNoChildren \\Sent) "/" "Sent"',
        '* LIST (\\HasNoChildren \\Trash) "/" "Deleted"',
        '* LIST (\\HasNoChildren) "/" "Notes"',
    ]
    
    print(f"\n📁 文件夹解析测试:")
    folders = manager.update_folder_cache("test_account", sample_folders)
    
    for name, folder in folders.items():
        print(f"   📂 {folder.display_name} ({folder.folder_type.value})")
        print(f"      原始名: {folder.name}")
        print(f"      标志: {folder.flags}")
        print(f"      可选择: {folder.selectable}")
    
    # 测试特殊文件夹获取
    print(f"\n🎯 特殊文件夹映射:")
    special = manager.get_special_folders("test_account")
    for folder_type, folder in special.items():
        print(f"   {folder_type.value}: {folder.display_name}")

if __name__ == "__main__":
    test_folder_manager()
