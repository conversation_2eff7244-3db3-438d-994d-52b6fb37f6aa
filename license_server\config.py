#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
许可证服务器配置文件
"""

import os
from datetime import timedelta


class Config:
    """基础配置"""
    
    # 基本设置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    
    # 数据库配置
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///license_server.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # API配置
    API_VERSION = 'v1'
    API_PREFIX = '/api/v1'
    
    # 安全配置
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY') or 'jwt-secret-key-change-in-production'
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=24)
    
    # 服务器配置
    HOST = os.environ.get('HOST') or '0.0.0.0'
    PORT = int(os.environ.get('PORT') or 8080)
    DEBUG = os.environ.get('DEBUG', 'False').lower() == 'true'
    
    # 许可证配置
    DEFAULT_LICENSE_DURATION_DAYS = int(os.environ.get('DEFAULT_LICENSE_DURATION_DAYS') or 365)
    MAX_ACTIVATIONS_PER_LICENSE = int(os.environ.get('MAX_ACTIVATIONS_PER_LICENSE') or 1)
    
    # 速率限制配置
    RATELIMIT_STORAGE_URL = os.environ.get('RATELIMIT_STORAGE_URL') or 'memory://'
    RATELIMIT_DEFAULT = os.environ.get('RATELIMIT_DEFAULT') or '100 per hour'
    
    # 管理员配置
    ADMIN_USERNAME = os.environ.get('ADMIN_USERNAME') or 'admin'
    ADMIN_PASSWORD = os.environ.get('ADMIN_PASSWORD') or 'admin123'  # 生产环境必须修改
    
    # 邮件配置（可选）
    MAIL_SERVER = os.environ.get('MAIL_SERVER')
    MAIL_PORT = int(os.environ.get('MAIL_PORT') or 587)
    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'True').lower() == 'true'
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
    
    # 日志配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL') or 'INFO'
    LOG_FILE = os.environ.get('LOG_FILE') or 'license_server.log'


class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///license_server_dev.db'


class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    # 生产环境应使用PostgreSQL或MySQL
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///license_server_prod.db'
    
    # 生产环境安全设置
    SECRET_KEY = os.environ.get('SECRET_KEY')
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY')
    
    if not SECRET_KEY:
        raise ValueError("生产环境必须设置 SECRET_KEY 环境变量")
    if not JWT_SECRET_KEY:
        raise ValueError("生产环境必须设置 JWT_SECRET_KEY 环境变量")


class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    WTF_CSRF_ENABLED = False


# 配置字典
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
