#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动提示对话框
程序启动时显示重要提示信息
"""

import os
import sys
import json
import logging
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QCheckBox, QTextEdit, QFrame, QApplication
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont, QPixmap, QIcon

class StartupNoticeDialog(QDialog):
    """启动提示对话框"""
    
    # 信号：用户点击了注意事项链接
    show_notice_requested = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("🚀 微软ou批量管理 - 启动提示")
        self.setModal(True)
        self.setFixedSize(500, 400)
        
        # 配置文件路径
        self.config_file = "config/startup_settings.json"
        self.config_dir = os.path.dirname(self.config_file)
        
        # 确保配置目录存在
        if not os.path.exists(self.config_dir):
            os.makedirs(self.config_dir)
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
        
        # 用户选择结果
        self.user_choice = None
        self.dont_show_again = False
        
        self.setup_ui()
        self.setup_connections()
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # 标题区域
        title_frame = QFrame()
        title_frame.setStyleSheet("""
            QFrame {
                background-color: #2196F3;
                border-radius: 8px;
                padding: 10px;
            }
        """)
        title_layout = QVBoxLayout(title_frame)
        
        title_label = QLabel("🚀 微软ou批量管理")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setStyleSheet("color: white; text-align: center;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_layout.addWidget(title_label)
        
        subtitle_label = QLabel("重要提示")
        subtitle_label.setFont(QFont("Arial", 12))
        subtitle_label.setStyleSheet("color: white; text-align: center;")
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_layout.addWidget(subtitle_label)
        
        layout.addWidget(title_frame)
        
        # 主要提示内容
        content_frame = QFrame()
        content_frame.setStyleSheet("""
            QFrame {
                background-color: #FFF3E0;
                border: 2px solid #FF9800;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        content_layout = QVBoxLayout(content_frame)
        
        # 警告图标和文本
        warning_layout = QHBoxLayout()
        
        warning_icon = QLabel("⚠️")
        warning_icon.setFont(QFont("Arial", 24))
        warning_icon.setFixedSize(40, 40)
        warning_icon.setAlignment(Qt.AlignmentFlag.AlignCenter)
        warning_layout.addWidget(warning_icon)
        
        main_message = QLabel(
            "请关闭任何VPN节点和网络代理工具，\n"
            "以确保邮件服务正常连接"
        )
        main_message.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        main_message.setStyleSheet("color: #E65100; line-height: 1.5;")
        main_message.setWordWrap(True)
        warning_layout.addWidget(main_message)
        
        content_layout.addLayout(warning_layout)
        
        # 详细说明
        detail_text = QTextEdit()
        detail_text.setReadOnly(True)
        detail_text.setMaximumHeight(120)
        detail_text.setStyleSheet("""
            QTextEdit {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 8px;
                font-size: 12px;
            }
        """)
        detail_text.setHtml("""
        <div style="line-height: 1.4;">
        <b>为什么需要关闭VPN和代理？</b><br>
        • VPN和代理可能会影响邮件服务器的连接<br>
        • 某些邮件提供商会阻止来自代理IP的连接<br>
        • 确保网络环境的稳定性和安全性<br><br>
        
        <b>如果您需要了解更多注意事项，请点击下方的"查看注意事项"按钮。</b>
        </div>
        """)
        content_layout.addWidget(detail_text)
        
        layout.addWidget(content_frame)
        
        # 选项区域
        options_layout = QVBoxLayout()
        
        # 不再显示复选框
        self.dont_show_checkbox = QCheckBox("下次启动时不再显示此提示")
        self.dont_show_checkbox.setFont(QFont("Arial", 11))
        self.dont_show_checkbox.setStyleSheet("color: #666;")
        options_layout.addWidget(self.dont_show_checkbox)
        
        layout.addLayout(options_layout)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        # 查看注意事项按钮
        self.notice_btn = QPushButton("📋 查看注意事项")
        self.notice_btn.setToolTip("查看详细的使用注意事项和常见问题")
        self.notice_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                padding: 10px 20px;
                font-weight: bold;
                border-radius: 5px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
            QPushButton:pressed {
                background-color: #E65100;
            }
        """)
        button_layout.addWidget(self.notice_btn)
        
        button_layout.addStretch()
        
        # 取消按钮
        self.cancel_btn = QPushButton("❌ 取消")
        self.cancel_btn.setToolTip("取消启动程序")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #757575;
                color: white;
                border: none;
                padding: 10px 20px;
                font-weight: bold;
                border-radius: 5px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #616161;
            }
            QPushButton:pressed {
                background-color: #424242;
            }
        """)
        button_layout.addWidget(self.cancel_btn)
        
        # 确定按钮
        self.ok_btn = QPushButton("✅ 确定")
        self.ok_btn.setToolTip("确认已关闭VPN和代理，继续启动程序")
        self.ok_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 10px 20px;
                font-weight: bold;
                border-radius: 5px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
        """)
        button_layout.addWidget(self.ok_btn)
        
        layout.addLayout(button_layout)
        
        # 设置默认按钮
        self.ok_btn.setDefault(True)
        self.ok_btn.setFocus()
    
    def setup_connections(self):
        """设置信号连接"""
        self.ok_btn.clicked.connect(self.accept_and_continue)
        self.cancel_btn.clicked.connect(self.reject_and_exit)
        self.notice_btn.clicked.connect(self.show_notice)
    
    def accept_and_continue(self):
        """确定并继续"""
        self.user_choice = "continue"
        self.dont_show_again = self.dont_show_checkbox.isChecked()
        
        # 保存设置
        if self.dont_show_again:
            self.save_startup_settings()
        
        self.logger.info(f"用户选择继续启动，不再显示: {self.dont_show_again}")
        self.accept()
    
    def reject_and_exit(self):
        """取消并退出"""
        self.user_choice = "exit"
        self.logger.info("用户选择取消启动")
        self.reject()
    
    def show_notice(self):
        """显示注意事项"""
        self.logger.info("用户请求查看注意事项")
        self.show_notice_requested.emit()
    
    def save_startup_settings(self):
        """保存启动设置"""
        try:
            settings = {
                "show_startup_notice": False,
                "last_updated": str(datetime.now()),
                "version": "1.0"
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"启动设置已保存到: {self.config_file}")
            
        except Exception as e:
            self.logger.error(f"保存启动设置失败: {e}")
    
    @staticmethod
    def should_show_notice():
        """检查是否应该显示启动提示"""
        try:
            config_file = "config/startup_settings.json"
            
            if not os.path.exists(config_file):
                return True  # 配置文件不存在，显示提示
            
            with open(config_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)
            
            return settings.get("show_startup_notice", True)
            
        except Exception:
            return True  # 读取配置失败，显示提示
    
    @staticmethod
    def reset_startup_settings():
        """重置启动设置（用于测试或重新启用提示）"""
        try:
            config_file = "config/startup_settings.json"
            
            if os.path.exists(config_file):
                os.remove(config_file)
                return True
            
        except Exception:
            pass
        
        return False
    
    def get_user_choice(self):
        """获取用户选择"""
        return self.user_choice
    
    def is_dont_show_again(self):
        """是否选择了不再显示"""
        return self.dont_show_again

# 导入datetime（在文件顶部添加）
from datetime import datetime

def show_startup_notice(parent=None):
    """显示启动提示的便捷函数"""
    if not StartupNoticeDialog.should_show_notice():
        return "continue"  # 不需要显示提示，直接继续
    
    dialog = StartupNoticeDialog(parent)
    result = dialog.exec()
    
    if result == QDialog.DialogCode.Accepted:
        return "continue"
    else:
        return "exit"

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # 测试对话框
    dialog = StartupNoticeDialog()
    result = dialog.exec()
    
    print(f"用户选择: {dialog.get_user_choice()}")
    print(f"不再显示: {dialog.is_dont_show_again()}")
    
    sys.exit(0)
