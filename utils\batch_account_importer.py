#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量账户导入工具
支持从CSV、JSON等格式批量导入邮箱账户
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import csv
import json
import logging
from typing import List, Dict
from pathlib import Path

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.multi_account_manager import AccountConfig

class BatchAccountImporter:
    """批量账户导入器"""
    
    def __init__(self, parent, account_manager):
        self.parent = parent
        self.account_manager = account_manager
        self.logger = logging.getLogger(__name__)
        
        # 创建导入窗口
        self.create_import_window()
    
    def create_import_window(self):
        """创建导入窗口"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("批量导入邮箱账户")
        self.window.geometry("700x500")
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # 居中显示
        self.window.geometry("+%d+%d" % (
            self.parent.winfo_rootx() + 100, 
            self.parent.winfo_rooty() + 100
        ))
        
        self.create_ui()
    
    def create_ui(self):
        """创建用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="批量导入邮箱账户", font=('Arial', 14, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="选择导入文件", padding="10")
        file_frame.pack(fill=tk.X, pady=10)
        
        # 文件路径
        path_frame = ttk.Frame(file_frame)
        path_frame.pack(fill=tk.X)
        
        self.file_path_var = tk.StringVar()
        ttk.Entry(path_frame, textvariable=self.file_path_var, width=50).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(path_frame, text="浏览", command=self.browse_file).pack(side=tk.RIGHT, padx=(10, 0))
        
        # 文件格式选择
        format_frame = ttk.Frame(file_frame)
        format_frame.pack(fill=tk.X, pady=10)
        
        ttk.Label(format_frame, text="文件格式:").pack(side=tk.LEFT)
        self.format_var = tk.StringVar(value="csv")
        ttk.Radiobutton(format_frame, text="CSV", variable=self.format_var, value="csv").pack(side=tk.LEFT, padx=10)
        ttk.Radiobutton(format_frame, text="JSON", variable=self.format_var, value="json").pack(side=tk.LEFT, padx=10)
        ttk.Radiobutton(format_frame, text="TXT", variable=self.format_var, value="txt").pack(side=tk.LEFT, padx=10)
        
        # 预览区域
        preview_frame = ttk.LabelFrame(main_frame, text="导入预览", padding="10")
        preview_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # 预览表格
        columns = ('邮箱地址', 'Client ID', 'Refresh Token', '状态')
        self.preview_tree = ttk.Treeview(preview_frame, columns=columns, show='headings', height=8)
        
        for col in columns:
            self.preview_tree.heading(col, text=col)
            self.preview_tree.column(col, width=150)
        
        # 滚动条
        preview_scroll = ttk.Scrollbar(preview_frame, orient=tk.VERTICAL, command=self.preview_tree.yview)
        self.preview_tree.configure(yscrollcommand=preview_scroll.set)
        
        self.preview_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        preview_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 操作按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=20)
        
        ttk.Button(button_frame, text="预览", command=self.preview_file).pack(side=tk.LEFT)
        ttk.Button(button_frame, text="下载模板", command=self.download_template).pack(side=tk.LEFT, padx=10)
        
        ttk.Button(button_frame, text="取消", command=self.window.destroy).pack(side=tk.RIGHT)
        ttk.Button(button_frame, text="导入", command=self.import_accounts).pack(side=tk.RIGHT, padx=(0, 10))
        
        # 说明文字
        help_text = """
导入格式说明:
CSV格式: email,client_id,refresh_token,password
JSON格式: [{"email": "...", "client_id": "...", "refresh_token": "...", "password": "..."}]
TXT格式: 每行一个邮箱，格式为 email|client_id|refresh_token|password
        """
        help_label = ttk.Label(main_frame, text=help_text, font=('Arial', 9), foreground='gray')
        help_label.pack(pady=10)
    
    def browse_file(self):
        """浏览文件"""
        file_types = [
            ("所有支持的文件", "*.csv;*.json;*.txt"),
            ("CSV文件", "*.csv"),
            ("JSON文件", "*.json"),
            ("文本文件", "*.txt"),
            ("所有文件", "*.*")
        ]
        
        filename = filedialog.askopenfilename(
            title="选择导入文件",
            filetypes=file_types
        )
        
        if filename:
            self.file_path_var.set(filename)
            # 自动检测文件格式
            ext = Path(filename).suffix.lower()
            if ext == '.csv':
                self.format_var.set('csv')
            elif ext == '.json':
                self.format_var.set('json')
            elif ext == '.txt':
                self.format_var.set('txt')
    
    def preview_file(self):
        """预览文件内容"""
        file_path = self.file_path_var.get()
        if not file_path:
            messagebox.showerror("错误", "请先选择文件")
            return
        
        try:
            # 清空预览表格
            for item in self.preview_tree.get_children():
                self.preview_tree.delete(item)
            
            # 根据格式解析文件
            format_type = self.format_var.get()
            accounts = self.parse_file(file_path, format_type)
            
            # 显示预览
            for account in accounts:
                status = self.validate_account(account)
                self.preview_tree.insert('', 'end', values=(
                    account.get('email', ''),
                    account.get('client_id', ''),
                    account.get('refresh_token', '')[:30] + '...' if account.get('refresh_token') else '',
                    status
                ))
            
            messagebox.showinfo("成功", f"预览完成，共 {len(accounts)} 个账户")
            
        except Exception as e:
            self.logger.error(f"预览文件失败: {e}")
            messagebox.showerror("错误", f"预览文件失败: {e}")
    
    def parse_file(self, file_path: str, format_type: str) -> List[Dict]:
        """解析文件"""
        accounts = []
        
        try:
            if format_type == 'csv':
                with open(file_path, 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    for row in reader:
                        accounts.append(dict(row))
            
            elif format_type == 'json':
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if isinstance(data, list):
                        accounts = data
                    else:
                        accounts = [data]
            
            elif format_type == 'txt':
                with open(file_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line and '|' in line:
                            parts = line.split('|')
                            if len(parts) >= 3:
                                accounts.append({
                                    'email': parts[0].strip(),
                                    'client_id': parts[1].strip(),
                                    'refresh_token': parts[2].strip(),
                                    'password': parts[3].strip() if len(parts) > 3 else ''
                                })
            
            return accounts
            
        except Exception as e:
            self.logger.error(f"解析文件失败: {e}")
            raise
    
    def validate_account(self, account: Dict) -> str:
        """验证账户信息"""
        if not account.get('email'):
            return "缺少邮箱地址"
        
        if not account.get('refresh_token'):
            return "缺少Refresh Token"
        
        if '@' not in account.get('email', ''):
            return "邮箱格式错误"
        
        # 检查是否已存在
        for existing_id, existing_account in self.account_manager.accounts.items():
            if existing_account.email == account.get('email'):
                return "账户已存在"
        
        return "有效"
    
    def import_accounts(self):
        """导入账户"""
        file_path = self.file_path_var.get()
        if not file_path:
            messagebox.showerror("错误", "请先选择文件")
            return
        
        try:
            # 解析文件
            format_type = self.format_var.get()
            accounts = self.parse_file(file_path, format_type)
            
            if not accounts:
                messagebox.showwarning("警告", "没有找到有效的账户信息")
                return
            
            # 导入账户
            success_count = 0
            error_count = 0
            
            for account_data in accounts:
                try:
                    # 验证账户
                    status = self.validate_account(account_data)
                    if status != "有效":
                        error_count += 1
                        continue
                    
                    # 创建账户配置
                    account_config = AccountConfig(
                        account_id=f"imported_account_{len(self.account_manager.accounts) + success_count + 1}",
                        email=account_data.get('email', ''),
                        client_id=account_data.get('client_id', '9e5f94bc-e8a4-4e73-b8be-63364c29d753'),
                        refresh_token=account_data.get('refresh_token', ''),
                        password=account_data.get('password', ''),
                        enabled=True,
                        priority=1,
                        max_retries=3
                    )
                    
                    # 添加到管理器
                    self.account_manager.add_account(account_config)
                    success_count += 1
                    
                except Exception as e:
                    self.logger.error(f"导入账户失败: {e}")
                    error_count += 1
            
            # 保存配置
            self.save_config()
            
            # 显示结果
            message = f"导入完成!\n成功: {success_count} 个账户\n失败: {error_count} 个账户"
            messagebox.showinfo("导入结果", message)
            
            if success_count > 0:
                self.window.destroy()
            
        except Exception as e:
            self.logger.error(f"导入账户失败: {e}")
            messagebox.showerror("错误", f"导入账户失败: {e}")
    
    def save_config(self):
        """保存配置"""
        try:
            config = {
                'accounts': [],
                'settings': {
                    'max_concurrent': 10,
                    'enable_fast_mode': True
                }
            }
            
            for account_id, account_config in self.account_manager.accounts.items():
                config['accounts'].append({
                    'account_id': account_config.account_id,
                    'email': account_config.email,
                    'client_id': account_config.client_id,
                    'refresh_token': account_config.refresh_token,
                    'password': account_config.password,
                    'enabled': account_config.enabled,
                    'priority': account_config.priority,
                    'max_retries': account_config.max_retries
                })
            
            with open('multi_account_config.json', 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            self.logger.error(f"保存配置失败: {e}")
    
    def download_template(self):
        """下载模板文件"""
        try:
            format_type = self.format_var.get()
            
            if format_type == 'csv':
                filename = filedialog.asksaveasfilename(
                    title="保存CSV模板",
                    defaultextension=".csv",
                    filetypes=[("CSV文件", "*.csv")]
                )
                if filename:
                    self.create_csv_template(filename)
            
            elif format_type == 'json':
                filename = filedialog.asksaveasfilename(
                    title="保存JSON模板",
                    defaultextension=".json",
                    filetypes=[("JSON文件", "*.json")]
                )
                if filename:
                    self.create_json_template(filename)
            
            elif format_type == 'txt':
                filename = filedialog.asksaveasfilename(
                    title="保存TXT模板",
                    defaultextension=".txt",
                    filetypes=[("文本文件", "*.txt")]
                )
                if filename:
                    self.create_txt_template(filename)
                    
        except Exception as e:
            messagebox.showerror("错误", f"创建模板失败: {e}")
    
    def create_csv_template(self, filename: str):
        """创建CSV模板"""
        with open(filename, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['email', 'client_id', 'refresh_token', 'password'])
            writer.writerow([
                '<EMAIL>',
                '9e5f94bc-e8a4-4e73-b8be-63364c29d753',
                'your_refresh_token_here',
                'your_password_here'
            ])
        messagebox.showinfo("成功", f"CSV模板已保存到: {filename}")
    
    def create_json_template(self, filename: str):
        """创建JSON模板"""
        template = [
            {
                "email": "<EMAIL>",
                "client_id": "9e5f94bc-e8a4-4e73-b8be-63364c29d753",
                "refresh_token": "your_refresh_token_here",
                "password": "your_password_here"
            }
        ]
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(template, f, indent=2, ensure_ascii=False)
        messagebox.showinfo("成功", f"JSON模板已保存到: {filename}")
    
    def create_txt_template(self, filename: str):
        """创建TXT模板"""
        template = """<EMAIL>|9e5f94bc-e8a4-4e73-b8be-63364c29d753|your_refresh_token_here|your_password_here
<EMAIL>|9e5f94bc-e8a4-4e73-b8be-63364c29d753|another_refresh_token|another_password
"""
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(template)
        messagebox.showinfo("成功", f"TXT模板已保存到: {filename}")

def main():
    """测试批量导入功能"""
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    from core.multi_account_manager import MultiAccountManager
    manager = MultiAccountManager()
    
    importer = BatchAccountImporter(root, manager)
    root.mainloop()

if __name__ == "__main__":
    main()