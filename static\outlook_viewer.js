/**
 * Outlook风格邮件查看器JavaScript功能
 */

// 全局变量
let currentEmail = null;
let attachmentData = [];

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeViewer();
    setupEventListeners();
    loadEmailData();
});

/**
 * 初始化查看器
 */
function initializeViewer() {
    console.log('📧 Outlook风格邮件查看器已加载');
    
    // 检查是否支持必要的API
    if (!window.fetch) {
        console.warn('⚠️ 浏览器不支持fetch API');
    }
    
    // 设置主题
    applyTheme();
    
    // 初始化工具提示
    initializeTooltips();
}

/**
 * 设置事件监听器
 */
function setupEventListeners() {
    // 工具栏按钮事件
    setupToolbarEvents();
    
    // 附件相关事件
    setupAttachmentEvents();
    
    // 键盘快捷键
    setupKeyboardShortcuts();
    
    // 窗口大小变化
    window.addEventListener('resize', handleWindowResize);
}

/**
 * 设置工具栏事件
 */
function setupToolbarEvents() {
    // 回复按钮
    const replyBtn = document.querySelector('.btn-reply');
    if (replyBtn) {
        replyBtn.addEventListener('click', function() {
            handleReply();
        });
    }
    
    // 全部回复按钮
    const replyAllBtn = document.querySelector('.btn-reply-all');
    if (replyAllBtn) {
        replyAllBtn.addEventListener('click', function() {
            handleReplyAll();
        });
    }
    
    // 转发按钮
    const forwardBtn = document.querySelector('.btn-forward');
    if (forwardBtn) {
        forwardBtn.addEventListener('click', function() {
            handleForward();
        });
    }
    
    // 标记按钮
    const flagBtn = document.querySelector('.btn-flag');
    if (flagBtn) {
        flagBtn.addEventListener('click', function() {
            handleFlag();
        });
    }
    
    // 删除按钮
    const deleteBtn = document.querySelector('.btn-delete');
    if (deleteBtn) {
        deleteBtn.addEventListener('click', function() {
            handleDelete();
        });
    }
    
    // 更多操作按钮
    const moreBtn = document.querySelector('.btn-more');
    if (moreBtn) {
        moreBtn.addEventListener('click', function() {
            handleMoreActions();
        });
    }
}

/**
 * 设置附件事件
 */
function setupAttachmentEvents() {
    // 为所有下载按钮添加事件监听器
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('btn-download') || 
            e.target.closest('.btn-download')) {
            e.preventDefault();
            const button = e.target.closest('.btn-download');
            const attachmentItem = button.closest('.attachment-item');
            const index = attachmentItem.dataset.index;
            downloadAttachment(index);
        }
    });
}

/**
 * 设置键盘快捷键
 */
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl+R: 回复
        if (e.ctrlKey && e.key === 'r') {
            e.preventDefault();
            handleReply();
        }
        
        // Ctrl+Shift+R: 全部回复
        if (e.ctrlKey && e.shiftKey && e.key === 'R') {
            e.preventDefault();
            handleReplyAll();
        }
        
        // Ctrl+F: 转发
        if (e.ctrlKey && e.key === 'f') {
            e.preventDefault();
            handleForward();
        }
        
        // Delete: 删除
        if (e.key === 'Delete') {
            e.preventDefault();
            handleDelete();
        }
        
        // F: 标记
        if (e.key === 'f' && !e.ctrlKey) {
            e.preventDefault();
            handleFlag();
        }
    });
}

/**
 * 处理回复
 */
function handleReply() {
    console.log('📧 处理回复');
    showNotification('回复功能开发中...', 'info');
    
    // 这里可以集成实际的回复功能
    // 例如：打开新窗口或调用邮件编辑器
}

/**
 * 处理全部回复
 */
function handleReplyAll() {
    console.log('📧 处理全部回复');
    showNotification('全部回复功能开发中...', 'info');
}

/**
 * 处理转发
 */
function handleForward() {
    console.log('📧 处理转发');
    showNotification('转发功能开发中...', 'info');
}

/**
 * 处理标记
 */
function handleFlag() {
    console.log('🚩 处理标记');
    
    const flagBtn = document.querySelector('.btn-flag');
    const flagBadge = document.querySelector('.flag-badge');
    
    if (flagBadge) {
        // 取消标记
        flagBadge.remove();
        showNotification('已取消标记', 'success');
    } else {
        // 添加标记
        const statusBadges = document.querySelector('.status-badges');
        if (statusBadges) {
            const newFlag = document.createElement('span');
            newFlag.className = 'badge flag-badge';
            newFlag.title = '已标记';
            newFlag.textContent = '🚩';
            statusBadges.appendChild(newFlag);
            showNotification('已添加标记', 'success');
        }
    }
}

/**
 * 处理删除
 */
function handleDelete() {
    console.log('🗑️ 处理删除');
    
    if (confirm('确定要删除这封邮件吗？')) {
        showNotification('删除功能开发中...', 'info');
        // 这里可以集成实际的删除功能
    }
}

/**
 * 处理更多操作
 */
function handleMoreActions() {
    console.log('⋯ 处理更多操作');
    
    // 创建下拉菜单
    const menu = createDropdownMenu([
        { text: '标记为未读', action: () => markAsUnread() },
        { text: '移动到文件夹', action: () => moveToFolder() },
        { text: '创建规则', action: () => createRule() },
        { text: '查看源代码', action: () => viewSource() },
        { text: '打印', action: () => printEmail() }
    ]);
    
    // 显示菜单
    showDropdownMenu(menu, document.querySelector('.btn-more'));
}

/**
 * 下载附件
 */
function downloadAttachment(filename, index) {
    console.log(`📎 下载附件: ${filename} (索引: ${index})`);
    
    // 显示下载进度
    showNotification(`正在下载 ${filename}...`, 'info');
    
    // 模拟下载过程
    setTimeout(() => {
        showNotification(`${filename} 下载完成`, 'success');
    }, 1000);
    
    // 这里可以集成实际的下载功能
    // 例如：调用后端API下载附件
}

/**
 * 应用主题
 */
function applyTheme() {
    const theme = localStorage.getItem('outlook-theme') || 'light';
    document.body.className = `outlook-theme-${theme}`;
}

/**
 * 切换主题
 */
function toggleTheme() {
    const currentTheme = document.body.className.includes('dark') ? 'dark' : 'light';
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
    
    document.body.className = `outlook-theme-${newTheme}`;
    localStorage.setItem('outlook-theme', newTheme);
    
    showNotification(`已切换到${newTheme === 'dark' ? '深色' : '浅色'}主题`, 'success');
}

/**
 * 初始化工具提示
 */
function initializeTooltips() {
    const elementsWithTooltips = document.querySelectorAll('[title]');
    
    elementsWithTooltips.forEach(element => {
        element.addEventListener('mouseenter', showTooltip);
        element.addEventListener('mouseleave', hideTooltip);
    });
}

/**
 * 显示工具提示
 */
function showTooltip(e) {
    const title = e.target.getAttribute('title');
    if (!title) return;
    
    // 创建工具提示元素
    const tooltip = document.createElement('div');
    tooltip.className = 'custom-tooltip';
    tooltip.textContent = title;
    tooltip.style.cssText = `
        position: absolute;
        background: #323130;
        color: white;
        padding: 4px 8px;
        border-radius: 2px;
        font-size: 12px;
        z-index: 1000;
        pointer-events: none;
    `;
    
    document.body.appendChild(tooltip);
    
    // 定位工具提示
    const rect = e.target.getBoundingClientRect();
    tooltip.style.left = rect.left + 'px';
    tooltip.style.top = (rect.bottom + 5) + 'px';
    
    // 临时移除title属性
    e.target.setAttribute('data-original-title', title);
    e.target.removeAttribute('title');
}

/**
 * 隐藏工具提示
 */
function hideTooltip(e) {
    const tooltip = document.querySelector('.custom-tooltip');
    if (tooltip) {
        tooltip.remove();
    }
    
    // 恢复title属性
    const originalTitle = e.target.getAttribute('data-original-title');
    if (originalTitle) {
        e.target.setAttribute('title', originalTitle);
        e.target.removeAttribute('data-original-title');
    }
}

/**
 * 显示通知
 */
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // 样式
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 16px;
        border-radius: 4px;
        color: white;
        font-weight: 500;
        z-index: 1001;
        animation: slideIn 0.3s ease;
    `;
    
    // 根据类型设置背景色
    switch (type) {
        case 'success':
            notification.style.backgroundColor = '#107c10';
            break;
        case 'error':
            notification.style.backgroundColor = '#d13438';
            break;
        case 'warning':
            notification.style.backgroundColor = '#ff8c00';
            break;
        default:
            notification.style.backgroundColor = '#0078d4';
    }
    
    document.body.appendChild(notification);
    
    // 3秒后自动移除
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

/**
 * 创建下拉菜单
 */
function createDropdownMenu(items) {
    const menu = document.createElement('div');
    menu.className = 'dropdown-menu';
    menu.style.cssText = `
        position: absolute;
        background: white;
        border: 1px solid #edebe9;
        border-radius: 2px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        z-index: 1000;
        min-width: 150px;
    `;
    
    items.forEach(item => {
        const menuItem = document.createElement('div');
        menuItem.className = 'dropdown-item';
        menuItem.textContent = item.text;
        menuItem.style.cssText = `
            padding: 8px 12px;
            cursor: pointer;
            border-bottom: 1px solid #f3f2f1;
        `;
        
        menuItem.addEventListener('click', () => {
            item.action();
            menu.remove();
        });
        
        menuItem.addEventListener('mouseenter', () => {
            menuItem.style.backgroundColor = '#f3f2f1';
        });
        
        menuItem.addEventListener('mouseleave', () => {
            menuItem.style.backgroundColor = 'white';
        });
        
        menu.appendChild(menuItem);
    });
    
    return menu;
}

/**
 * 显示下拉菜单
 */
function showDropdownMenu(menu, trigger) {
    document.body.appendChild(menu);
    
    const rect = trigger.getBoundingClientRect();
    menu.style.left = rect.left + 'px';
    menu.style.top = (rect.bottom + 2) + 'px';
    
    // 点击其他地方关闭菜单
    const closeMenu = (e) => {
        if (!menu.contains(e.target)) {
            menu.remove();
            document.removeEventListener('click', closeMenu);
        }
    };
    
    setTimeout(() => {
        document.addEventListener('click', closeMenu);
    }, 0);
}

/**
 * 处理窗口大小变化
 */
function handleWindowResize() {
    // 移除可能存在的下拉菜单
    const dropdownMenus = document.querySelectorAll('.dropdown-menu');
    dropdownMenus.forEach(menu => menu.remove());
}

/**
 * 加载邮件数据
 */
function loadEmailData() {
    // 这里可以从URL参数或其他来源加载邮件数据
    console.log('📧 加载邮件数据');
}

/**
 * 标记为未读
 */
function markAsUnread() {
    console.log('📧 标记为未读');
    showNotification('已标记为未读', 'success');
}

/**
 * 移动到文件夹
 */
function moveToFolder() {
    console.log('📁 移动到文件夹');
    showNotification('移动功能开发中...', 'info');
}

/**
 * 创建规则
 */
function createRule() {
    console.log('📋 创建规则');
    showNotification('规则创建功能开发中...', 'info');
}

/**
 * 查看源代码
 */
function viewSource() {
    console.log('🔍 查看源代码');
    showNotification('源代码查看功能开发中...', 'info');
}

/**
 * 打印邮件
 */
function printEmail() {
    console.log('🖨️ 打印邮件');
    window.print();
}

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);
